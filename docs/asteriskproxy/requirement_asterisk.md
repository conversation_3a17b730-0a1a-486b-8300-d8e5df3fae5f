## 設備名稱：Asterisk PBX

### 注意事項

1. [使用.NET](http://使用.NET) 9開發。
2. 符合企業級等級的穩定性及運行性能。
3. 使用Ngp.Communication.AsteriskProxy專案。
4. 使用Minimal API不要用Controller。
5. 在程式中加入簡易的英文註解。
6. 選擇Best Practice。
7. 源能夠正確回收，正常運作或異常時，連線埠均能正確關閉。
8. 提供FluentAPI接口。
9. 可以Graceful Exit。
10. 高平行化設計，避免同步IO阻塞。
11. 完善Thread-Safe功能，優先使用SemaphoreSlim而不是lock，並且避免鎖死的狀況產生。
12. 使用高性能的連線管理機制。
13. 不要使用Console.WriteLine，請使用ILogger，並合理配置不同的Log等級
14. 系統中使用的數值型別使用要精準，例：應該是ushort就不要定義成int。
15. 所有的事件委派請採用EventHandler<TEventArgs>。
16. 重要：事件委派邏輯以及Fluent API接口，請參照ModbusTcpMaster專案，做類似的設計。

### 自動輪詢與事件邏輯

1. 透過Fluent API提供需要讀取狀態的分機清單。
2. 透過Fluent API提供使用的帳號密碼，以及終端網址。
3. 需加入一個選項，可選擇是否驗證Https SSL憑證。
4. 運行時應可以隨時取得各分機號碼的「連線狀態」以及「通話狀態」。
5. 背景不停輪詢對應分機的狀態內容，如果有更新，則觸發數值更新的委派事件。
6. 如果沒有產生數值變化的輪詢，則不需要LOG，避免過多LOG。
7. 如果連線狀態變更，則觸發連線狀態更新的委派事件。

### 通訊規格：

1. Asterisk Rest API版本22(可透過context7查詢)
2. 通訊規格應在FluentAPI可指定，可提供未來更新的版本時也可延伸。

---