using System.Text.Json.Serialization;
using System.Text.RegularExpressions;

namespace TwentyFourDioGateway.Models
{
    /// <summary>
    /// Asterisk 分機狀態模型
    /// </summary>
    public class AsteriskExtensionStatus
    {
        /// <summary>
        /// 分機號碼
        /// </summary>
        public string Extension { get; set; } = string.Empty;
        
        /// <summary>
        /// 分機是否在線上
        /// </summary>
        public bool IsOnline { get; set; }
        
        /// <summary>
        /// 分機是否正在通話中
        /// </summary>
        public bool IsInCall { get; set; }
        
        /// <summary>
        /// 狀態描述
        /// </summary>
        public string StatusDescription { get; set; } = string.Empty;
        
        /// <summary>
        /// 分機對應的端點資訊
        /// </summary>
        public List<AsteriskEndpoint>? Endpoints { get; set; }
    }
    
    /// <summary>
    /// Asterisk 端點資訊 (來自 ARI API 響應)
    /// </summary>
    public class AsteriskEndpoint
    {
        [JsonPropertyName("resource")]
        public string? Resource { get; set; }
        
        [JsonPropertyName("id")]
        public string? Id { get; set; }
        
        [JsonPropertyName("technology")]
        public string? Technology { get; set; }
        
        [JsonPropertyName("state")]
        public string? State { get; set; }
        
        [JsonPropertyName("channel_ids")]
        public List<string>? ChannelIds { get; set; }
        
        /// <summary>
        /// 從 Resource 或 Id 中取得分機號碼
        /// </summary>
        public string? GetExtensionNumber()
        {
            // 嘗試從 Resource 中提取 (例如: "PJSIP/1001")
            if (!string.IsNullOrEmpty(Resource))
            {
                var match = Regex.Match(Resource, @"(\w+)/(\d+)");
                if (match.Success && match.Groups.Count > 2)
                {
                    return match.Groups[2].Value;
                }
            }
            
            // 嘗試從 Id 中提取 (可能格式會根據 Asterisk 設定不同)
            if (!string.IsNullOrEmpty(Id))
            {
                var match = Regex.Match(Id, @"(\d{3,})");
                if (match.Success)
                {
                    return match.Value;
                }
            }
            
            return null;
        }
    }
    
    /// <summary>
    /// Asterisk 通道資訊 (來自 ARI API 響應)
    /// </summary>
    public class AsteriskChannel
    {
        [JsonPropertyName("id")]
        public string? Id { get; set; }
        
        [JsonPropertyName("name")]
        public string? Name { get; set; }
        
        [JsonPropertyName("state")]
        public string? State { get; set; }
        
        [JsonPropertyName("caller")]
        public AsteriskCallerInfo? Caller { get; set; }
        
        [JsonPropertyName("connected")]
        public AsteriskCallerInfo? Connected { get; set; }
        
        /// <summary>
        /// 通道創建時間，使用可為空類型處理日期時間格式錯誤
        /// </summary>
        [JsonPropertyName("creationtime")]
        public DateTime? CreationTime { get; set; }
        
        /// <summary>
        /// 從通道名稱中取得分機號碼
        /// </summary>
        public string? GetExtensionFromName()
        {
            if (string.IsNullOrEmpty(Name))
                return null;
                
            // 常見的通道名稱模式，如 "PJSIP/1001-00000001"
            var match = Regex.Match(Name, @"(\w+)/(\d+)-");
            if (match.Success && match.Groups.Count > 2)
            {
                return match.Groups[2].Value;
            }
            
            // 嘗試其他可能的模式，例如 "SIP/1001"
            match = Regex.Match(Name, @"(\w+)/(\d+)");
            if (match.Success && match.Groups.Count > 2)
            {
                return match.Groups[2].Value;
            }
            
            return null;
        }
        
        /// <summary>
        /// 判斷通道是否與指定分機相關
        /// </summary>
        public bool IsRelatedToExtension(string extension)
        {
            // 檢查通道名稱
            if (!string.IsNullOrEmpty(Name) && Name.Contains(extension))
                return true;
                
            // 檢查主叫方
            if (Caller != null && !string.IsNullOrEmpty(Caller.Number) && Caller.Number == extension)
                return true;
                
            // 檢查被叫方
            if (Connected != null && !string.IsNullOrEmpty(Connected.Number) && Connected.Number == extension)
                return true;
                
            // 使用正則表達式從通道名稱中提取分機號碼
            var extractedExtension = GetExtensionFromName();
            if (!string.IsNullOrEmpty(extractedExtension) && extractedExtension == extension)
                return true;
                
            return false;
        }
    }
    
    /// <summary>
    /// Asterisk 來電者資訊 (來自 ARI API 響應)
    /// </summary>
    public class AsteriskCallerInfo
    {
        [JsonPropertyName("name")]
        public string? Name { get; set; }
        
        [JsonPropertyName("number")]
        public string? Number { get; set; }
    }
} 