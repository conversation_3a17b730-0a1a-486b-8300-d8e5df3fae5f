using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading;
using System.Threading.Tasks;
using TwentyFourDioGateway.Models;

namespace TwentyFourDioGateway.Services
{
    /// <summary>
    /// Asterisk 分機狀態監控服務
    /// 負責透過 Asterisk REST Interface (ARI) API 取得分機狀態並更新到 Modbus 位址
    /// </summary>
    public class AsteriskService : BackgroundService
    {
        private readonly ILogger<AsteriskService> _logger;
        private readonly IOptions<Setting> _settings;
        private readonly IOptions<DeviceMapping> _deviceMapping;
        private readonly GatewayService _gatewayService;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly Dictionary<string, AsteriskExtensionStatus> _extensionStatusCache = new();
        private const string HttpClientName = "AsteriskClient";
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private Timer? _timer;

        /// <summary>
        /// 建構子
        /// </summary>
        public AsteriskService(
            ILogger<AsteriskService> logger,
            IOptions<Setting> settings,
            IOptions<DeviceMapping> deviceMapping,
            GatewayService gatewayService,
            IHttpClientFactory httpClientFactory,
            IServiceScopeFactory serviceScopeFactory)
        {
            _logger = logger;
            _settings = settings;
            _deviceMapping = deviceMapping;
            _gatewayService = gatewayService;
            _httpClientFactory = httpClientFactory;
            _serviceScopeFactory = serviceScopeFactory;
        }

        /// <summary>
        /// 背景服務執行方法
        /// </summary>
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            try
            {
                if (!_settings.Value.EnableAsteriskMonitoring)
                {
                    _logger.LogInformation("Asterisk 分機狀態監控功能未啟用，背景服務不會執行。");
                    return;
                }

                _logger.LogInformation("Asterisk 分機狀態監控服務已啟動");

                while (!stoppingToken.IsCancellationRequested)
                {
                    try
                    {
                        await UpdateExtensionStatusAsync(stoppingToken);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "更新 Asterisk 分機狀態時發生錯誤");
                    }

                    await Task.Delay(TimeSpan.FromMilliseconds(_settings.Value.AsteriskUpdateIntervalMilliseconds), stoppingToken);
                }
            }
            catch (TaskCanceledException)
            {
                // 正常取消程序，不需處理
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Asterisk 監控服務執行時發生未處理的例外");
            }
            finally
            {
                _logger.LogInformation("Asterisk 分機狀態監控服務已停止");
            }
        }

        /// <summary>
        /// 更新所有分機狀態
        /// </summary>
        private async Task UpdateExtensionStatusAsync(CancellationToken cancellationToken)
        {
            // 若沒有設定分機映射，則跳過處理
            if (_deviceMapping.Value.AsteriskExtensions == null || _deviceMapping.Value.AsteriskExtensions.Count == 0)
            {
                _logger.LogWarning("未設定 Asterisk 分機映射，無法進行狀態更新");
                return;
            }

            // 取得所有端點
            var endpoints = await GetAllEndpointsAsync(cancellationToken);
            if (endpoints == null)
            {
                _logger.LogWarning("無法取得 Asterisk 端點資訊");
                return;
            }

            // 記錄取得的端點資訊，以便診斷
            _logger.LogDebug("Asterisk 端點資訊: {EndpointsCount} 個端點", endpoints.Count);
            foreach (var endpoint in endpoints)
            {
                _logger.LogDebug("端點: Id={Id}, Resource={Resource}, Technology={Technology}, State={State}, ChannelIds={ChannelIds}",
                    endpoint.Id,
                    endpoint.Resource,
                    endpoint.Technology,
                    endpoint.State,
                    endpoint.ChannelIds != null ? string.Join(",", endpoint.ChannelIds) : "無");
            }

            // 取得所有通道
            var channels = await GetAllChannelsAsync(cancellationToken);
            if (channels == null)
            {
                _logger.LogWarning("無法取得 Asterisk 通道資訊");
                return;
            }

            // 記錄取得的通道資訊，以便診斷
            _logger.LogDebug("Asterisk 通道資訊: {ChannelsCount} 個通道", channels.Count);
            foreach (var channel in channels)
            {
                _logger.LogDebug("通道: Id={Id}, Name={Name}, State={State}, Caller={Caller}, Connected={Connected}",
                    channel.Id,
                    channel.Name,
                    channel.State,
                    channel.Caller?.Number,
                    channel.Connected?.Number);
            }

            // 更新分機狀態
            foreach (var extension in _deviceMapping.Value.AsteriskExtensions)
            {
                // 處理單一分機設定方式 (向下相容)
                if (!string.IsNullOrEmpty(extension.Extension) && extension.OnlineStatusModbusAddress.HasValue)
                {
                    await UpdateSingleExtensionStatusAsync(extension.Extension, 
                                                        extension.OnlineStatusModbusAddress.Value, 
                                                        extension.CallStatusModbusAddress ?? 0, 
                                                        endpoints, channels);
                    continue;
                }
                
                // 處理分機區間設定
                if (!string.IsNullOrEmpty(extension.StartExtensionNumber) && 
                    !string.IsNullOrEmpty(extension.EndExtensionNumber))
                {
                    // 確保起始和結束分機號都是數字
                    if (int.TryParse(extension.StartExtensionNumber, out int startExt) && 
                        int.TryParse(extension.EndExtensionNumber, out int endExt))
                    {
                        // 遍歷所有分機區間
                        for (int currentExt = startExt; currentExt <= endExt; currentExt++)
                        {
                            string extensionNumber = currentExt.ToString();
                            
                            // 計算對應的 Modbus 位址偏移量
                            int offset = currentExt - startExt;
                            ushort onlineStatusAddress = (ushort)(extension.StartOnlineStatusModbusAddress + offset);
                            ushort callStatusAddress = (ushort)(extension.StartCallStatusModbusAddress + offset);
                            
                            await UpdateSingleExtensionStatusAsync(extensionNumber, 
                                                                onlineStatusAddress, 
                                                                callStatusAddress, 
                                                                endpoints, channels);
                        }
                    }
                    else
                    {
                        _logger.LogWarning("【Asterisk】分機設定格式錯誤: StartExtensionNumber={Start}, EndExtensionNumber={End}，" +
                                        "無法解析為整數",
                                        extension.StartExtensionNumber, extension.EndExtensionNumber);
                    }
                }
            }
        }
        
        /// <summary>
        /// 更新單一分機狀態
        /// </summary>
        private async Task UpdateSingleExtensionStatusAsync(
            string extensionNumber, 
            ushort onlineStatusAddress, 
            ushort callStatusAddress, 
            List<AsteriskEndpoint> endpoints, 
            List<AsteriskChannel> channels)
        {
            var extensionStatus = GetExtensionStatus(extensionNumber, endpoints, channels);
            
            _logger.LogDebug("分機 {Extension} 匹配到 {EndpointsCount} 個端點",
                extensionNumber, extensionStatus.Endpoints?.Count ?? 0);
            
            try
            {
                // 更新在線狀態到 Modbus
                if (onlineStatusAddress > 0)
                {
                    _logger.LogDebug("【Asterisk】正在更新分機 {Extension} 在線狀態 {Status} 到 Modbus 位址 {Address}",
                        extensionNumber, extensionStatus.IsOnline, onlineStatusAddress);
                        
                    await _gatewayService.UpdateStatusAsync(TwentyFourDioGateway.Models.ModbusAddressType.DiscreteInput, 
                                                         onlineStatusAddress, extensionStatus.IsOnline);
                    
                    // 驗證更新是否成功
                    var dataStore = _gatewayService.dataStore;
                    var actualStatus = dataStore.InputDiscretes[onlineStatusAddress];
                    
                    _logger.LogDebug("【Asterisk】分機 {Extension} 在線狀態已更新為 {Status}，驗證 Modbus 內存狀態: {ActualStatus}",
                        extensionNumber, extensionStatus.IsOnline, actualStatus);
                }
                
                // 更新通話狀態到 Modbus
                if (callStatusAddress > 0)
                {
                    _logger.LogDebug("【Asterisk】正在更新分機 {Extension} 通話狀態 {Status} 到 Modbus 位址 {Address}",
                        extensionNumber, extensionStatus.IsInCall, callStatusAddress);
                        
                    await _gatewayService.UpdateStatusAsync(TwentyFourDioGateway.Models.ModbusAddressType.DiscreteInput, 
                                                         callStatusAddress, extensionStatus.IsInCall);
                    
                    // 驗證更新是否成功
                    var dataStore = _gatewayService.dataStore;
                    var actualStatus = dataStore.InputDiscretes[callStatusAddress];
                    
                    _logger.LogDebug("【Asterisk】分機 {Extension} 通話狀態已更新為 {Status}，驗證 Modbus 內存狀態: {ActualStatus}",
                        extensionNumber, extensionStatus.IsInCall, actualStatus);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "【Asterisk】更新分機 {Extension} 狀態至 Modbus 時發生錯誤", extensionNumber);
            }
            
            // 記錄狀態變更
            var cacheKey = extensionNumber;
            if (!_extensionStatusCache.TryGetValue(cacheKey, out var cachedStatus) || 
                cachedStatus.IsOnline != extensionStatus.IsOnline || 
                cachedStatus.IsInCall != extensionStatus.IsInCall)
            {
                _logger.LogInformation("【Asterisk】分機 {Extension} 狀態更新：在線={Online}，通話中={InCall}，描述={Description}",
                    extensionNumber, extensionStatus.IsOnline, extensionStatus.IsInCall, extensionStatus.StatusDescription);
                
                _extensionStatusCache[cacheKey] = extensionStatus;
            }
        }

        /// <summary>
        /// 取得分機狀態
        /// </summary>
        private AsteriskExtensionStatus GetExtensionStatus(
            string extension, 
            List<AsteriskEndpoint> endpoints, 
            List<AsteriskChannel> channels)
        {
            var status = new AsteriskExtensionStatus
            {
                Extension = extension,
                IsOnline = false,
                IsInCall = false,
                StatusDescription = "離線",
                Endpoints = new List<AsteriskEndpoint>()
            };

            // 嘗試多種方式匹配端點與分機
            var matchingEndpoints = new List<AsteriskEndpoint>();
            
            // 方法1: 根據 Id 包含分機號
            var byId = endpoints.Where(e => e.Id != null && e.Id.Contains(extension)).ToList();
            if (byId.Any())
            {
                _logger.LogDebug("分機 {Extension} 通過 Id 匹配到 {Count} 個端點", extension, byId.Count);
                matchingEndpoints.AddRange(byId);
            }
            
            // 方法2: 根據 Resource 包含分機號
            var byResource = endpoints.Where(e => e.Resource != null && e.Resource.Contains(extension) && !matchingEndpoints.Contains(e)).ToList();
            if (byResource.Any())
            {
                _logger.LogDebug("分機 {Extension} 通過 Resource 匹配到 {Count} 個端點", extension, byResource.Count);
                matchingEndpoints.AddRange(byResource);
            }
            
            // 方法3: 使用模型中新增的 GetExtensionNumber 方法
            var byExtract = endpoints.Where(e => 
                e.GetExtensionNumber() == extension && 
                !matchingEndpoints.Contains(e)).ToList();
            if (byExtract.Any())
            {
                _logger.LogDebug("分機 {Extension} 通過提取號碼匹配到 {Count} 個端點", extension, byExtract.Count);
                matchingEndpoints.AddRange(byExtract);
            }

            // 如果仍未找到匹配端點，嘗試遍歷檢查所有端點的資訊
            if (!matchingEndpoints.Any())
            {
                foreach (var endpoint in endpoints)
                {
                    _logger.LogDebug("檢查端點: {Id}, {Resource}, {Technology}", 
                        endpoint.Id, endpoint.Resource, endpoint.Technology);
                }
            }

            if (matchingEndpoints.Any())
            {
                status.Endpoints = matchingEndpoints;
                
                // 檢查是否有任何端點在線上 (考慮多種可能的狀態值)
                var validOnlineStates = new[] { "online", "available", "registered", "reachable" };
                var isOnline = matchingEndpoints.Any(e => e.State != null && validOnlineStates.Contains(e.State.ToLower()));
                status.IsOnline = isOnline;
                
                // 記錄識別到的端點狀態
                foreach (var ep in matchingEndpoints)
                {
                    _logger.LogDebug("分機 {Extension} 對應端點 {Id} 狀態: {State}", 
                        extension, ep.Id, ep.State);
                }
                
                if (isOnline)
                {
                    status.StatusDescription = "在線";
                    
                    // 檢查是否有任何端點的通道正在通話中
                    bool isInCall = false;
                    
                    // 首先檢查端點對應的通道
                    foreach (var endpoint in matchingEndpoints)
                    {
                        if (endpoint.ChannelIds != null && endpoint.ChannelIds.Any())
                        {
                            foreach (var channelId in endpoint.ChannelIds)
                            {
                                var channel = channels.FirstOrDefault(c => c.Id == channelId);
                                // 考慮多種可能的通話狀態
                                var validCallStates = new[] { "up", "ringing", "busy", "dialing", "ring", "progress" };
                                if (channel != null && channel.State != null && 
                                    !channel.State.Equals("down", StringComparison.OrdinalIgnoreCase) &&
                                    validCallStates.Contains(channel.State.ToLower()))
                                {
                                    isInCall = true;
                                    status.StatusDescription = $"通話中 ({channel.State})";
                                    _logger.LogDebug("分機 {Extension} 通過通道 {ChannelId} 狀態 {State} 判定為通話中", 
                                        extension, channelId, channel.State);
                                    break;
                                }
                            }
                            
                            if (isInCall) break;
                        }
                    }
                    
                    // 如果未找到通話中的通道，嘗試使用通道模型的擴展方法查找相關通道
                    if (!isInCall)
                    {
                        var relatedChannels = channels.Where(c => c.IsRelatedToExtension(extension)).ToList();
                        if (relatedChannels.Any())
                        {
                            _logger.LogDebug("分機 {Extension} 通過通道關聯找到 {Count} 個相關通道", extension, relatedChannels.Count);
                            
                            // 檢查這些通道是否有非 down 狀態的
                            var activeChannel = relatedChannels.FirstOrDefault(c => 
                                c.State != null && !c.State.Equals("down", StringComparison.OrdinalIgnoreCase));
                            
                            if (activeChannel != null)
                            {
                                isInCall = true;
                                status.StatusDescription = $"通話中 ({activeChannel.State})";
                                _logger.LogDebug("分機 {Extension} 通過相關通道 {Name} 狀態 {State} 判定為通話中", 
                                    extension, activeChannel.Name, activeChannel.State);
                            }
                        }
                    }
                    
                    status.IsInCall = isInCall;
                }
            }
            else
            {
                // _logger.LogWarning("分機 {Extension} 未找到對應的端點", extension);
                
                // 如果沒有找到端點，則嘗試直接通過通道判斷狀態
                var relatedChannels = channels.Where(c => c.IsRelatedToExtension(extension)).ToList();
                if (relatedChannels.Any())
                {
                    _logger.LogDebug("分機 {Extension} 未找到端點但找到 {Count} 個相關通道", extension, relatedChannels.Count);
                    
                    // 有相關通道表示分機在線
                    status.IsOnline = true;
                    status.StatusDescription = "僅通過通道識別";
                    
                    // 檢查是否有活動通道
                    var activeChannel = relatedChannels.FirstOrDefault(c => 
                        c.State != null && !c.State.Equals("down", StringComparison.OrdinalIgnoreCase));
                    
                    if (activeChannel != null)
                    {
                        status.IsInCall = true;
                        status.StatusDescription = $"通話中 ({activeChannel.State})";
                        _logger.LogDebug("分機 {Extension} 通過相關通道 {Name} 狀態 {State} 判定為通話中", 
                            extension, activeChannel.Name, activeChannel.State);
                    }
                }
            }
            
            return status;
        }

        /// <summary>
        /// 從 Asterisk REST Interface 取得所有端點資訊
        /// </summary>
        private async Task<List<AsteriskEndpoint>?> GetAllEndpointsAsync(CancellationToken cancellationToken)
        {
            return await CallAsteriskApiAsync<List<AsteriskEndpoint>>("endpoints", cancellationToken);
        }

        /// <summary>
        /// 從 Asterisk REST Interface 取得所有通道資訊
        /// </summary>
        private async Task<List<AsteriskChannel>?> GetAllChannelsAsync(CancellationToken cancellationToken)
        {
            return await CallAsteriskApiAsync<List<AsteriskChannel>>("channels", cancellationToken);
        }

        /// <summary>
        /// 呼叫 Asterisk REST Interface API
        /// </summary>
        private async Task<T?> CallAsteriskApiAsync<T>(string endpoint, CancellationToken cancellationToken)
        {
            try
            {
                // 使用命名的HttpClient
                var client = _httpClientFactory.CreateClient(HttpClientName);
                
                var apiUrl = _settings.Value.AsteriskApiUrl;
                
                if (string.IsNullOrEmpty(apiUrl))
                {
                    _logger.LogError("Asterisk API URL 未設定");
                    return default;
                }
                
                // 確保 URL 正確格式
                if (!apiUrl.EndsWith("/"))
                {
                    apiUrl += "/";
                }
                
                var requestUrl = $"{apiUrl}{endpoint}";
                _logger.LogDebug("嘗試連接 Asterisk API: {Url}", requestUrl);
                
                // 設定基本認證
                var authValue = Convert.ToBase64String(Encoding.ASCII.GetBytes(
                    $"{_settings.Value.AsteriskApiUsername}:{_settings.Value.AsteriskApiPassword}"));
                
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", authValue);
                
                using var request = new HttpRequestMessage(HttpMethod.Get, requestUrl);
                using var response = await client.SendAsync(request, cancellationToken);
                
                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogError("Asterisk API 請求失敗: {StatusCode} {Reason}", 
                        (int)response.StatusCode, response.ReasonPhrase);
                    return default;
                }
                
                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogTrace("Asterisk API 回應: {Content}", content);
                
                if (string.IsNullOrEmpty(content))
                {
                    _logger.LogError("Asterisk API 回應為空");
                    return default;
                }
                
                // 使用自訂的 JSON 序列化選項
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };
                
                // 添加自訂的 DateTime 轉換器，解決 creationtime 等欄位解析問題
                options.Converters.Add(new AsteriskDateTimeConverter());
                options.Converters.Add(new DateTimeConverterUsingDateTimeParse());
                
                return JsonSerializer.Deserialize<T>(content, options);
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "呼叫 Asterisk API 時發生 HTTP 請求錯誤: {Message}", ex.Message);
                return default;
            }
            catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException || cancellationToken.IsCancellationRequested)
            {
                _logger.LogWarning(ex, "呼叫 Asterisk API 超時或取消");
                return default;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "呼叫 Asterisk API 時發生未預期錯誤: {Message}", ex.Message);
                return default;
            }
        }
    }
    
    /// <summary>
    /// 自訂的 DateTime 轉換器，用於處理 Asterisk 返回的時間格式
    /// </summary>
    public class AsteriskDateTimeConverter : JsonConverter<DateTime>
    {
        public override DateTime Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            string? dateTimeString = reader.GetString();
            if (string.IsNullOrEmpty(dateTimeString))
            {
                return DateTime.MinValue;
            }
            
            // 嘗試多種可能的格式解析
            if (DateTime.TryParse(dateTimeString, out DateTime result))
            {
                return result;
            }
            
            // ISO 8601 格式
            if (DateTime.TryParseExact(dateTimeString, "yyyy-MM-ddTHH:mm:ss.fffZ", null, 
                System.Globalization.DateTimeStyles.AdjustToUniversal, out result))
            {
                return result;
            }
            
            // Asterisk 特定格式 (根據實際情況調整)
            string[] formats = new string[] 
            {
                "yyyy-MM-dd HH:mm:ss", 
                "yyyy-MM-ddTHH:mm:ss", 
                "yyyy-MM-dd HH:mm:ss.fff",
                "ddd MMM dd yyyy HH:mm:ss",
                "ddd MMM dd HH:mm:ss yyyy",
                "yyyy-MM-ddTHH:mm:sszzz"
            };
            
            if (DateTime.TryParseExact(dateTimeString, formats, null, 
                System.Globalization.DateTimeStyles.None, out result))
            {
                return result;
            }
            
            // 如果無法解析，記錄收到的格式並返回最小值
            Console.WriteLine($"無法解析日期時間格式: {dateTimeString}");
            return DateTime.MinValue;
        }
        
        public override void Write(Utf8JsonWriter writer, DateTime value, JsonSerializerOptions options)
        {
            writer.WriteStringValue(value.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"));
        }
    }

    /// <summary>
    /// 通用的 DateTime 轉換器，處理各種格式的日期時間字串
    /// </summary>
    public class DateTimeConverterUsingDateTimeParse : JsonConverter<DateTime?>
    {
        public override DateTime? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            if (reader.TokenType == JsonTokenType.Null)
            {
                return null;
            }
            
            try 
            {
                string? dateTimeString = reader.GetString();
                if (string.IsNullOrEmpty(dateTimeString))
                {
                    return null;
                }
                
                // 使用基本的 DateTime.Parse，它會嘗試自動識別格式
                if (DateTime.TryParse(dateTimeString, out DateTime result))
                {
                    return result;
                }
                
                // 如果無法解析，記錄詳細信息並返回 null
                Console.WriteLine($"無法解析日期時間格式: {dateTimeString}");
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"解析日期時間時發生錯誤: {ex.Message}");
                return null;
            }
        }
        
        public override void Write(Utf8JsonWriter writer, DateTime? value, JsonSerializerOptions options)
        {
            if (value.HasValue)
            {
                writer.WriteStringValue(value.Value.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"));
            }
            else
            {
                writer.WriteNullValue();
            }
        }
    }
} 