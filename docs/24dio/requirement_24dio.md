## 設備名稱：24Dio

### 注意事項

1. [使用.NET](http://使用.NET) 9開發。
2. 符合企業級等級的穩定性及運行性能。
3. 使用Ngp.Communication.TwentyFourDioPoller專案。
4. 使用Minimal API不要用Controller。
5. 在程式中加入簡易的英文註解。
6. 選擇Best Practice。
7. 源能夠正確回收，正常運作或異常時，連線埠均能正確關閉。
8. 提供FluentAPI接口。
9. 可以Graceful Exit。
10. 高平行化設計，避免同步IO阻塞。
11. 完善Thread-Safe功能，優先使用SemaphoreSlim而不是lock，並且主機避免死鎖。
12. 使用高性能的連線管理機制。
13. 不要使用Console.WriteLine，請使用ILogger，並合理配置不同的Log等級
14. 系統中使用的數值型別使用要精準，例：應該是ushort就不要定義成int。
15. 所有的事件委派請採用EventHandler<TEventArgs>。
16. 重要：事件委派邏輯以及Fluent API接口，請參照ModbusTcpMaster專案，做類似的設計。

### 自動輪詢與事件邏輯

1. 透過Fluent API提供需要讀取狀態的設備清單。
2. 運行時應可以隨時取得連線狀態。
3. 背景不停輪詢對應的設備的DI/DO內容，如果有更新，則觸發數值更新的委派事件。
4. 如果沒有產生數值變化的輪詢，則不需要LOG，避免過多LOG。
5. 暫存器數值更新，請依照設定的數值型別以及Endian發送委派事件。
6. 如果連線狀態變更，則觸發連線狀態更新的委派事件。
7. 如果遠端伺服器回應錯誤封包如長度不符，或包含錯誤碼、CRC錯誤等，則觸發發生錯誤的委派事件。
8. 如果滿足上一點提到的錯誤內容，則判定為連線異常，將強制切斷連線連線。
9. 如果連線中斷，下一次恢復連線時，應該繼續接續往下輪詢，而不是從頭開始。
10. 如果接收到了寫入指令，應該完成當下指令後，優先發送寫入指令，發送後再繼續輪詢。
11. 斷線判定：
    1. 一定時間內沒有回傳DI或DO的狀態，預設為5秒。
    2. 回傳的內容不是正確的DI或DO狀態時，應紀錄為異常封包並捨棄，如果單位時間內產生太多異常封包則判定為斷線。
    3. 產生任何Socket或其它連線相關異常。
12. 上述情形，則應強制切斷TCP連線後，積極嘗試重新連線，並同時避免Socket用盡的問題。
13. 輪詢與連線管理應能正常協作，勿互相干擾。
14. 輪詢與收取回應，邏輯應分開，因為如果設備狀態有變更，會主動回傳DI或DO的狀態，如果採一問一答的方式，會容易產生問題。

### 通訊規格：

1. 支援DI與DO點，並整合TCP/IP協議的終端。
2. 支援以下命令格式(ASCII)：
    - **@PIN DO 10101010**：設置8個DO點的狀態為10101010（依序設定）。
        - 一次可以將所有的DO點位設定成指定的狀態，此指令很少用得到。
    - **@SET DO 10101010**：將8個DO中的第1、3、5、7點設為1。
        - 將特定的DO點位設定成1的狀態，如果傳送0的點位，則不會被變動。
    - **@CLR DO 10101010**：將8個DO中的第1、3、5、7點設為0。
        - 將特定的DO點位設定成0的狀態，如果傳送1的點位，則不會被變動。
    - **@GET DI**：傳送@GET DI之後，會回傳所有的DI狀態，例如 `@DI 101010101010101010101010` 表示24點DI狀態。
    - **@GET DO**：傳送@GET DO之後，會回傳所有DO狀態，例如 `@DO 10101010` 表示8點DO狀態。
    - 以上所有指令，結尾皆包含一個空白字元及CR (20 0d)。
    - 以上所有指令的回覆，結尾皆包含一個CR(0d)。
3. DI或DO狀態變更時，設備會自動推送更新狀態。
4. 預設TCP端口為5801。
5. 針對指定點位需提供除彈跳功能，避免實體按鈕產生的彈跳現象。
6. 需能正確處理被分段的TCP封包。

---