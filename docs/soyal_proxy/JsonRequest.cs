﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace SoyalGateway
{
    internal class JsonRequest
    {
        [JsonPropertyName("l_user")]
        public string User { get; set; } = "login user";
        [JsonPropertyName("cmd_array")]
        public List<JsonCommand> Commands { get; set; } = new List<JsonCommand>();
    }
    internal class JsonCommand
    {
        [JsonPropertyName("c_cmd")]
        public int CommandPrefix { get; set; } = 2000;
        [JsonPropertyName("Area")]
        public int Area { get; set; } = 0;
        [JsonPropertyName("Node")]
        public int Node { get; set; } = 1;
        [JsonIgnore]
        public JsonCommandType Command { get; set; } = JsonCommandType.QueryStatus;

        [JsonPropertyName("Hex")]
        public string Hex => Command switch
        {
            JsonCommandType.QueryStatus => "0x2100",
            JsonCommandType.TurnRelayOn => "0x2184",
            _ => throw new NotImplementedException()
        };
    }
    public enum JsonCommandType
    {
        QueryStatus,
        TurnRelayOn
    }
}
