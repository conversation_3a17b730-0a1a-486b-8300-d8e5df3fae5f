﻿// See https://aka.ms/new-console-template for more information
using Microsoft.Extensions.Configuration;
using Modbus.Data;
using SoyalGateway;
using System;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Text.Json;


var builder = new ConfigurationBuilder()
     .SetBasePath(Directory.GetCurrentDirectory())
    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
var config = builder.Build();
//Setting x = config.GetSection("Setting").Get<Setting>();
//var x = JsonSerializer.Deserialize<Setting>(config.GetSection("Setting").Value);

//config["Setting"];

DataStore dataStore = DataStoreFactory.CreateDefaultDataStore(ushort.MaxValue, ushort.MaxValue, 0, 0);

Setting setting = config.GetSection("Setting").Get<Setting>();

var allNodes = setting.Areas.SelectMany(area =>
Enumerable.Range(area.StartNode, 1 + area.EndNode - area.StartNode)
    .Select(node =>
        new DoorCardReader()
        {
            Area = (byte)area.Id,
            NodeId = (byte)node
        })).ToList();

//var allNodes = new List<DoorCardReader>();
//foreach (var area in setting.Areas)
//{
//    allNOdes
//}
//var allNodes = Enumerable.Range(0, 16)
//    .SelectMany(area => Enumerable.Range(1, 254)
//        .Select(node =>
//            new DoorCardReader()
//            {
//                Area = (byte)area,
//                NodeId = (byte)node
//            }))
//    .ToList();
Console.WriteLine(JsonSerializer.Serialize(allNodes));
//allNodes.Add(new DoorCardReader()
//{
//    Area = 7,
//    NodeId = 35
//});


var requests = allNodes.Select(node =>
    new ModbusRequest()
    {
        Address = node.EnumDoorCardReader == EnumDoorCardReader.Enterprise ? (ushort)(node.Area * 4096 + 256) : (ushort)(node.Area * 4096),
        SlaveId = node.NodeId,
        Length = 2,
        ModbusCommandType = ModbusCommand.ReadDiscreteInputs
    }).Concat(allNodes.Select(node =>
    new ModbusRequest()
    {
        Address = node.EnumDoorCardReader == EnumDoorCardReader.Enterprise ? (ushort)(node.Area * 4096 + 256) : (ushort)(node.Area * 4096),
        SlaveId = node.NodeId,
        Length = 2,
        ModbusCommandType = ModbusCommand.ReadCoils
    })).ToList();


var maxEngineCount = setting.EngineCount;
var engineCount = requests.Count() < maxEngineCount ? requests.Count() : maxEngineCount;

CancellationTokenSource cts = new CancellationTokenSource();

var ip = new IPEndPoint(IPAddress.Parse(setting.Soyal701ServerIp), setting.Soyal701ServerModbusPort);

var engines = Enumerable.Range(0, engineCount).Select(i => new ModbusMasterPollingEngine<byte>((byte)i, ip, cts.Token)).ToList();
engines.ForEach(engine =>
{
    engine.NewDigitalValuesCallBack = async (ModbusRequest request, IEnumerable<bool> result) =>
    {
        var node = allNodes
           .Where(node => node.EnumDoorCardReader == EnumDoorCardReader.Enterprise)
           .Where(node => node.NodeId == request.SlaveId)
           .Where(node => node.Area == (byte)((request.Address - 256) / 4096))
           .FirstOrDefault();
        if (node is null)
        {
            node = allNodes
            .Where(node => node.EnumDoorCardReader == EnumDoorCardReader.Home)
            .Where(node => node.NodeId == request.SlaveId)
            .Where(node => node.Area == (byte)(request.Address / 4096))
            .FirstOrDefault();
        }
        if (node is null)
        {
            Console.WriteLine($"{DateTime.Now.ToString("HH:mm:ss.fff")}: Not Found: {request.SlaveId} {request.Address} {request.Length}: {result.Select(e => e ? "1" : "0").Aggregate((p, n) => p + n)}");
        }
        else
        {
            if (request.ModbusCommandType == ModbusCommand.ReadDiscreteInputs)
            {
                //dataStore.InputDiscretes[node.WeemaModbusStartAddress + setting.ModbusOffset] = result.First();
                var offset = node.WeemaModbusStartAddress + setting.ModbusOffset;
                    dataStore.InputDiscretes[offset] = result.First();
                    Console.WriteLine($"{DateTime.Now.ToString("HH:mm:ss.fff")}: Di EngineId: {engine.Id}, Area:{node.Area}, Node:{node.NodeId}, Status:{result.First()}");               
            }
            else
            {
                var offset = node.WeemaModbusStartAddress + setting.ModbusOffset;
                //dataStore.InputDiscretes[node.WeemaModbusStartAddress + setting.ModbusOffset] = result.First();
                dataStore.CoilDiscretes[offset] = result.First();
                Console.WriteLine($"{DateTime.Now.ToString("HH:mm:ss.fff")}: Coils EngineId: {engine.Id}, Area:{node.Area}, Node:{node.NodeId}, Status:{result.First()}");              
            }
        }
        
   
      
    };
});

//var tasks = engines
//    .Take(engineCount - 1)
//    .Select((engine, i) => engine.Poll(requests.Skip(i * (requests.Count() / engineCount)).Take(requests.Count() / engineCount)))
//    .ToList();
//tasks.Add(engines.Last().Poll(requests.Skip((engineCount - 1) * (requests.Count() / engineCount))));

List<Task> tasks = new List<Task>();
foreach (var item in engines.Select((engine, i) => new { Engine = engine, Index = i }))
{
    await Task.Delay(100);
    item.Engine.GapDelayInTicks = setting.PollGapDelayInTicks;
    if (engineCount - 1 != item.Index)
    {
        tasks.Add(item.Engine.Poll(requests.Skip(item.Index * (requests.Count() / engineCount)).Take(requests.Count() / engineCount)));
    }
    else
    {
        tasks.Add(item.Engine.Poll(requests.Skip(item.Index * (requests.Count() / engineCount))));
    }
}

var modbusServer = new ModbusTcpServer(dataStore, ip: setting.ModbusServerIp, port: setting.ModbusServerPort);
modbusServer.ValuesChanged += delegate (object? sender, Modbus.Device.ModbusSlaveRequestEventArgs e)
{
    if (e.Message.FunctionCode == 0x05)
    {
        var address = e.Message.MessageFrame[2] * 256 + e.Message.MessageFrame[3] + 1;
        //if (e.Message.MessageFrame[3] == 0xff)
        //{
        //    e.Message.MessageFrame[3] = 0xff;
        //}
        //else
        //{
        //    e.Message.MessageFrame[3] = 0x00;

        //}
        //Console.WriteLine($"Changed {dataStore.CoilDiscretes[address]}");


    }
    Console.WriteLine("Changed");
};
modbusServer.ModbusSlaveRequestReceived += delegate (object? sender, Modbus.Device.ModbusSlaveRequestEventArgs e)
{
    if (e.Message.FunctionCode == 0x05)
    {
        var address = e.Message.MessageFrame[2] * 256 + e.Message.MessageFrame[3] + 1;
        string cmd = e.Message.MessageFrame[4] == 0xff ? "0x2184" : "0x2183";
        try
        {
            var area = e.Message.MessageFrame[2];
            var nodeId = e.Message.MessageFrame[3] + 1;
            StringBuilder sb = new StringBuilder("{\"l_user\":\"login user\",\"cmd_array\":[{\"c_cmd\":2000,\"Area\":" + area + ",\"Node\":" + nodeId + ",\"Hex\":\"" + cmd + "\"}]}");
            var cts = new CancellationTokenSource(setting.SendJsonTimeoutInMilliseconds);

            TcpClient client = new TcpClient();
            client.ConnectAsync(setting.Soyal701ServerIp, setting.Soyal701ServerJsonPort, cts.Token).GetAwaiter().GetResult();
            NetworkStream ns = new NetworkStream(client.Client);

            StreamWriter sw = new StreamWriter(ns);
            sw.AutoFlush = true;
            sw.WriteAsync(sb, cts.Token).GetAwaiter().GetResult();
            client.GetStream().Close();
            client.Close();

        }
        catch (Exception exception)
        {
            Console.WriteLine(exception);
        }

        if (dataStore.CoilDiscretes[address])
        {
            e.Message.MessageFrame[4] = 0xff;
        }
        else
        {
            e.Message.MessageFrame[4] = 0x00;

        }
        //if (dataStore.CoilDiscretes[address])
        //{
        //    e.Message.MessageFrame[3] = 0xff;
        //}
        //else
        //{
        //    e.Message.MessageFrame[3] = 0x00;

        //}
        Console.WriteLine($"Write Received {dataStore.CoilDiscretes[address]}");


    }
};

tasks.Add(modbusServer.StartListen(cts.Token));

await Task.WhenAll(tasks);
//Console.WriteLine(tasks.SelectMany(e => e).Count());
//tasks.SelectMany(e => e).ToList().ForEach(t =>
//{
//    Console.WriteLine($"{t.SlaveId}, {t.Address}");
//});
//.Add(engines.Last().Poll(requests.Skip(engineCount *16)));
//.Count();
//.Select((engine, i) => engine.Poll(requests.Skip(i * 16).Take(engineCount / 16)))





//var value = allNodes.Select(area =>
//{
//    Console.WriteLine($"{area.Area}, {area.NodeId}, {area.WeemaModbusStartAddress}");
//    return area;
//}).ToList();


Console.WriteLine(allNodes.DistinctBy(d => d.WeemaModbusStartAddress).Count());
Console.WriteLine(allNodes.Count());




//Console.WriteLine("Hello, World!");
////List<(int area, int node)> nodes = new List<(int area, int node)>();
//var jscommands = new List<JsonCommand>();
//var area1 = Enumerable.Range(137, 1).Select(n => new JsonCommand()
//{
//    Area = 1,
//    Command = JsonCommandType.QueryStatus,
//    Node = n
//}).ToList();

//jscommands.AddRange(area1);
//var jsrequest = new JsonRequest()
//{
//    Commands = jscommands,
//};

//Console.Write(JsonSerializer.Serialize(jsrequest));
