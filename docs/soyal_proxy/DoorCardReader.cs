﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SoyalGateway
{
    internal enum EnumDoorCardReader
    {
        Home,
        Enterprise
    }
    internal class DoorCardReader
    {
        public byte Area { get; set; }
        public byte NodeId { get; set; }
        public EnumDoorCardReader EnumDoorCardReader { get; set; } = EnumDoorCardReader.Enterprise;
        public ushort WeemaModbusStartAddress { 
            get
            {
                return (ushort)((Area << 8 | NodeId ));
            }
        }
    }
}
