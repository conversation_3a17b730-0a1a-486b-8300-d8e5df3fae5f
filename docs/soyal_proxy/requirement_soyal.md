### Soyal門禁代理

### 注意事項

1. [使用.NET](http://使用.NET) 9開發。
2. 請切換到Git分支：SoyalProxy 再進行操作。
3. 符合企業級等級的穩定性及運行性能。
4. 使用Ngp.Communication.SipMessageProxy專案。
5. 共用的部分請定義在Ngp.Shared專案。
6. 使用Minimal API不要用Controller。
7. 在程式中加入簡易的英文註解。
8. 選擇Best Practice。
9. 資源能夠正確回收，正常運作或異常時，連線埠(如果有使用的話)均能正確關閉。
10. 提供FluentAPI接口。
11. 可以Graceful Exit。
12. 高平行化設計，避免同步IO阻塞。
13. 完善Thread-Safe功能，優先使用SemaphoreSlim而不是lock，並且避免鎖死的狀況產生。
14. 使用高性能的連線管理機制。
15. 不要使用Console.WriteLine，請使用ILogger，並合理配置不同的Log等級
16. 系統中使用的數值型別使用要精準，例：應該是ushort就不要定義成int。
17. 所有的事件委派請採用EventHandler<TEventArgs>。
18. 請透過Ngp.Shared共用層，與依賴注入在Ngp專案裡來調用ModbusTcpMaster專案，禁止直接加入ModbusTcpMaster ProjectReference。

### 自動輪詢與事件邏輯

1. 背景不停輪詢門禁的狀態，主要有兩種狀態，一種是門位的狀態，一種是鎖的狀態。
2. 一個門位+一個鎖的狀態=一組門禁狀態。
3. ModbusTcpSlave如果斷線，應該不停嘗試重新連線，請交給ModbusTcpMaster的重連功能即可。
4. Json Port只在需要時連線，平時不需要時可以不要連線。
5. 如果同時有多個Json Request，請妥善照順序傳送，並一次性將同時存在的Json Request處理完，不要多次連線/斷線。
6. 如果狀態改變，或連線狀態改變，請透過事件委派接口傳出。
7. 因為遠端的ModbusTcp Slave採用多重Slave Id設計，無法一次query多個點位，所以請採平行化設計，可設定一次要有多少個引擎在同時輪詢，以保持最高速度。

### 通訊規格

1. 讀取門位的狀態，與門鎖的狀態的機制請參考附檔，採用ModbusTcp。
2. 寫入開鎖的機制，請參考附檔，主要為對特定通訊埠傳送JSON封包。
3. 附檔為可以使用的Soyal Proxy，邏輯及流程請參考。
    1. docs\soyal_proxy\Program.cs
    2. docs\soyal_proxy\DoorCardREader.cs
    3. docs\soyal_proxy\JsonRequest.cs
4. 預設ModbusTcpSlave為502Port，接收Json的Tcp Port為1631。

### 測試範例

1. 在Ngp專案裡面建立一支SoyalProxyService的BackgroundService。
2. 使用這支Service進行範例代碼編寫(可參考ModbusTcpMasterService)。
3. 測試的ModbusTcpSlave：192.168.100.100:502，只有SlaveId：1有效。
4. 測試的Json Request：192.168.100.100:1631。

---