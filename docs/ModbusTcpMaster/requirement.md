## ModbusTCP Master

### 注意事項

1. [使用.NET](http://使用.NET) 9開發。
2. 符合企業級等級的穩定性及運行性能。
3. 使用Ngp.Communication.ModbusTcpMaster專案。
4. 使用Minimal API不要用Controller。
5. 在程式中加入簡易的英文註解。
6. 選擇Best Practice。
7. 源能夠正確回收，正常運作或異常時，連線埠均能正確關閉。
8. 提供FluentAPI接口。
9. 可以Graceful Exit。
10. 高平行化設計，避免同步IO阻塞。
11. 完善Thread-Safe功能，優先使用SemaphoreSlim而不是lock，並且主機避免死鎖。
12. 使用高性能的連線管理機制。
13. 不要使用Console.WriteLine，請使用ILogger，並合理配置不同的Log等級
14. 系統中使用的數值型別使用要精準，例：應該是ushort就不要定義成int。
15. 所有的事件委派請採用EventHandler<TEventArgs>。

### Modbus相關要求

1. 自己寫引擎，不使用NModbus或相似的庫。
2. 實作Modbus指令碼。
    1. ReadCoils
    2. ReadDiscreteInputs
    3. ReadHoldingRegisters
    4. ReadInputRegisters
    5. WriteSingleCoil
    6. WriteSingleRegister
    7. WriteMultipleCoils
    8. WriteMultipleRegisters
3. 支援型別
    1. bool
    2. byte
    3. int16
    4. uint16
    5. int32
    6. uint32
    7. int64
    8. uint64
    9. float
    10. double
4. 支援以下四種Endian
    1. Big‑Endian
    2. Little‑Endian
    3. Big‑Endian Byte‑Swap
    4. Little‑Endian Byte‑Swap
5. 只依據長度與endian處理型別即可，無需處理scale等的轉換
6. 支援0 based address, 1 based address, 與modbus register address等設定位址方式。
7. 支援延伸的modbus register address位址例如：40001~49999~410000,410001,410002等等。
8. 符合Modbus的標準規範。
9. 支援Single Write跟Multiple Write的模式。
10. 支援ModbusTCP及Modbus RTU over TCP的模式，
    1. Modbus RTU over TCP應該被強制設定為一次只能有一組問答。
    2. ModbusTCP可透過參數控制單一條連線的平行化程度，可自動分配平行化程度，或使用使用者指定的數值，預設只使用一條連線，搭配封包平行化。
11. 能夠辨識及處理Slave回傳回來的錯誤碼的能力。
12. 應能智慧最佳化封包
    1. 請求長度不超過對應的Max Polling Quantity，提高穩定度。
    2. 有效合併相鄰位址，減少輪詢次數。
    3. 請求不超出輪詢暫存器的邊界，減少問題。
13. 可以針對每個端點中的多個Slave輪詢。
14. 可以將各個端點設定為Single Write或Multiple Write。

### 通訊要求

1. 平行化處理，在實際使用時提供可連線至至少1000台或以上Slave的能力。
2. 連線與斷線重試的機制性能要強，減少系統中斷時間。
3. 妥善利用資源，避免用盡Socket的問題。
4. 可針對每個端點調整的參數：
    1. Read Timeout Millisecond
    2. Write Timeout Millisecond
    3. Package Delay Millisecond
    4. Poll Delay Millisecond
    5. Max Digital Polling Quantity
    6. Max Analog Polling Quantity
5. 每個端點(IP+Port)預設使用一個TCP連線。
6. 如果使用者對同端點(IP+Port)的組合設定多組，則視為不同個端點，
7. 運行時應可以隨時取得連線狀態。
8. 一個ModbusTCP Master要可以管理多組端點，每組端點又包含多個Slave以及對應的暫存器清單。
9. 針對指定點位需提供除彈跳功能，避免實體按鈕產生的彈跳現象。
10. 需能正確處理被分段的TCP封包。

### 輪詢與事件邏輯

1. 透過Fluent API提供需要讀取狀態的暫存器清單。
2. 背景不停輪詢對應的暫存器內容，如果有更新，則觸發數值更新的委派事件。
3. 如果沒有產生數值變化的輪詢，則不需要LOG，避免過多LOG。
4. 暫存器數值更新，請依照設定的數值型別以及Endian發送委派事件。
5. 如果連線狀態變更，則觸發連線狀態更新的委派事件。
6. 如果遠端伺服器回應錯誤封包如長度不符，或包含錯誤碼、CRC錯誤等，則觸發發生錯誤的委派事件。
7. 如果滿足上一點提到的錯誤內容，則判定為連線異常，將強制切斷連線連線。
8. 如果連線中斷，下一次恢復連線時，應該繼續接續往下輪詢，而不是從頭開始。
9. 如果接收到了寫入指令，應該完成當下指令後，優先發送寫入指令，發送後再繼續輪詢。

### 簡易流程說明

1. 創造所需的ModbusTCP Master實例，每個實例對應一個端點。
2. 透過Fluent API提供每個實例所需的設定、參數、以及需要輪詢的暫存器清單。
3. 開始輪詢之後，會不停的取得所需輪詢的暫存器清單的狀態。
4. 隨時收到寫入指令，能自動執行。
5. 如果連線產生異常，會積極重試，盡速恢復運行。
