### 通知引擎

### 注意事項

1. [使用.NET](http://使用.NET) 9開發。
2. 符合企業級等級的穩定性及運行性能。
3. 使用Ngp.Communication.NotifyEngine專案。
4. 使用Minimal API不要用Controller。
5. 在程式中加入簡易的英文註解。
6. 選擇Best Practice。
7. 源能夠正確回收，正常運作或異常時，連線埠均能正確關閉。
8. 提供FluentAPI接口。
9. 可以Graceful Exit。
10. 高平行化設計，避免同步IO阻塞。
11. 完善Thread-Safe功能，優先使用SemaphoreSlim而不是lock，並且避免鎖死的狀況產生。
12. 使用高性能的連線管理機制。
13. 不要使用Console.WriteLine，請使用ILogger，並合理配置不同的Log等級
14. 系統中使用的數值型別使用要精準，例：應該是ushort就不要定義成int。
15. 所有的事件委派請採用EventHandler<TEventArgs>。
16. 重要：事件委派邏輯以及Fluent API接口，請參照ModbusTcpMaster專案，做類似的設計。

### 自動輪詢與事件邏輯

1. 透過FluentAPI設定通知方式，含以下幾種：
    1. Email(SMTP) (至少要支援Gmail)
    2. 簡訊通知(台灣簡訊網)
    3. Sip Message通知(調用SipMessageProxy專案，但不與SipMessageProxy產生偶合，可使用依賴注入的方式，介面請定義在Ngp.Shared)
    4. LINE Message API
2. 透過FluentAPI設定各個項目所需使用的參數，網址，帳號密碼，收件人，等等，不要儲存在appsettings.json。
3. 所有方式都要支援傳送至多個收件人。
4. 同一個Notify需要可以同時利用多種方式傳送，例如：同時傳送給簡訊和Email的收件人。
5. 傳送失敗要有重送機制，傳送成功、失敗、超時等各項結果，要有事件委派通知。

### 測試

1. 參考ModbusTcpMasterService於Ngp專案產生對應測試。
2. 每種通知方式至少都有一項測試。
3. 可以先不用實際測試，因為尚未提供各項登入資訊。

---