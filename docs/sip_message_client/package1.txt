No.     Time                          Source                Destination           Protocol Length Info
     13 2025-06-21 10:09:25.129832    ***************       ***********           SIP      562    Request: REGISTER sip:***********:6088  (1 binding) | 

Frame 13: 562 bytes on wire (4496 bits), 562 bytes captured (4496 bits) on interface \Device\NPF_{AA104707-661A-4AF3-A5C6-6DE9D34171D6}, id 0
Ethernet II, Src: GigaByteTech_aa:83:cc (d8:5e:d3:aa:83:cc), Dst: ProxmoxServe_d2:61:04 (bc:24:11:d2:61:04)
Internet Protocol Version 4, Src: ***************, Dst: ***********
User Datagram Protocol, Src Port: 62518, Dst Port: 6088
Session Initiation Protocol (REGISTER)
    Request-Line: REGISTER sip:***********:6088 SIP/2.0
        Method: REGISTER
        Request-URI: sip:***********:6088
        [Resent Packet: False]
    Message Header
        Via: SIP/2.0/UDP ***************:62518;rport;branch=z9hG4bK626066c2179c4ff7b0d9924b4659b730
            Transport: UDP
            Sent-by Address: ***************
            Sent-by port: 62518
            RPort: rport
            Branch: z9hG4bK626066c2179c4ff7b0d9924b4659b730
        From: <sip:<EMAIL>>;tag=d5c47c4b6f674770
            SIP from address: sip:<EMAIL>
            SIP from tag: d5c47c4b6f674770
        To: <sip:<EMAIL>>
            SIP to address: sip:<EMAIL>
        Call-ID: 7f167bcd-7b2c-48eb-a520-c6a52df240b8
        [Generated Call-ID: 7f167bcd-7b2c-48eb-a520-c6a52df240b8]
        CSeq: 1001 REGISTER
            Sequence Number: 1001
            Method: REGISTER
        Contact: <sip:880001@***************:62518;ob>
            Contact URI: sip:880001@***************:62518;ob
        Content-Length: 0
        Max-Forwards: 70
        User-Agent: NGP-SipProxy/1.0
        Expires: 300
        Allow: PRACK, INVITE, ACK, BYE, CANCEL, UPDATE, INFO, SUBSCRIBE, NOTIFY, REFER, MESSAGE, OPTIONS

No.     Time                          Source                Destination           Protocol Length Info
     14 2025-06-21 10:09:25.130900    ***********           ***************       SIP      596    Status: 401 Unauthorized | 

Frame 14: 596 bytes on wire (4768 bits), 596 bytes captured (4768 bits) on interface \Device\NPF_{AA104707-661A-4AF3-A5C6-6DE9D34171D6}, id 0
Ethernet II, Src: ProxmoxServe_d2:61:04 (bc:24:11:d2:61:04), Dst: GigaByteTech_aa:83:cc (d8:5e:d3:aa:83:cc)
Internet Protocol Version 4, Src: ***********, Dst: ***************
User Datagram Protocol, Src Port: 6088, Dst Port: 62518
Session Initiation Protocol (401)
    Status-Line: SIP/2.0 401 Unauthorized
        Status-Code: 401
        [Resent Packet: False]
    Message Header
        Via: SIP/2.0/UDP ***************:62518;rport=62518;received=***************;branch=z9hG4bK626066c2179c4ff7b0d9924b4659b730
            Transport: UDP
            Sent-by Address: ***************
            Sent-by port: 62518
            RPort: 62518
            Received: ***************
            Branch: z9hG4bK626066c2179c4ff7b0d9924b4659b730
        Call-ID: 7f167bcd-7b2c-48eb-a520-c6a52df240b8
        [Generated Call-ID: 7f167bcd-7b2c-48eb-a520-c6a52df240b8]
        From: <sip:<EMAIL>>;tag=d5c47c4b6f674770
            SIP from address: sip:<EMAIL>
            SIP from tag: d5c47c4b6f674770
        To: <sip:<EMAIL>>;tag=z9hG4bK626066c2179c4ff7b0d9924b4659b730
            SIP to address: sip:<EMAIL>
            SIP to tag: z9hG4bK626066c2179c4ff7b0d9924b4659b730
        CSeq: 1001 REGISTER
            Sequence Number: 1001
            Method: REGISTER
        WWW-Authenticate: Digest realm="asterisk",nonce="1750471765/3a76df3572293e106da10f44b9ba5170",opaque="7450b52b4d6ed25b",algorithm=MD5,qop="auth"
            Authentication Scheme: Digest
            Realm: "asterisk"
            Nonce Value: "1750471765/3a76df3572293e106da10f44b9ba5170"
            Opaque Value: "7450b52b4d6ed25b"
            Algorithm: MD5
            QOP: "auth"
        Server: FPBX-**********(22.2.0)
        Content-Length:  0




fail: Ngp.Communication.SipMessageProxy.Connection.UdpConnectionManager[0]
      Error sending message with correlation ID f93ad06b-6976-4cc2-9b33-3a0310b9fde5
      System.TimeoutException: Timeout waiting for response to f93ad06b-6976-4cc2-9b33-3a0310b9fde5
         at Ngp.Communication.SipMessageProxy.Connection.UdpConnectionManager.SendAndReceiveAsync(String message, String correlationId, CancellationToken cancellationToken) in C:\WorkingDirectory\2025_repos\NextGenerationPlatform\Ngp\Ngp.Communication.SipMessageProxy\Connection\UdpConnectionManager.cs:line 196
fail: Ngp.Communication.SipMessageProxy.Connection.SipRegistrationManager[0]
      Registration failed
      System.TimeoutException: Timeout waiting for response to f93ad06b-6976-4cc2-9b33-3a0310b9fde5
         at Ngp.Communication.SipMessageProxy.Connection.UdpConnectionManager.SendAndReceiveAsync(String message, String correlationId, CancellationToken cancellationToken) in C:\WorkingDirectory\2025_repos\NextGenerationPlatform\Ngp\Ngp.Communication.SipMessageProxy\Connection\UdpConnectionManager.cs:line 196
         at Ngp.Communication.SipMessageProxy.Connection.SipRegistrationManager.RegisterAsync(CancellationToken cancellationToken) in C:\WorkingDirectory\2025_repos\NextGenerationPlatform\Ngp\Ngp.Communication.SipMessageProxy\Connection\SipRegistrationManager.cs:line 156
info: Ngp.Services.SipMessageProxyService[0]
      SIP registration state changed: Registering -> <NAME_EMAIL>
warn: Ngp.Services.SipMessageProxyService[0]
      Registration state change error: Timeout waiting for response to f93ad06b-6976-4cc2-9b33-3a0310b9fde5
fail: Ngp.Services.SipMessageProxyService[0]
      SIP error in Registration: Registration failed
      System.TimeoutException: Timeout waiting for response to f93ad06b-6976-4cc2-9b33-3a0310b9fde5
         at Ngp.Communication.SipMessageProxy.Connection.UdpConnectionManager.SendAndReceiveAsync(String message, String correlationId, CancellationToken cancellationToken) in C:\WorkingDirectory\2025_repos\NextGenerationPlatform\Ngp\Ngp.Communication.SipMessageProxy\Connection\UdpConnectionManager.cs:line 196
         at Ngp.Communication.SipMessageProxy.Connection.SipRegistrationManager.RegisterAsync(CancellationToken cancellationToken) in C:\WorkingDirectory\2025_repos\NextGenerationPlatform\Ngp\Ngp.Communication.SipMessageProxy\Connection\SipRegistrationManager.cs:line 156
info: Ngp.Communication.SipMessageProxy.SipMessageProxy[0]
      SIP proxy started successfully
info: Ngp.Services.SipMessageProxyService[0]
      SIP Message Proxy Service started successfully