No.     Time                          Source                Destination           Protocol Length Info
     20 2025-06-20 19:57:01.564935    ***************       ***********           SIP      570    Request: REGISTER sip:***********:6088  (1 binding) | 

Frame 20: 570 bytes on wire (4560 bits), 570 bytes captured (4560 bits) on interface \Device\NPF_{AA104707-661A-4AF3-A5C6-6DE9D34171D6}, id 0
Ethernet II, Src: GigaByteTech_aa:83:cc (d8:5e:d3:aa:83:cc), Dst: ProxmoxServe_d2:61:04 (bc:24:11:d2:61:04)
Internet Protocol Version 4, Src: ***************, Dst: ***********
User Datagram Protocol, Src Port: 57995, Dst Port: 6088
Session Initiation Protocol (REGISTER)
    Request-Line: REGISTER sip:***********:6088 SIP/2.0
        Method: REGISTER
        Request-URI: sip:***********:6088
        [Resent Packet: False]
    Message Header
        Via: SIP/2.0/UDP ***************:57995;rport;branch=z9hG4bKPjdc01ff67b0da44c58304874f91701e42
            Transport: UDP
            Sent-by Address: ***************
            Sent-by port: 57995
            RPort: rport
            Branch: z9hG4bKPjdc01ff67b0da44c58304874f91701e42
        Max-Forwards: 70
        From: <sip:<EMAIL>>;tag=a362b2e182104c7bbd79cfb184493dc9
            SIP from address: sip:<EMAIL>
            SIP from tag: a362b2e182104c7bbd79cfb184493dc9
        To: <sip:<EMAIL>>
            SIP to address: sip:<EMAIL>
        Call-ID: 08dd3e5c7f9b4452917676537ccd8db2
        [Generated Call-ID: 08dd3e5c7f9b4452917676537ccd8db2]
        CSeq: 29985 REGISTER
            Sequence Number: 29985
            Method: REGISTER
        User-Agent: MicroSIP/3.21.6
        Contact: <sip:8811@***************:57995;ob>
            Contact URI: sip:8811@***************:57995;ob
        Expires: 90
        Allow: PRACK, INVITE, ACK, BYE, CANCEL, UPDATE, INFO, SUBSCRIBE, NOTIFY, REFER, MESSAGE, OPTIONS
        Content-Length:  0

No.     Time                          Source                Destination           Protocol Length Info
     21 2025-06-20 19:57:01.565819    ***********           ***************       SIP      609    Status: 401 Unauthorized | 

Frame 21: 609 bytes on wire (4872 bits), 609 bytes captured (4872 bits) on interface \Device\NPF_{AA104707-661A-4AF3-A5C6-6DE9D34171D6}, id 0
Ethernet II, Src: ProxmoxServe_d2:61:04 (bc:24:11:d2:61:04), Dst: GigaByteTech_aa:83:cc (d8:5e:d3:aa:83:cc)
Internet Protocol Version 4, Src: ***********, Dst: ***************
User Datagram Protocol, Src Port: 6088, Dst Port: 57995
Session Initiation Protocol (401)
    Status-Line: SIP/2.0 401 Unauthorized
        Status-Code: 401
        [Resent Packet: False]
    Message Header
        Via: SIP/2.0/UDP ***************:57995;rport=57995;received=***************;branch=z9hG4bKPjdc01ff67b0da44c58304874f91701e42
            Transport: UDP
            Sent-by Address: ***************
            Sent-by port: 57995
            RPort: 57995
            Received: ***************
            Branch: z9hG4bKPjdc01ff67b0da44c58304874f91701e42
        Call-ID: 08dd3e5c7f9b4452917676537ccd8db2
        [Generated Call-ID: 08dd3e5c7f9b4452917676537ccd8db2]
        From: <sip:<EMAIL>>;tag=a362b2e182104c7bbd79cfb184493dc9
            SIP from address: sip:<EMAIL>
            SIP from tag: a362b2e182104c7bbd79cfb184493dc9
        To: <sip:<EMAIL>>;tag=z9hG4bKPjdc01ff67b0da44c58304874f91701e42
            SIP to address: sip:<EMAIL>
            SIP to tag: z9hG4bKPjdc01ff67b0da44c58304874f91701e42
        CSeq: 29985 REGISTER
            Sequence Number: 29985
            Method: REGISTER
        WWW-Authenticate: Digest realm="asterisk",nonce="1750420623/a7a76a4c75f75d6dd4ecc8b3c2549cbc",opaque="7ceadc854ffd276a",algorithm=MD5,qop="auth"
            Authentication Scheme: Digest
            Realm: "asterisk"
            Nonce Value: "1750420623/a7a76a4c75f75d6dd4ecc8b3c2549cbc"
            Opaque Value: "7ceadc854ffd276a"
            Algorithm: MD5
            QOP: "auth"
        Server: FPBX-**********(22.2.0)
        Content-Length:  0

No.     Time                          Source                Destination           Protocol Length Info
     22 2025-06-20 19:57:01.566059    ***************       ***********           SIP      861    Request: REGISTER sip:***********:6088  (1 binding) | 

Frame 22: 861 bytes on wire (6888 bits), 861 bytes captured (6888 bits) on interface \Device\NPF_{AA104707-661A-4AF3-A5C6-6DE9D34171D6}, id 0
Ethernet II, Src: GigaByteTech_aa:83:cc (d8:5e:d3:aa:83:cc), Dst: ProxmoxServe_d2:61:04 (bc:24:11:d2:61:04)
Internet Protocol Version 4, Src: ***************, Dst: ***********
User Datagram Protocol, Src Port: 57995, Dst Port: 6088
Session Initiation Protocol (REGISTER)
    Request-Line: REGISTER sip:***********:6088 SIP/2.0
        Method: REGISTER
        Request-URI: sip:***********:6088
        [Resent Packet: False]
    Message Header
        Via: SIP/2.0/UDP ***************:57995;rport;branch=z9hG4bKPj9625159fec0e48eda333b15acd2b2ec3
            Transport: UDP
            Sent-by Address: ***************
            Sent-by port: 57995
            RPort: rport
            Branch: z9hG4bKPj9625159fec0e48eda333b15acd2b2ec3
        Max-Forwards: 70
        From: <sip:<EMAIL>>;tag=a362b2e182104c7bbd79cfb184493dc9
            SIP from address: sip:<EMAIL>
            SIP from tag: a362b2e182104c7bbd79cfb184493dc9
        To: <sip:<EMAIL>>
            SIP to address: sip:<EMAIL>
        Call-ID: 08dd3e5c7f9b4452917676537ccd8db2
        [Generated Call-ID: 08dd3e5c7f9b4452917676537ccd8db2]
        CSeq: 29986 REGISTER
            Sequence Number: 29986
            Method: REGISTER
        User-Agent: MicroSIP/3.21.6
        Contact: <sip:8811@***************:57995;ob>
            Contact URI: sip:8811@***************:57995;ob
        Expires: 90
        Allow: PRACK, INVITE, ACK, BYE, CANCEL, UPDATE, INFO, SUBSCRIBE, NOTIFY, REFER, MESSAGE, OPTIONS
         […]Authorization: Digest username="8811", realm="asterisk", nonce="1750420623/a7a76a4c75f75d6dd4ecc8b3c2549cbc", uri="sip:***********:6088", response="54d7cc5f547c8dcba160d013115816ad", algorithm=MD5, cnonce="a7dcf20accde459fa36c73cc06b
            Authentication Scheme: Digest
            Username: "8811"
            Realm: "asterisk"
            Nonce Value: "1750420623/a7a76a4c75f75d6dd4ecc8b3c2549cbc"
            Authentication URI: "sip:***********:6088"
            Digest Authentication Response: "54d7cc5f547c8dcba160d013115816ad"
            Algorithm: MD5
            CNonce Value: "a7dcf20accde459fa36c73cc06b81a4d"
            Opaque Value: "7ceadc854ffd276a"
            QOP: auth
            Nonce Count: 00000001
        Content-Length:  0

No.     Time                          Source                Destination           Protocol Length Info
     23 2025-06-20 19:57:01.567012    ***********           ***************       SIP      560    Status: 200 OK (REGISTER)  (1 binding) | 

Frame 23: 560 bytes on wire (4480 bits), 560 bytes captured (4480 bits) on interface \Device\NPF_{AA104707-661A-4AF3-A5C6-6DE9D34171D6}, id 0
Ethernet II, Src: ProxmoxServe_d2:61:04 (bc:24:11:d2:61:04), Dst: GigaByteTech_aa:83:cc (d8:5e:d3:aa:83:cc)
Internet Protocol Version 4, Src: ***********, Dst: ***************
User Datagram Protocol, Src Port: 6088, Dst Port: 57995
Session Initiation Protocol (200)
    Status-Line: SIP/2.0 200 OK
        Status-Code: 200
        [Resent Packet: False]
    Message Header
        Via: SIP/2.0/UDP ***************:57995;rport=57995;received=***************;branch=z9hG4bKPj9625159fec0e48eda333b15acd2b2ec3
            Transport: UDP
            Sent-by Address: ***************
            Sent-by port: 57995
            RPort: 57995
            Received: ***************
            Branch: z9hG4bKPj9625159fec0e48eda333b15acd2b2ec3
        Call-ID: 08dd3e5c7f9b4452917676537ccd8db2
        [Generated Call-ID: 08dd3e5c7f9b4452917676537ccd8db2]
        From: <sip:<EMAIL>>;tag=a362b2e182104c7bbd79cfb184493dc9
            SIP from address: sip:<EMAIL>
            SIP from tag: a362b2e182104c7bbd79cfb184493dc9
        To: <sip:<EMAIL>>;tag=z9hG4bKPj9625159fec0e48eda333b15acd2b2ec3
            SIP to address: sip:<EMAIL>
            SIP to tag: z9hG4bKPj9625159fec0e48eda333b15acd2b2ec3
        CSeq: 29986 REGISTER
            Sequence Number: 29986
            Method: REGISTER
        Date: Fri, 20 Jun 2025 11:57:03 GMT
        Contact: <sip:8811@***************:57995;ob>;expires=89
            Contact URI: sip:8811@***************:57995;ob
            Contact parameter: expires=89
        Expires: 90
        Server: FPBX-**********(22.2.0)
        Content-Length:  0

No.     Time                          Source                Destination           Protocol Length Info
     24 2025-06-20 19:57:01.567592    ***********           ***************       SIP      476    Request: OPTIONS sip:8811@***************:57995;ob | 

Frame 24: 476 bytes on wire (3808 bits), 476 bytes captured (3808 bits) on interface \Device\NPF_{AA104707-661A-4AF3-A5C6-6DE9D34171D6}, id 0
Ethernet II, Src: ProxmoxServe_d2:61:04 (bc:24:11:d2:61:04), Dst: GigaByteTech_aa:83:cc (d8:5e:d3:aa:83:cc)
Internet Protocol Version 4, Src: ***********, Dst: ***************
User Datagram Protocol, Src Port: 6088, Dst Port: 57995
Session Initiation Protocol (OPTIONS)
    Request-Line: OPTIONS sip:8811@***************:57995;ob SIP/2.0
        Method: OPTIONS
        Request-URI: sip:8811@***************:57995;ob
        [Resent Packet: False]
    Message Header
        Via: SIP/2.0/UDP ***********:6088;rport;branch=z9hG4bKPja1878073-624e-4a90-ba4e-c27e0994b165
            Transport: UDP
            Sent-by Address: ***********
            Sent-by port: 6088
            RPort: rport
            Branch: z9hG4bKPja1878073-624e-4a90-ba4e-c27e0994b165
        From: <sip:8811@***********>;tag=3c84b863-0c48-4723-91c4-dd2ec063f5b3
            SIP from address: sip:8811@***********
            SIP from tag: 3c84b863-0c48-4723-91c4-dd2ec063f5b3
        To: <sip:8811@***************;ob>
            SIP to address: sip:8811@***************;ob
        Contact: <sip:8811@***********:6088>
            Contact URI: sip:8811@***********:6088
        Call-ID: 3dcf69b3-feb1-4b88-8caf-44ffc2e763c9
        [Generated Call-ID: 3dcf69b3-feb1-4b88-8caf-44ffc2e763c9]
        CSeq: 34180 OPTIONS
            Sequence Number: 34180
            Method: OPTIONS
        Max-Forwards: 70
        User-Agent: FPBX-**********(22.2.0)
        Content-Length:  0

No.     Time                          Source                Destination           Protocol Length Info
     25 2025-06-20 19:57:01.567695    ***************       ***********           SIP      839    Status: 200 OK (OPTIONS) | 

Frame 25: 839 bytes on wire (6712 bits), 839 bytes captured (6712 bits) on interface \Device\NPF_{AA104707-661A-4AF3-A5C6-6DE9D34171D6}, id 0
Ethernet II, Src: GigaByteTech_aa:83:cc (d8:5e:d3:aa:83:cc), Dst: ProxmoxServe_d2:61:04 (bc:24:11:d2:61:04)
Internet Protocol Version 4, Src: ***************, Dst: ***********
User Datagram Protocol, Src Port: 57995, Dst Port: 6088
Session Initiation Protocol (200)
    Status-Line: SIP/2.0 200 OK
        Status-Code: 200
        [Resent Packet: False]
        [Request Frame: 24]
        [Response Time (ms): 0]
    Message Header
        Via: SIP/2.0/UDP ***********:6088;rport=6088;received=***********;branch=z9hG4bKPja1878073-624e-4a90-ba4e-c27e0994b165
            Transport: UDP
            Sent-by Address: ***********
            Sent-by port: 6088
            RPort: 6088
            Received: ***********
            Branch: z9hG4bKPja1878073-624e-4a90-ba4e-c27e0994b165
        Call-ID: 3dcf69b3-feb1-4b88-8caf-44ffc2e763c9
        [Generated Call-ID: 3dcf69b3-feb1-4b88-8caf-44ffc2e763c9]
        From: <sip:8811@***********>;tag=3c84b863-0c48-4723-91c4-dd2ec063f5b3
            SIP from address: sip:8811@***********
            SIP from tag: 3c84b863-0c48-4723-91c4-dd2ec063f5b3
        To: <sip:8811@***************;ob>;tag=z9hG4bKPja1878073-624e-4a90-ba4e-c27e0994b165
            SIP to address: sip:8811@***************;ob
            SIP to tag: z9hG4bKPja1878073-624e-4a90-ba4e-c27e0994b165
        CSeq: 34180 OPTIONS
            Sequence Number: 34180
            Method: OPTIONS
        Allow: PRACK, INVITE, ACK, BYE, CANCEL, UPDATE, INFO, SUBSCRIBE, NOTIFY, REFER, MESSAGE, OPTIONS
        Accept: application/sdp, application/pidf+xml, application/xpidf+xml, application/simple-message-summary, message/sipfrag;version=2.0, application/im-iscomposing+xml, text/plain
        Supported: replaces, 100rel, timer, norefersub, trickle-ice
        Allow-Events: presence, message-summary, refer
        User-Agent: MicroSIP/3.21.6
        Content-Length:  0

No.     Time                          Source                Destination           Protocol Length Info
     26 2025-06-20 19:57:01.576012    ***********           ***************       SIP      687    Request: NOTIFY sip:8811@***************:57995;ob | 

Frame 26: 687 bytes on wire (5496 bits), 687 bytes captured (5496 bits) on interface \Device\NPF_{AA104707-661A-4AF3-A5C6-6DE9D34171D6}, id 0
Ethernet II, Src: ProxmoxServe_d2:61:04 (bc:24:11:d2:61:04), Dst: GigaByteTech_aa:83:cc (d8:5e:d3:aa:83:cc)
Internet Protocol Version 4, Src: ***********, Dst: ***************
User Datagram Protocol, Src Port: 6088, Dst Port: 57995
Session Initiation Protocol (NOTIFY)
    Request-Line: NOTIFY sip:8811@***************:57995;ob SIP/2.0
        Method: NOTIFY
        Request-URI: sip:8811@***************:57995;ob
        [Resent Packet: False]
    Message Header
        Via: SIP/2.0/UDP ***********:6088;rport;branch=z9hG4bKPj8a625b75-6b5d-4afa-bfd6-a67c71f2287b
            Transport: UDP
            Sent-by Address: ***********
            Sent-by port: 6088
            RPort: rport
            Branch: z9hG4bKPj8a625b75-6b5d-4afa-bfd6-a67c71f2287b
        From: <sip:8811@***********>;tag=a98585bc-fe59-4668-8dcf-f6f176648b69
            SIP from address: sip:8811@***********
            SIP from tag: a98585bc-fe59-4668-8dcf-f6f176648b69
        To: <sip:8811@***************;ob>
            SIP to address: sip:8811@***************;ob
        Contact: <sip:8811@***********:6088>
            Contact URI: sip:8811@***********:6088
        Call-ID: 35f4669c-b1bf-41d0-b8c6-4f1750e39217
        [Generated Call-ID: 35f4669c-b1bf-41d0-b8c6-4f1750e39217]
        CSeq: 40221 NOTIFY
            Sequence Number: 40221
            Method: NOTIFY
        Subscription-State: terminated
        Event: message-summary
        Allow-Events: presence, dialog, message-summary, refer
        Max-Forwards: 70
        User-Agent: FPBX-**********(22.2.0)
        Content-Type: application/simple-message-summary
        Content-Length:    48
    Message Body

No.     Time                          Source                Destination           Protocol Length Info
     27 2025-06-20 19:57:01.576211    ***************       ***********           SIP      452    Status: 200 OK (NOTIFY) | 

Frame 27: 452 bytes on wire (3616 bits), 452 bytes captured (3616 bits) on interface \Device\NPF_{AA104707-661A-4AF3-A5C6-6DE9D34171D6}, id 0
Ethernet II, Src: GigaByteTech_aa:83:cc (d8:5e:d3:aa:83:cc), Dst: ProxmoxServe_d2:61:04 (bc:24:11:d2:61:04)
Internet Protocol Version 4, Src: ***************, Dst: ***********
User Datagram Protocol, Src Port: 57995, Dst Port: 6088
Session Initiation Protocol (200)
    Status-Line: SIP/2.0 200 OK
        Status-Code: 200
        [Resent Packet: False]
        [Request Frame: 26]
        [Response Time (ms): 0]
    Message Header
        Via: SIP/2.0/UDP ***********:6088;rport=6088;received=***********;branch=z9hG4bKPj8a625b75-6b5d-4afa-bfd6-a67c71f2287b
            Transport: UDP
            Sent-by Address: ***********
            Sent-by port: 6088
            RPort: 6088
            Received: ***********
            Branch: z9hG4bKPj8a625b75-6b5d-4afa-bfd6-a67c71f2287b
        Call-ID: 35f4669c-b1bf-41d0-b8c6-4f1750e39217
        [Generated Call-ID: 35f4669c-b1bf-41d0-b8c6-4f1750e39217]
        From: <sip:8811@***********>;tag=a98585bc-fe59-4668-8dcf-f6f176648b69
            SIP from address: sip:8811@***********
            SIP from tag: a98585bc-fe59-4668-8dcf-f6f176648b69
        To: <sip:8811@***************;ob>;tag=z9hG4bKPj8a625b75-6b5d-4afa-bfd6-a67c71f2287b
            SIP to address: sip:8811@***************;ob
            SIP to tag: z9hG4bKPj8a625b75-6b5d-4afa-bfd6-a67c71f2287b
        CSeq: 40221 NOTIFY
            Sequence Number: 40221
            Method: NOTIFY
        User-Agent: MicroSIP/3.21.6
        Content-Length:  0
