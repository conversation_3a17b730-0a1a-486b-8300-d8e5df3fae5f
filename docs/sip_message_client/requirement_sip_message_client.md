### 注意事項

1. [使用.NET](http://使用.NET) 9開發。
2. 符合企業級等級的穩定性及運行性能。
3. 使用Ngp.Communication.SipMessageProxy專案。
4. 使用Minimal API不要用Controller。
5. 在程式中加入簡易的英文註解。
6. 選擇Best Practice。
7. 源能夠正確回收，正常運作或異常時，連線埠均能正確關閉。
8. 提供FluentAPI接口。
9. 可以Graceful Exit。
10. 高平行化設計，避免同步IO阻塞。
11. 完善Thread-Safe功能，優先使用SemaphoreSlim而不是lock，並且避免鎖死的狀況產生。
12. 使用高性能的連線管理機制。
13. 不要使用Console.WriteLine，請使用ILogger，並合理配置不同的Log等級
14. 系統中使用的數值型別使用要精準，例：應該是ushort就不要定義成int。
15. 所有的事件委派請採用EventHandler<TEventArgs>。

### 自動輪詢與事件邏輯

1. 透過Fluent API提供要登入的伺服器IP位址，PORT號，及分機號碼，密碼，域名等等。
2. 啟動後即透過SIP協議，自動註冊至Sip Server (通常是Asterisk)。
3. 註冊後，需隨時維持註冊狀態，如果斷線了需即時重試。
4. 註冊狀態變更時，應該傳送相對應的事件委派。
5. 提供一個傳送訊息的接口，可以讓程式傳送SIP的SIMPLE Message到指定分機。
6. 設計傳送成功與失敗等狀態的事件通知。
7. 無論正常或異常退出程序，都應該要送出反註冊的封包，讓伺服器即時知道我們離線了。

### 通訊規格

1. SIP協議，請使用sip socery做為SIP棧，不要自己手刻。
2. Sip Socery的文件請參考context7，上面有最新的文件內容。
3. 支援UDP與自定義Port號模式。

### 重點提示

1. 伺服器擁有多重IP，你對***********送，他可能會從***********回來。
2. 無論伺服器從哪個IP回應，你都要能正常解通析回應。
3. 無論伺服器從哪個IP回應，你都應該維持對一開始指定的IP傳送封包。

---