## 注意事項

1. 使用.NET 9開發。
2. 符合企業級等級的穩定性及運行性能。
3. 作為獨立的函式庫專案Ngp.Calculation.LogicEngine。
4. 在程式中加入簡易的英文註解。
5. 選擇Best Practice。
6. 資源能夠正確回收，正常運作或異常時，記憶體與運算資源均能正確釋放。
7. 提供FluentAPI接口作為輸入介面。
8. 透過EventHandler<TEventArgs>事件委派作為輸出介面。
9. 高平行化設計，避免同步運算阻塞。
10. 完善Thread-Safe功能，優先使用SemaphoreSlim而不是lock，避免死鎖。
11. 使用高性能的運算管線機制。
12. 使用ILogger介面，讓使用者可自行注入Logger實作。
13. 系統中使用的數值型別使用要精準，例：應該是decimal就不要定義成double。
14. 完全解耦設計，不依賴任何特定的資料來源或通訊協定。
15. 請參考ModbusTcpMaster的架構設計。

## 核心功能要求

### 1. 運算引擎架構

1. **輸入介面（FluentAPI）**
    - 註冊輸入變數
    - 更新變數數值
    - 設定運算公式
    - 配置運算參數
2. **輸出介面（事件委派）**
    - 運算結果更新事件
    - 運算錯誤事件
    - 狀態變更事件
    - 數值變化事件
3. **運算處理核心**
    - 公式解析器
    - 運算執行器
    - 快取管理器
    - 錯誤處理器

### 2. 自定義公式功能

1. **公式語法支援**
    
    ```
    // 基本運算
    Output = A + B
    Output = (A - B) * C / D
    
    // 數學函數
    Output = Sin(A) + Cos(B)
    Output = Sqrt(A^2 + B^2)
    Output = Max(A, B, C)
    Output = Min(A, B, C)
    
    // 邏輯運算
    Output = A > B ? C : D
    Output = (A AND B) OR C
    
    // 統計函數
    Output = Avg(A, B, C, D)
    Output = Sum(A, B, C)
    ```
    
2. **變數綁定機制**
    - 支援任意命名的輸入變數
    - 動態變數類型推斷
    - 變數別名管理
    - 變數有效性檢查
3. **公式管理**
    - 公式新增/修改/刪除
    - 公式語法驗證
    - 公式相依性分析
    - 循環相依檢測

### 3. 數值轉換功能

1. **Scale與Offset**
    - 線性轉換：Output = Input * Scale + Offset
    - 支援每個變數獨立設定
    - 支援正向與反向轉換
    - 支援鏈式轉換
2. **Lookup Table**
    - 一維查表（輸入值→輸出值）
    - 線性內插補間
    - 邊界值處理策略
    - 多段式映射
3. **進階轉換**
    - 非線性轉換（對數、指數）
    - 單位轉換（溫度、壓力等）
    - 資料型別轉換
    - 精度控制

### 4. 支援的資料型別

1. **數值型別**
    - bool
    - byte
    - int16 / uint16
    - int32 / uint32
    - int64 / uint64
    - float
    - double
    - decimal
2. **型別轉換規則**
    - 自動型別提升
    - 精度保護機制
    - 溢位處理策略

### 5. 運算觸發機制

1. **觸發模式**
    - 數值變化觸發（任一輸入變數改變）
    - 手動觸發（明確呼叫）
    - 批次觸發（累積變化後統一處理）
2. **運算優化**
    - 相依性分析（只計算受影響的公式）
    - 平行運算（無相依的公式並行處理）
    - 結果快取（避免重複運算）

## FluentAPI設計

```csharp
// 建立運算引擎實例
var engine = new LogicEngineBuilder()    
.WithLogger(logger)    
.WithMaxConcurrency(10)    
.Build();

// 註冊輸入變數
engine.RegisterInput("DI1")
.WithInitialValue(0)
.WithScale(1.0)    
.WithOffset(0);

engine.RegisterInput("DI2")    
.WithInitialValue(0)
.WithLookupTable(table => table
        .AddMapping(0, 0)
        .AddMapping(100, 50)
        .AddMapping(200, 150)
        .WithInterpolation());

// 定義運算公式
engine.DefineFormula("CALC1")
.WithExpression("DI1 * DI2 + 100")
.WithOutputScale(0.1)
.WithOutputOffset(-10)
.OnError(ErrorStrategy.UseLastValue);

engine.DefineFormula("CALC2")
.WithExpression("CALC1 > 50 ? DI1 : DI2")
.WithDecimalPlaces(2);

// 註冊事件處理
engine.OutputChanged += (sender, e) => {
// e.FormulaId, e.OldValue, e.NewValue
};

engine.CalculationError += (sender, e) => {    
// e.FormulaId, e.Error, e.Expression
};

// 開始運算
engine.Start();
// 更新輸入值
engine.UpdateInput("DI1", 25.5);
engine.UpdateInput("DI2", 100);
// 批次更新
engine.BatchUpdate(batch => batch
.Update("DI1", 30)    
.Update("DI2", 150)
);
```

## 事件定義

### 1. OutputChangedEventArgs

```csharp
public class OutputChangedEventArgs : EventArgs
{    
	public string FormulaId { get; set; }    
	public string FormulaExpression { get; set; }   
	public object OldValue { get; set; }    
	public object NewValue { get; set; }    
	public DateTime Timestamp { get; set; }    
	public Dictionary InputValues { get; set; }
}
```

### 2. CalculationErrorEventArgs

```csharp
public class CalculationErrorEventArgs : EventArgs
{    
	public string FormulaId { get; set; }    
	public string Expression { get; set; }    
	public Exception Error { get; set; }    
	public Dictionary InputValues { get; set; }    
	public DateTime Timestamp { get; set; }
}
```

### 3. InputChangedEventArgs

```csharp
public class InputChangedEventArgs : EventArgs
{    
	public string InputId { get; set; }    
	public object OldValue { get; set; }    
	public object NewValue { get; set; }    
	public object ScaledValue { get; set; }    
	public DateTime Timestamp { get; set; }
}
```

### 4. EngineStateChangedEventArgs

```csharp
public class EngineStateChangedEventArgs : EventArgs
{    
	public EngineState OldState { get; set; }    
	public EngineState NewState { get; set; }    
	public string Reason { get; set; }
}
```

## 效能與可靠性設計

### 1. 效能目標

- 單一公式運算延遲 < 0.1ms
- 支援至少1,000個同時運算的公式
- 每秒處理至少50,000筆輸入更新
- 記憶體使用最佳化

### 2. 錯誤處理策略

- **UseLastValue**: 使用上次的有效值
- **UseDefaultValue**: 使用預設值
- **Propagate**: 傳播錯誤，不輸出結果
- **UseNaN**: 輸出NaN或null

### 3. Thread-Safe設計

- 所有公開方法都是Thread-Safe
- 使用Concurrent Collections
- 避免鎖定競爭
- 支援多執行緒更新輸入

## 進階功能

### 1. 運算函數擴展

```csharp
//註冊自定義函數
engine.RegisterFunction("MyCustomFunc", (double[] args) => {    
	return args.Sum() / args.Length;
});
// 在公式中使用
engine.DefineFormula("CUSTOM1")
.WithExpression("MyCustomFunc(DI1, DI2, DI3)");
```

### 2. 條件運算

```csharp
engine.DefineFormula("CONDITION1")
.WithCondition("DI1 > 0", "DI1 * 2")
.WithCondition("DI2 > 0", "DI2 * 3")
.WithElse("0");
```

### 3. 時間相關運算

```csharp
// 移動平均
engine.DefineFormula("AVG1")
.WithExpression("MovingAvg(DI1, 10)") 
// 最近10個值的平均 

// 變化率
engine.DefineFormula("RATE1")
.WithExpression("RateOfChange(DI1, 60)") 
// 60秒內的變化率
```

### 4. 狀態管理

```csharp
// 查詢運算狀態
var status = engine.GetStatus();
// status.ActiveFormulas, status.ErrorCount, status.LastUpdateTime
// 查詢特定公式狀態
var formulaInfo = engine.GetFormulaInfo("CALC1");
// formulaInfo.LastValue, formulaInfo.LastError, formulaInfo.ExecutionTime
```

## 使用範例

### 1. 溫度轉換範例

```csharp
var engine = new LogicEngineBuilder().Build();
// 攝氏溫度輸入
engine.RegisterInput("TempC")
.WithInitialValue(25.0);
// 華氏轉換
engine.DefineFormula("TempF")
.WithExpression("TempC * 9/5 + 32");
// 絕對溫度
engine.DefineFormula("TempK")
.WithExpression("TempC + 273.15");

engine.Start();
```

### 2. 警報邏輯範例

```csharp
// 註冊感測器輸入
engine.RegisterInput("Pressure")
.WithScale(0.1)  
// 原始值轉換為Bar    
.WithOffset(0);engine.RegisterInput("Temperature")    
.WithLookupTable(/* 熱電偶查表 */);
// 定義警報條件
engine.DefineFormula("HighPressureAlarm")    
.WithExpression("Pressure > 10");

engine.DefineFormula("OverTempAlarm")    
.WithExpression("Temperature > 80");

engine.DefineFormula("CriticalAlarm")    
.WithExpression("HighPressureAlarm AND OverTempAlarm");
```

### 3. 能源計算範例

```csharp
// 電力參數
engine.RegisterInput("Voltage");
engine.RegisterInput("Current");
engine.RegisterInput("PowerFactor");
// 功率計算
engine.DefineFormula("ActivePower")
.WithExpression("Voltage * Current * PowerFactor")
.WithDecimalPlaces(2);
// 能源累積（需要時間整合功能）
engine.DefineFormula("EnergyConsumption")    
.WithExpression("Integrate(ActivePower, 3600)") 
// kWh    .WithDecimalPlaces(3);
```

## 開發指引

### 1. 專案結構

```
Ngp.Calculation.LogicEngine/
├── Core/
│   ├── Engine.cs
│   ├── FormulaParser.cs
│   ├── Calculator.cs
│   └── Cache.cs
├── Models/
│   ├── Formula.cs
│   ├── Input.cs
│   └── LookupTable.cs
├── Events/
│   └── EventArgs/
├── Builders/
│   ├── EngineBuilder.cs
│   ├── FormulaBuilder.cs
│   └── InputBuilder.cs
├── Functions/
│   ├── MathFunctions.cs
│   ├── StatisticsFunctions.cs
│   └── TimeFunctions.cs
└── Interfaces/
    ├── ILogicEngine.cs
    ├── IFormula.cs
    └── IFunction.cs
```

### 2. 關鍵介面

```csharp
public interface ILogicEngine
{    
	void RegisterInput(string inputId);    
	void DefineFormula(string formulaId);    
	void UpdateInput(string inputId, object value);    
	void Start();    
	void Stop();    
	event EventHandler OutputChanged;    
	event EventHandler CalculationError;
}
```

這個設計完全解耦，可作為獨立的函式庫被任何專案使用，透過FluentAPI接收輸入，透過事件委派輸出結果。