## 智慧家庭整合

### 注意事項

1. [使用.NET](http://使用.NET) 9開發。
2. 符合企業級等級的穩定性及運行性能。
3. 使用Ngp.Communication.WeemaSmartHomeHub專案。
4. 使用Minimal API不要用Controller。
5. 在程式中加入簡易的英文註解。
6. 選擇Best Practice。
7. 源能夠正確回收，正常運作或異常時，連線埠均能正確關閉。
8. 提供FluentAPI接口。
9. 可以Graceful Exit。
10. 高平行化設計，避免同步IO阻塞。
11. 完善Thread-Safe功能，優先使用SemaphoreSlim而不是lock，並且主機避免死鎖。
12. 使用高性能的連線管理機制。
13. 不要使用Console.WriteLine，請使用ILogger，並合理配置不同的Log等級
14. 系統中使用的數值型別使用要精準，例：應該是ushort就不要定義成int。
15. 所有的事件委派請採用EventHandler<TEventArgs>。
16. 請選擇開源，高性能，且與中國無關的MQTT方案。

### 通訊要求

1. 透過MQTT傳送/接收 WeemaSmartHome的IOT裝置狀態及數值，包含以下：
    1. 智慧插座：
        1. 電壓
        2. 電流
        3. 實功率KW
        4. 虛功率KVAR
        5. 累計KWH
        6. 溫度-1
        7. 溫度-2
        8. 功率因素
        9. 頻率
        10. 開關狀態(On-Off)/需可控制
        11. 溫度上限/需可控制
        12. 電流上限/需可控制
    2. IAQ空氣品質偵測器：
        1. 溫度
        2. 濕度
        3. CO
        4. CO2
        5. PM2.5
        6. PM10
        7. HCHO
        8. TVOC
    3. 照度計：
        1. 照度
        2. 智慧燈控：
        3. 電壓
        4. 電流
        5. 實功率KW
        6. 虛功率KVAR
        7. 累計KWH
        8. 溫度-1
        9. 溫度-2
        10. 功率因素
        11. 頻率
        12. 開關狀態(On-Off)/需可控制
        13. 燈光亮度(0-24)/需可控制

### 通訊要求

1. 平行化處理，在實際使用時提供處理2萬組WeemaSmartHome的性能(每組包含50智慧插座或智慧燈控，25個照度計，25個IAQ)。
2. 請協助選擇並安裝MQTT Broker。