### 節點系統

### 注意事項

1. [使用.NET](http://使用.NET) 9 搭配EntityFramework + PostgreSQL。
2. 採用Code First Migration。
3. Migration目錄請放在Data\Migrations\AppDbContext，以應付日後新增其它DbContext。
4. Context請使用AppDbContext，Primary Key請避免使用UUID。
5. 需使用TimescaleDB功能。
6. 採用IOC解偶合，單專案分層方案，使用Mediator.SourceGenerator CQRS。
7. 類別要遵守SRP原則。
8. Enum要定義成PostgreSQL的Enum Type。
9. 符合企業級等級的穩定性及運行性能。
10. 如有需要，使用Minimal API不要用Controller。
11. 在程式中加入簡易的英文註解。
12. 選擇Best Practice。
13. 高平行化設計，避免同步IO阻塞。
14. 完善Thread-Safe功能，優先使用SemaphoreSlim而不是lock，並且避免鎖死的狀況產生。
15. 不要使用Console.WriteLine，請使用ILogger，並合理配置不同的Log等級
16. 系統中使用的數值型別使用要精準，例：應該是ushort就不要定義成int。
17. 所有的事件委派請採用EventHandler<TEventArgs>。
18. 一般導覽屬性需為virtual，可空型別。
19. 集合型導覽屬性需為virtual，ICollection<T>，預設null!。
20. DateTime類屬性需避免傳入空值時被代入預設值，導致邏輯錯誤。
21. String請預設空字串(string.Empty)，避免null相關的處理。
22. EF相關的定義如長度，型別等，盡量透過Fluent API定義。

### 功能需求

1. Node應包含至少以下欄位：
    1. IsEnabled
    2. IsLogEnabled
    3. IsFailureNode
    4. DisplayRuntime
    5. DecimalPlaces
    6. Unit
    7. Scale
2. 提供節點狀態與更新時間的API，此API必須迅速，請妥善運用Cache。
3. 每次系統啟動時，需先從資料庫裡取得先前儲存的狀態，然後才會使用其它系統做輪詢。
4. 節點狀態更新時，先進快取，再進資料庫，以增加節點更新的吞吐量。
5. 任何時候存取節點狀態，必使用快取，除了系統啟動時的第一次。
6. 提供強制變更節點狀態的API。
7. 規劃「Data Path」，類似「\\SiteA\BuildingB\FloorC\MeterA\ReadingC」概念，需可靈活更改配置增加或減少階層數。
8. 「Data Path」應能支援中文，並避免任何空白。
9. 需至少存在Boolean，Analog，String三種節點，以及使用者自定義的進階節點類別(多數值)。
10. 查表功能，由節點原有之狀態，從一清單中查詢對應字串。
11. 查表功能需另外提供一張表供儲存，以JsonDocument儲存為jsonb格式。
12. 允許使用者自定義進階節點類別，至少需包含：三相電表，水表，空氣品質偵測器等，不要寫死的，需可以靈活調整自定義進階節點類別，包含哪些讀值，以及選擇要做為歷史紀錄紀錄下來的讀值，單一點位應最多只有一個數值欄位供儲存歷史紀錄。
13. 使用者自定義的進階節點類別，需以樣板型式儲存下來提供下次使用。
14. 自定義一支使用FasterKV的Cache系統，不需要持久化，也不需過期設定，且預留為日後可替換成Redis的設計。
15. 提供所相關功能之基本的CRUD功能如：節點管理，自定義節點類別管理，查表內容管理等等。
16. DataBase Migration請不要靠額外的SQL檔，要在dotnet ef的migration裡運行。

### 歷史紀錄系統

1. 使用NodeId與時間做為主鍵，無需額外主鍵。
2. 歷史紀錄只需紀錄數值，需包含至少以下兩種模式：
    1. 累計使用量(數值)：透過即時收到的電表的累計值或水表的累計值或其它感測器數值，紀錄每小時四筆(每小時的0分,15分,30分,45分)紀錄，此紀錄邏輯固定即可。
    2. 瞬時變化值(數值)：透過即時收到的空氣品質或其它感測器數值，紀錄一個可繪製出趨勢圖表的歷史紀錄，除定時紀錄之外，有特別大的變化時也應該被紀錄下來，這個Thredshold等參數需要可以被調整。
    3. 狀態變化紀錄(布林)。
3. 布林與數值的歷史紀錄請分開不同表，並且字串不需要歷史紀錄。
4. 需提供一支API供使用者查詢以下情境
    1. 可選擇多組裝置。
    2. 選擇單一日期之後，查詢對應裝置的當天的每小時用量，當月的每天用量，該年度的每月用量，以及歷年的每年用量。
5. 為求資料量精簡，不要紀錄Trigger/DataQuality/Context/RecordMode等資訊。
6. 歷史紀錄需對應NodeId，而不是要對應自定義節點的Id。

### 性能要求

1. 避免所有N+1 Queries。
2. Data Path產樹與檢索性能必須要強。
3. 需避免節點大量更新時吞吐量問題，節點狀態皆先進入快取系統，然後再持久化(可使用Consumer-Producer/Channel等Pattern解決)。
4. 歷史紀錄規劃需有良好規劃，以適應可能達百萬等級的裝置數量。
5. 歷史紀錄需紀錄大量資料，避免資料庫佔用過大空間，需精簡欄位使用。

### 測試場景

1. 請於Ngp專案實作一些範例。
2. 測試伺服器：IP: ***************，帳號：postgres，密碼：postgres，資料庫：Ngp。
3. 請套用Db Migration至上述伺服器，如果目標資料庫已存在，請Drop it再套用Migration。