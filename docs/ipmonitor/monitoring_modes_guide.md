# IP監控系統 - 三種監控模式切換指南

## 📋 監控模式概述

IP監控系統提供三種不同的監控模式，每種模式適用於不同的監控場景：

### 1. 即時模式 (Immediate Mode)
- **特點**: 任何成功或失敗都立即反映狀態變化
- **適用場景**: 關鍵服務監控、需要即時反應的系統
- **優點**: 反應速度快，無延遲
- **缺點**: 可能因網路抖動產生誤報

### 2. 一般模式 (Normal Mode)
- **特點**: 提供重試機制來確認狀態變化
- **適用場景**: 生產環境的穩定監控
- **優點**: 減少誤報，提供穩定的監控結果
- **缺點**: 狀態變化有一定延遲

### 3. 簡易模式 (Simple Mode)
- **特點**: 在時間窗口內有任何回應就算在線
- **適用場景**: 基本的服務可用性檢查、大範圍IP掃描
- **優點**: 容錯性高，適合不穩定的網路環境
- **缺點**: 精確度較低

## 🔄 模式切換方法

### 方法1: 使用Factory預設方法

```csharp
// 即時模式 - 高性能監控
var immediateMonitor = IpMonitorFactory.CreateHighPerformance(
    "immediate-monitor", 
    new[] { "*******", "*******" }, 
    loggerFactory);

// 一般模式 - 生產環境監控
var normalMonitor = IpMonitorFactory.CreateProduction(
    "normal-monitor", 
    new[] { "*******", "*******" }, 
    loggerFactory);

// 簡易模式 - 基本可用性檢查
var simpleMonitor = IpMonitorFactory.CreateSimpleMode(
    "simple-monitor", 
    new[] { "*******", "*******" }, 
    300000, // 5分鐘維持時間
    loggerFactory);
```

### 方法2: 使用Builder自定義配置

```csharp
// 即時模式配置
var immediateMonitor = IpMonitorFactory.Create(loggerFactory)
    .WithId("custom-immediate")
    .WithMonitoringMode(MonitoringMode.Immediate)
    .WithDetectionMethod(DetectionMethod.IcmpPing)
    .WithMaxConcurrency(20)
    .WithTimeout(3000)
    .WithPollingInterval(1000)  // 1秒輪詢
    .AddIpAddresses(ipAddresses)
    .Build();

// 一般模式配置
var normalMonitor = IpMonitorFactory.Create(loggerFactory)
    .WithId("custom-normal")
    .WithMonitoringMode(MonitoringMode.Normal)
    .WithDetectionMethod(DetectionMethod.IcmpPing)
    .WithMaxConcurrency(50)
    .WithTimeout(5000)
    .WithPollingInterval(2000)  // 2秒輪詢
    .WithRetryPolicy(3, 1000)   // 3次重試，間隔1秒
    .WithConsecutiveSuccessCount(2)  // 連續2次成功確認上線
    .AddIpAddresses(ipAddresses)
    .Build();

// 簡易模式配置
var simpleMonitor = IpMonitorFactory.Create(loggerFactory)
    .WithId("custom-simple")
    .WithMonitoringMode(MonitoringMode.Simple)
    .WithDetectionMethod(DetectionMethod.IcmpPing)
    .WithMaxConcurrency(25)
    .WithTimeout(5000)
    .WithPollingInterval(10000)  // 10秒輪詢
    .WithMaintenanceTime(600000)  // 10分鐘維持時間
    .AddIpAddresses(ipAddresses)
    .Build();
```

### 方法3: 使用HTTP API

#### 創建即時模式監控器
```bash
POST /api/ipmonitor/monitors/immediate
Content-Type: application/json

{
  "monitorId": "immediate-monitor",
  "ipAddresses": ["*******", "*******", "***********"]
}
```

#### 創建一般模式監控器
```bash
POST /api/ipmonitor/monitors/normal
Content-Type: application/json

{
  "monitorId": "normal-monitor",
  "ipAddresses": ["*******", "*******", "***********"],
  "retryCount": 3,
  "retryDelayMs": 1000,
  "consecutiveSuccessCount": 2
}
```

#### 創建簡易模式監控器
```bash
POST /api/ipmonitor/monitors/simple
Content-Type: application/json

{
  "monitorId": "simple-monitor",
  "ipAddresses": ["*******", "*******", "***********"],
  "maintenanceTimeMs": 300000
}
```

## 📊 模式參數說明

### 即時模式參數
- `PollingInterval`: 輪詢間隔（建議: 500-1000ms）
- `MaxConcurrency`: 最大並行數（建議: 10-50）
- `Timeout`: Ping超時時間（建議: 3000-5000ms）

### 一般模式參數
- `RetryCount`: 重試次數（預設: 3次）
- `RetryDelayMs`: 重試間隔（預設: 1000ms）
- `ConsecutiveSuccessCount`: 連續成功次數（預設: 2次）
- `PollingInterval`: 輪詢間隔（建議: 2000-5000ms）

### 簡易模式參數
- `MaintenanceTimeMs`: 維持時間窗口（預設: 300000ms = 5分鐘）
- `PollingInterval`: 輪詢間隔（建議: 5000-30000ms）

## 🎯 場景選擇建議

### 即時模式適用場景
- 關鍵業務系統監控
- 負載均衡器健康檢查
- 實時服務狀態監控
- 需要快速故障切換的系統

### 一般模式適用場景
- 生產環境服務監控
- 網路設備狀態監控
- 伺服器可用性監控
- 需要穩定監控結果的場景

### 簡易模式適用場景
- 大範圍IP掃描
- 基本網路連通性檢查
- 不穩定網路環境監控
- 資源受限的監控場景

## 🔄 動態模式切換

由於監控模式是在建立監控器時設定的，要切換模式需要：

1. 停止現有監控器
2. 建立新的監控器使用不同模式
3. 啟動新監控器

```csharp
// 停止現有監控器
await currentMonitor.StopAsync();
currentMonitor.Dispose();

// 建立新模式監控器
var newMonitor = IpMonitorFactory.Create(loggerFactory)
    .WithId("switched-monitor")
    .WithMonitoringMode(MonitoringMode.Normal)  // 切換到一般模式
    .AddIpAddresses(ipAddresses)
    .Build();

// 啟動新監控器
await newMonitor.StartAsync();
```

## 📈 監控模式效果比較

| 模式 | 反應速度 | 穩定性 | 資源消耗 | 誤報率 | 適用場景 |
|------|----------|--------|----------|--------|----------|
| 即時 | ⭐⭐⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐ | 關鍵系統 |
| 一般 | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 生產環境 |
| 簡易 | ⭐⭐ | ⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐ | 基本檢查 |

選擇合適的監控模式可以大幅提升監控系統的效果和穩定性！
