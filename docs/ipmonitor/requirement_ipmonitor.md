### 注意事項

1. [使用.NET](http://使用.NET) 9開發。
2. 符合企業級等級的穩定性及運行性能。
3. 使用Ngp.Communication.IpMonitor專案。
4. 使用Minimal API不要用Controller。
5. 在程式中加入簡易的英文註解。
6. 選擇Best Practice。
7. 源能夠正確回收，正常運作或異常時，連線埠均能正確關閉。
8. 提供FluentAPI接口。
9. 可以Graceful Exit。
10. 高平行化設計，避免同步IO阻塞。
11. 完善Thread-Safe功能，優先使用SemaphoreSlim而不是lock，並且避免鎖死的狀況產生。
12. 使用高性能的連線管理機制。
13. 不要使用Console.WriteLine，請使用ILogger，並合理配置不同的Log等級
14. 系統中使用的數值型別使用要精準，例：應該是ushort就不要定義成int。
15. 所有的事件委派請採用EventHandler<TEventArgs>。
16. 重要：事件委派邏輯以及Fluent API接口，請參照ModbusTcpMaster或TwentyFourDioPoller專案，做類似的設計。

### 自動輪詢與事件邏輯

1. 透過Fluent API創建實例及設定需要監控的IP清單。
2. 不停的重複對於IP清單內的IP做監控，確認是否離線，如果狀態有改變，則透過事件委派送出訊號。
3. 提供以下幾種IP清單提供方式：
    1. 單一IP。
    2. IP區間，例如：***********~*************或 ***********~***************。
4. 提供以下離線判斷模式：
    1. 即時：任何的成功與失敗都即時反應。
    2. 一般：如果這個IP，原來的狀態是有連線的，那麼監控訊號失敗時提供合理重試機制，確認他是否真的由失敗變為離線，反之，如果這個IP一開始是離線的，現在變為連線，也要連續成功才能算真的成功。
    3. 簡易：設定一定的時間，在這時間內只要有回應過，就算有在線上，其它的失敗都可以無視，從上一次成功的時間開始計算，到下一次的時間到之前，如果都沒反應就算離線。
5. 提供以下幾種偵測方式：
    1. ICMP (Ping)
    2. ARP Ping
    3. fping (only on linux)
6. 提供以下參數供調整，包含但不限於：
    1. 最大併行數量(使用類似SemaphoreSlim的方式限流)。
    2. Timeout時間 (Ping的Timeout)。
    3. 維持時間(簡易模式判斷是否在線上的時間，例如5分鐘，表示只要5分鐘內他有一次回應就算在線上)。
    4. 重試次數，提供給一般模式使用。

---