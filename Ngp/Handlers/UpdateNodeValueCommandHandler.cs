using Mediator;
using Microsoft.EntityFrameworkCore;
using Ngp.Commands;
using Ngp.Data;
using Ngp.Entities;
using Ngp.Entities.Enums;
using Ngp.Services.Interfaces;

namespace Ngp.Handlers;

/// <summary>
/// Handler for updating node values
/// Implements cache-first, database-second pattern for high throughput
/// </summary>
public class UpdateNodeValueCommandHandler : ICommandHandler<UpdateNodeValueCommand, UpdateNodeValueResult>
{
    private readonly AppDbContext _context;
    private readonly INodeCacheService _cacheService;
    private readonly ILogger<UpdateNodeValueCommandHandler> _logger;
    private readonly SemaphoreSlim _semaphore;

    public UpdateNodeValueCommandHandler(
        AppDbContext context,
        INodeCacheService cacheService,
        ILogger<UpdateNodeValueCommandHandler> logger)
    {
        _context = context;
        _cacheService = cacheService;
        _logger = logger;
        _semaphore = new SemaphoreSlim(1, 1);
    }

    public async ValueTask<UpdateNodeValueResult> Handle(UpdateNodeValueCommand command, CancellationToken cancellationToken)
    {
        try
        {
            // Get current node state from cache first
            var currentState = await _cacheService.GetNodeStateAsync(command.NodeId, cancellationToken);
            var previousValue = currentState?.Value;
            
            // Check if value actually changed (unless force update is requested)
            if (!command.ForceUpdate && currentState != null && currentState.Value == command.Value)
            {
                return new UpdateNodeValueResult
                {
                    Success = true,
                    ValueChanged = false,
                    PreviousValue = previousValue
                };
            }

            // Update cache first for immediate availability
            var newState = new NodeCacheState
            {
                Value = command.Value,
                IsGoodQuality = command.IsGoodQuality,
                LastUpdated = command.Timestamp ?? DateTime.UtcNow
            };

            await _cacheService.SetNodeStateAsync(command.NodeId, newState, cancellationToken);

            // Update database asynchronously (fire and forget for performance)
            _ = Task.Run(async () =>
            {
                await UpdateDatabaseAsync(command, cancellationToken);
            }, cancellationToken);

            _logger.LogDebug("Node value updated in cache: {NodeId} = {Value}", command.NodeId, command.Value);

            return new UpdateNodeValueResult
            {
                Success = true,
                ValueChanged = true,
                PreviousValue = previousValue
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating node value for {NodeId}", command.NodeId);
            
            return new UpdateNodeValueResult
            {
                Success = false,
                ErrorMessage = ex.Message,
                ValueChanged = false
            };
        }
    }

    private async Task UpdateDatabaseAsync(UpdateNodeValueCommand command, CancellationToken cancellationToken)
    {
        await _semaphore.WaitAsync(cancellationToken);
        try
        {
            var node = await _context.Nodes
                .FirstOrDefaultAsync(n => n.NodeId == command.NodeId, cancellationToken);

            if (node == null)
            {
                _logger.LogWarning("Node not found in database: {NodeId}", command.NodeId);
                return;
            }

            // Update node in database
            node.CurrentValue = command.Value;
            node.IsGoodQuality = command.IsGoodQuality;
            node.LastUpdated = command.Timestamp ?? DateTime.UtcNow;
            node.UpdatedAt = DateTime.UtcNow;

            // Add history record if enabled
            if (node.EnableHistoryRecording && node.HistoryRecordMode.HasValue)
            {
                await AddHistoryRecordAsync(node, command.Value, command.Timestamp ?? DateTime.UtcNow, cancellationToken);
            }

            await _context.SaveChangesAsync(cancellationToken);
            
            _logger.LogDebug("Node value updated in database: {NodeId}", command.NodeId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating node value in database for {NodeId}", command.NodeId);
        }
        finally
        {
            _semaphore.Release();
        }
    }

    private async Task AddHistoryRecordAsync(Node node, string value, DateTime timestamp, CancellationToken cancellationToken)
    {
        try
        {
            switch (node.NodeType)
            {
                case NodeType.Boolean:
                    if (bool.TryParse(value, out var boolValue))
                    {
                        var boolRecord = new BooleanHistoryRecord
                        {
                            NodeId = node.Id,
                            Timestamp = timestamp,
                            Value = boolValue
                        };
                        _context.BooleanHistoryRecords.Add(boolRecord);
                    }
                    break;

                case NodeType.Analog:
                    if (double.TryParse(value, out var doubleValue))
                    {
                        var numericRecord = new NumericHistoryRecord
                        {
                            NodeId = node.Id,
                            Timestamp = timestamp,
                            Value = doubleValue,
                            RecordMode = node.HistoryRecordMode!.Value
                        };
                        _context.NumericHistoryRecords.Add(numericRecord);
                    }
                    break;

                // String values are not recorded in history as per requirements
                case NodeType.String:
                case NodeType.Custom:
                default:
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding history record for node {NodeId}", node.NodeId);
        }
    }
}
