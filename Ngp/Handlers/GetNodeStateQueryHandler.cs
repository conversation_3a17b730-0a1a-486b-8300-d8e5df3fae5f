using Mediator;
using Microsoft.EntityFrameworkCore;
using Ngp.Data;
using Ngp.Queries;
using Ngp.Services.Interfaces;
using System.Text.Json;

namespace Ngp.Handlers;

/// <summary>
/// Handler for getting node state queries
/// Implements cache-first strategy for optimal performance
/// </summary>
public class GetNodeStateQueryHandler : 
    I<PERSON>ueryHandler<GetNodeStateQuery, NodeStateResult?>,
    IQueryHandler<GetMultipleNodeStatesQuery, Dictionary<string, NodeStateResult>>
{
    private readonly AppDbContext _context;
    private readonly INodeCacheService _cacheService;
    private readonly ILogger<GetNodeStateQueryHandler> _logger;

    public GetNodeStateQueryHandler(
        AppDbContext context,
        INodeCacheService cacheService,
        ILogger<GetNodeStateQueryHandler> logger)
    {
        _context = context;
        _cacheService = cacheService;
        _logger = logger;
    }

    public async ValueTask<NodeStateResult?> Handle(GetNodeStateQuery query, CancellationToken cancellationToken)
    {
        try
        {
            // Try cache first unless forced to use database
            if (!query.ForceFromDatabase)
            {
                var cachedState = await _cacheService.GetNodeStateAsync(query.NodeId, cancellationToken);
                if (cachedState != null)
                {
                    // Get additional node information from database for complete result
                    var nodeInfo = await GetNodeInfoFromDatabaseAsync(query.NodeId, cancellationToken);
                    if (nodeInfo != null)
                    {
                        return CreateNodeStateResult(nodeInfo, cachedState.Value, cachedState.IsGoodQuality, cachedState.LastUpdated);
                    }
                }
            }

            // Fallback to database
            return await GetNodeStateFromDatabaseAsync(query.NodeId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting node state for {NodeId}", query.NodeId);
            return null;
        }
    }

    public async ValueTask<Dictionary<string, NodeStateResult>> Handle(GetMultipleNodeStatesQuery query, CancellationToken cancellationToken)
    {
        var results = new Dictionary<string, NodeStateResult>();

        try
        {
            var nodeIds = query.NodeIds.ToList();
            
            if (!query.ForceFromDatabase)
            {
                // Try to get states from cache first
                var cachedStates = await _cacheService.GetMultipleNodeStatesAsync(nodeIds, cancellationToken);
                
                // Get node information from database for all nodes
                var nodeInfos = await GetMultipleNodeInfoFromDatabaseAsync(nodeIds, cancellationToken);
                
                foreach (var nodeId in nodeIds)
                {
                    if (cachedStates.TryGetValue(nodeId, out var cachedState) && 
                        nodeInfos.TryGetValue(nodeId, out var nodeInfo))
                    {
                        results[nodeId] = CreateNodeStateResult(nodeInfo, cachedState.Value, cachedState.IsGoodQuality, cachedState.LastUpdated);
                    }
                }
                
                // For nodes not found in cache, get from database
                var missingNodeIds = nodeIds.Except(results.Keys).ToList();
                if (missingNodeIds.Any())
                {
                    var dbResults = await GetMultipleNodeStatesFromDatabaseAsync(missingNodeIds, cancellationToken);
                    foreach (var kvp in dbResults)
                    {
                        results[kvp.Key] = kvp.Value;
                    }
                }
            }
            else
            {
                // Force database query
                results = await GetMultipleNodeStatesFromDatabaseAsync(nodeIds, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting multiple node states");
        }

        return results;
    }

    private async Task<NodeInfo?> GetNodeInfoFromDatabaseAsync(string nodeId, CancellationToken cancellationToken)
    {
        return await _context.Nodes
            .Include(n => n.DataPath)
            .Include(n => n.LookupTable)
            .Where(n => n.NodeId == nodeId)
            .Select(n => new NodeInfo
            {
                NodeId = n.NodeId,
                Name = n.Name,
                NodeType = n.NodeType,
                IsEnabled = n.IsEnabled,
                IsFailureNode = n.IsFailureNode,
                Unit = n.Unit,
                Scale = n.Scale,
                DecimalPlaces = n.DecimalPlaces,
                DataPath = n.DataPath != null ? n.DataPath.FullPath : null,
                LookupMappings = n.LookupTable != null ? n.LookupTable.LookupMappings : null,
                DefaultLookupText = n.LookupTable != null ? n.LookupTable.DefaultText : null
            })
            .FirstOrDefaultAsync(cancellationToken);
    }

    private async Task<Dictionary<string, NodeInfo>> GetMultipleNodeInfoFromDatabaseAsync(IEnumerable<string> nodeIds, CancellationToken cancellationToken)
    {
        var nodeInfos = await _context.Nodes
            .Include(n => n.DataPath)
            .Include(n => n.LookupTable)
            .Where(n => nodeIds.Contains(n.NodeId))
            .Select(n => new NodeInfo
            {
                NodeId = n.NodeId,
                Name = n.Name,
                NodeType = n.NodeType,
                IsEnabled = n.IsEnabled,
                IsFailureNode = n.IsFailureNode,
                Unit = n.Unit,
                Scale = n.Scale,
                DecimalPlaces = n.DecimalPlaces,
                DataPath = n.DataPath != null ? n.DataPath.FullPath : null,
                LookupMappings = n.LookupTable != null ? n.LookupTable.LookupMappings : null,
                DefaultLookupText = n.LookupTable != null ? n.LookupTable.DefaultText : null
            })
            .ToListAsync(cancellationToken);

        return nodeInfos.ToDictionary(n => n.NodeId, n => n);
    }

    private async Task<NodeStateResult?> GetNodeStateFromDatabaseAsync(string nodeId, CancellationToken cancellationToken)
    {
        var node = await _context.Nodes
            .Include(n => n.DataPath)
            .Include(n => n.LookupTable)
            .FirstOrDefaultAsync(n => n.NodeId == nodeId, cancellationToken);

        if (node == null)
            return null;

        var nodeInfo = new NodeInfo
        {
            NodeId = node.NodeId,
            Name = node.Name,
            NodeType = node.NodeType,
            IsEnabled = node.IsEnabled,
            IsFailureNode = node.IsFailureNode,
            Unit = node.Unit,
            Scale = node.Scale,
            DecimalPlaces = node.DecimalPlaces,
            DataPath = node.DataPath?.FullPath,
            LookupMappings = node.LookupTable?.LookupMappings,
            DefaultLookupText = node.LookupTable?.DefaultText
        };

        return CreateNodeStateResult(nodeInfo, node.CurrentValue, node.IsGoodQuality, node.LastUpdated);
    }

    private async Task<Dictionary<string, NodeStateResult>> GetMultipleNodeStatesFromDatabaseAsync(IEnumerable<string> nodeIds, CancellationToken cancellationToken)
    {
        var nodes = await _context.Nodes
            .Include(n => n.DataPath)
            .Include(n => n.LookupTable)
            .Where(n => nodeIds.Contains(n.NodeId))
            .ToListAsync(cancellationToken);

        var results = new Dictionary<string, NodeStateResult>();

        foreach (var node in nodes)
        {
            var nodeInfo = new NodeInfo
            {
                NodeId = node.NodeId,
                Name = node.Name,
                NodeType = node.NodeType,
                IsEnabled = node.IsEnabled,
                IsFailureNode = node.IsFailureNode,
                Unit = node.Unit,
                Scale = node.Scale,
                DecimalPlaces = node.DecimalPlaces,
                DataPath = node.DataPath?.FullPath,
                LookupMappings = node.LookupTable?.LookupMappings,
                DefaultLookupText = node.LookupTable?.DefaultText
            };

            results[node.NodeId] = CreateNodeStateResult(nodeInfo, node.CurrentValue, node.IsGoodQuality, node.LastUpdated);
        }

        return results;
    }

    private static NodeStateResult CreateNodeStateResult(NodeInfo nodeInfo, string currentValue, bool isGoodQuality, DateTime lastUpdated)
    {
        var result = new NodeStateResult
        {
            NodeId = nodeInfo.NodeId,
            Name = nodeInfo.Name,
            NodeType = nodeInfo.NodeType,
            CurrentValue = currentValue,
            IsGoodQuality = isGoodQuality,
            LastUpdated = lastUpdated,
            IsEnabled = nodeInfo.IsEnabled,
            IsFailureNode = nodeInfo.IsFailureNode,
            Unit = nodeInfo.Unit,
            Scale = nodeInfo.Scale,
            DecimalPlaces = nodeInfo.DecimalPlaces,
            DataPath = nodeInfo.DataPath
        };

        // Apply lookup table mapping if available
        if (nodeInfo.LookupMappings != null)
        {
            result.MappedValue = GetMappedValue(currentValue, nodeInfo.LookupMappings, nodeInfo.DefaultLookupText);
        }

        return result;
    }

    private static string? GetMappedValue(string value, JsonDocument lookupMappings, string? defaultText)
    {
        try
        {
            if (lookupMappings.RootElement.TryGetProperty("mappings", out var mappingsElement))
            {
                foreach (var mapping in mappingsElement.EnumerateArray())
                {
                    if (mapping.TryGetProperty("value", out var mappingValue) &&
                        mapping.TryGetProperty("text", out var mappingText))
                    {
                        if (mappingValue.GetString() == value)
                        {
                            return mappingText.GetString();
                        }
                    }
                }
            }
        }
        catch (Exception)
        {
            // Ignore JSON parsing errors
        }

        return defaultText ?? value;
    }

    private class NodeInfo
    {
        public string NodeId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public Entities.Enums.NodeType NodeType { get; set; }
        public bool IsEnabled { get; set; }
        public bool IsFailureNode { get; set; }
        public string Unit { get; set; } = string.Empty;
        public double Scale { get; set; }
        public byte DecimalPlaces { get; set; }
        public string? DataPath { get; set; }
        public JsonDocument? LookupMappings { get; set; }
        public string? DefaultLookupText { get; set; }
    }
}
