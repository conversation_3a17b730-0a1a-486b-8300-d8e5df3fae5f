using Mediator;
using Ngp.Entities.Enums;

namespace Ngp.Queries;

/// <summary>
/// Query to get current state of a node
/// </summary>
public class GetNodeStateQuery : IQuery<NodeStateResult?>
{
    /// <summary>
    /// Node identifier
    /// </summary>
    public required string NodeId { get; set; }
    
    /// <summary>
    /// Whether to force reading from database instead of cache
    /// </summary>
    public bool ForceFromDatabase { get; set; } = false;
}

/// <summary>
/// Query to get states of multiple nodes
/// </summary>
public class GetMultipleNodeStatesQuery : IQuery<Dictionary<string, NodeStateResult>>
{
    /// <summary>
    /// Collection of node identifiers
    /// </summary>
    public required IEnumerable<string> NodeIds { get; set; }
    
    /// <summary>
    /// Whether to force reading from database instead of cache
    /// </summary>
    public bool ForceFromDatabase { get; set; } = false;
}

/// <summary>
/// Result containing node state information
/// </summary>
public class NodeStateResult
{
    /// <summary>
    /// Node identifier
    /// </summary>
    public string NodeId { get; set; } = string.Empty;
    
    /// <summary>
    /// Display name of the node
    /// </summary>
    public string Name { get; set; } = string.Empty;
    
    /// <summary>
    /// Type of the node
    /// </summary>
    public NodeType NodeType { get; set; }
    
    /// <summary>
    /// Current value of the node
    /// </summary>
    public string CurrentValue { get; set; } = string.Empty;
    
    /// <summary>
    /// Quality indicator for the current value
    /// </summary>
    public bool IsGoodQuality { get; set; } = true;
    
    /// <summary>
    /// Timestamp when the value was last updated
    /// </summary>
    public DateTime LastUpdated { get; set; }
    
    /// <summary>
    /// Whether the node is enabled
    /// </summary>
    public bool IsEnabled { get; set; }
    
    /// <summary>
    /// Whether this is a failure node
    /// </summary>
    public bool IsFailureNode { get; set; }
    
    /// <summary>
    /// Unit of measurement
    /// </summary>
    public string Unit { get; set; } = string.Empty;
    
    /// <summary>
    /// Scale factor
    /// </summary>
    public double Scale { get; set; }
    
    /// <summary>
    /// Number of decimal places for display
    /// </summary>
    public byte DecimalPlaces { get; set; }
    
    /// <summary>
    /// Full data path of the node
    /// </summary>
    public string? DataPath { get; set; }
    
    /// <summary>
    /// Mapped value from lookup table (if applicable)
    /// </summary>
    public string? MappedValue { get; set; }
}
