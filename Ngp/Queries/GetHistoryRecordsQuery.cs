using Mediator;
using Ngp.Entities.Enums;

namespace Ngp.Queries;

/// <summary>
/// Query to get history records for nodes
/// </summary>
public class GetHistoryRecordsQuery : IQuery<HistoryRecordsResult>
{
    /// <summary>
    /// Collection of node identifiers
    /// </summary>
    public required IEnumerable<string> NodeIds { get; set; }
    
    /// <summary>
    /// Start date for the query
    /// </summary>
    public required DateTime StartDate { get; set; }
    
    /// <summary>
    /// End date for the query
    /// </summary>
    public required DateTime EndDate { get; set; }
    
    /// <summary>
    /// Aggregation type for the results
    /// </summary>
    public HistoryAggregationType AggregationType { get; set; } = HistoryAggregationType.Raw;
    
    /// <summary>
    /// Maximum number of records to return
    /// </summary>
    public int MaxRecords { get; set; } = 1000;
}

/// <summary>
/// Query to get usage statistics for a specific date
/// </summary>
public class GetUsageStatisticsQuery : IQuery<UsageStatisticsResult>
{
    /// <summary>
    /// Collection of node identifiers
    /// </summary>
    public required IEnumerable<string> NodeIds { get; set; }
    
    /// <summary>
    /// Target date for statistics
    /// </summary>
    public required DateTime TargetDate { get; set; }
    
    /// <summary>
    /// Statistics type (hourly, daily, monthly, yearly)
    /// </summary>
    public StatisticsType StatisticsType { get; set; } = StatisticsType.Hourly;
}

/// <summary>
/// Aggregation type for history records
/// </summary>
public enum HistoryAggregationType
{
    /// <summary>
    /// Raw data without aggregation
    /// </summary>
    Raw = 1,
    
    /// <summary>
    /// Hourly aggregation
    /// </summary>
    Hourly = 2,
    
    /// <summary>
    /// Daily aggregation
    /// </summary>
    Daily = 3,
    
    /// <summary>
    /// Monthly aggregation
    /// </summary>
    Monthly = 4,
    
    /// <summary>
    /// Yearly aggregation
    /// </summary>
    Yearly = 5
}

/// <summary>
/// Statistics type for usage queries
/// </summary>
public enum StatisticsType
{
    /// <summary>
    /// Hourly usage for a day
    /// </summary>
    Hourly = 1,
    
    /// <summary>
    /// Daily usage for a month
    /// </summary>
    Daily = 2,
    
    /// <summary>
    /// Monthly usage for a year
    /// </summary>
    Monthly = 3,
    
    /// <summary>
    /// Yearly usage across years
    /// </summary>
    Yearly = 4
}

/// <summary>
/// Result containing history records
/// </summary>
public class HistoryRecordsResult
{
    /// <summary>
    /// Dictionary of node records (NodeId -> Records)
    /// </summary>
    public Dictionary<string, List<HistoryRecordItem>> Records { get; set; } = new();
    
    /// <summary>
    /// Total number of records returned
    /// </summary>
    public int TotalRecords { get; set; }
    
    /// <summary>
    /// Whether the result was truncated due to MaxRecords limit
    /// </summary>
    public bool IsTruncated { get; set; }
}

/// <summary>
/// Result containing usage statistics
/// </summary>
public class UsageStatisticsResult
{
    /// <summary>
    /// Dictionary of node statistics (NodeId -> Statistics)
    /// </summary>
    public Dictionary<string, List<UsageStatisticItem>> Statistics { get; set; } = new();
    
    /// <summary>
    /// Type of statistics returned
    /// </summary>
    public StatisticsType StatisticsType { get; set; }
    
    /// <summary>
    /// Target date for the statistics
    /// </summary>
    public DateTime TargetDate { get; set; }
}

/// <summary>
/// Individual history record item
/// </summary>
public class HistoryRecordItem
{
    /// <summary>
    /// Timestamp of the record
    /// </summary>
    public DateTime Timestamp { get; set; }
    
    /// <summary>
    /// Value (numeric or boolean)
    /// </summary>
    public object Value { get; set; } = null!;
    
    /// <summary>
    /// Recording mode used
    /// </summary>
    public HistoryRecordMode RecordMode { get; set; }
}

/// <summary>
/// Individual usage statistic item
/// </summary>
public class UsageStatisticItem
{
    /// <summary>
    /// Period identifier (hour, day, month, year)
    /// </summary>
    public string Period { get; set; } = string.Empty;
    
    /// <summary>
    /// Usage value for the period
    /// </summary>
    public double Usage { get; set; }
    
    /// <summary>
    /// Start timestamp of the period
    /// </summary>
    public DateTime PeriodStart { get; set; }
    
    /// <summary>
    /// End timestamp of the period
    /// </summary>
    public DateTime PeriodEnd { get; set; }
}
