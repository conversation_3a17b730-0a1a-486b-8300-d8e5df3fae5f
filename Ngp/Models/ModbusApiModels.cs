using Ngp.Communication.ModbusTcpMaster.Enums;
using Ngp.Communication.AsteriskProxy.Enums;
using System.ComponentModel.DataAnnotations;

namespace Ngp.Models;

/// <summary>
/// Request model for writing a single coil
/// </summary>
public class WriteSingleCoilRequest
{
    /// <summary>
    /// Gets or sets the endpoint identifier
    /// </summary>
    [Required]
    public string EndpointId { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the slave ID
    /// </summary>
    [Range(1, 247)]
    public byte SlaveId { get; set; }

    /// <summary>
    /// Gets or sets the coil address
    /// </summary>
    public ushort Address { get; set; }

    /// <summary>
    /// Gets or sets the value to write
    /// </summary>
    public bool Value { get; set; }
}

/// <summary>
/// Request model for writing a single register
/// </summary>
public class WriteSingleRegisterRequest
{
    /// <summary>
    /// Gets or sets the endpoint identifier
    /// </summary>
    [Required]
    public string EndpointId { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the slave ID
    /// </summary>
    [Range(1, 247)]
    public byte SlaveId { get; set; }

    /// <summary>
    /// Gets or sets the register address
    /// </summary>
    public ushort Address { get; set; }

    /// <summary>
    /// Gets or sets the value to write
    /// </summary>
    public ushort Value { get; set; }
}

/// <summary>
/// Request model for reading a register value
/// </summary>
public class ReadRegisterRequest
{
    /// <summary>
    /// Gets or sets the endpoint identifier
    /// </summary>
    [Required]
    public string EndpointId { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the slave ID
    /// </summary>
    [Range(1, 247)]
    public byte SlaveId { get; set; }

    /// <summary>
    /// Gets or sets the register address
    /// </summary>
    public ushort Address { get; set; }

    /// <summary>
    /// Gets or sets the function code
    /// </summary>
    public ModbusFunction Function { get; set; }
}

/// <summary>
/// Response model for API operations
/// </summary>
public class ApiResponse<T>
{
    /// <summary>
    /// Gets or sets whether the operation was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Gets or sets the response data
    /// </summary>
    public T? Data { get; set; }

    /// <summary>
    /// Gets or sets the error message if operation failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Gets or sets the success message
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// Gets or sets the timestamp of the response
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Creates a successful response
    /// </summary>
    /// <param name="data">Response data</param>
    /// <returns>Successful response</returns>
    public static ApiResponse<T> CreateSuccess(T data)
    {
        return new ApiResponse<T>
        {
            Success = true,
            Data = data
        };
    }

    /// <summary>
    /// Creates a successful response with message
    /// </summary>
    /// <param name="data">Response data</param>
    /// <param name="message">Success message</param>
    /// <returns>Successful response</returns>
    public static ApiResponse<T> CreateSuccess(T data, string message)
    {
        return new ApiResponse<T>
        {
            Success = true,
            Data = data,
            Message = message
        };
    }

    /// <summary>
    /// Creates an error response
    /// </summary>
    /// <param name="errorMessage">Error message</param>
    /// <returns>Error response</returns>
    public static ApiResponse<T> CreateError(string errorMessage)
    {
        return new ApiResponse<T>
        {
            Success = false,
            ErrorMessage = errorMessage
        };
    }
}

/// <summary>
/// Connection status information
/// </summary>
public class ConnectionStatusInfo
{
    /// <summary>
    /// Gets or sets the endpoint identifier
    /// </summary>
    public string EndpointId { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the connection state
    /// </summary>
    public Ngp.Communication.ModbusTcpMaster.Enums.ConnectionState State { get; set; }

    /// <summary>
    /// Gets or sets the state description
    /// </summary>
    public string StateDescription { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets whether the connection is active
    /// </summary>
    public bool IsConnected { get; set; }

    /// <summary>
    /// Gets or sets the last update timestamp
    /// </summary>
    public DateTime LastUpdate { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Register value information
/// </summary>
public class RegisterValueInfo
{
    /// <summary>
    /// Gets or sets the endpoint identifier
    /// </summary>
    public string EndpointId { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the slave ID
    /// </summary>
    public byte SlaveId { get; set; }

    /// <summary>
    /// Gets or sets the register address
    /// </summary>
    public ushort Address { get; set; }

    /// <summary>
    /// Gets or sets the function code
    /// </summary>
    public ModbusFunction Function { get; set; }

    /// <summary>
    /// Gets or sets the raw value as hex string
    /// </summary>
    public string? RawValue { get; set; }

    /// <summary>
    /// Gets or sets the raw value as byte array
    /// </summary>
    public byte[]? RawBytes { get; set; }

    /// <summary>
    /// Gets or sets whether the value is available
    /// </summary>
    public bool IsAvailable { get; set; }

    /// <summary>
    /// Gets or sets the timestamp when the value was last read
    /// </summary>
    public DateTime? LastRead { get; set; }
}

/// <summary>
/// System status information
/// </summary>
public class SystemStatusInfo
{
    /// <summary>
    /// Gets or sets the service status
    /// </summary>
    public string ServiceStatus { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the number of active masters
    /// </summary>
    public int ActiveMasters { get; set; }

    /// <summary>
    /// Gets or sets the connection statuses
    /// </summary>
    public List<ConnectionStatusInfo> Connections { get; set; } = new();

    /// <summary>
    /// Gets or sets the system uptime
    /// </summary>
    public TimeSpan Uptime { get; set; }

    /// <summary>
    /// Gets or sets the last update timestamp
    /// </summary>
    public DateTime LastUpdate { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Asterisk connection status information
/// </summary>
public class AsteriskConnectionStatusInfo
{
    /// <summary>
    /// Gets or sets the endpoint identifier
    /// </summary>
    public string EndpointId { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the connection state
    /// </summary>
    public Ngp.Communication.AsteriskProxy.Enums.ConnectionState State { get; set; }

    /// <summary>
    /// Gets or sets the state description
    /// </summary>
    public string StateDescription { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets whether the connection is active
    /// </summary>
    public bool IsConnected { get; set; }

    /// <summary>
    /// Gets or sets the last update timestamp
    /// </summary>
    public DateTime LastUpdate { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Asterisk system status information
/// </summary>
public class AsteriskSystemStatusInfo
{
    /// <summary>
    /// Gets or sets the service status
    /// </summary>
    public string ServiceStatus { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the number of active proxies
    /// </summary>
    public int ActiveProxies { get; set; }

    /// <summary>
    /// Gets or sets the connection statuses
    /// </summary>
    public List<AsteriskConnectionStatusInfo> Connections { get; set; } = new();

    /// <summary>
    /// Gets or sets the system uptime
    /// </summary>
    public TimeSpan Uptime { get; set; }

    /// <summary>
    /// Gets or sets the last update timestamp
    /// </summary>
    public DateTime LastUpdate { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Extension status information
/// </summary>
public class ExtensionStatusInfo
{
    /// <summary>
    /// Gets or sets the endpoint identifier
    /// </summary>
    public string EndpointId { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the extension number
    /// </summary>
    public string Extension { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the extension context
    /// </summary>
    public string Context { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the extension state
    /// </summary>
    public ExtensionState State { get; set; }

    /// <summary>
    /// Gets or sets the state description
    /// </summary>
    public string StateDescription { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the last updated timestamp
    /// </summary>
    public DateTime LastUpdated { get; set; }

    /// <summary>
    /// Gets or sets the extension tag
    /// </summary>
    public string? Tag { get; set; }

    /// <summary>
    /// Gets or sets the extension description
    /// </summary>
    public string? Description { get; set; }
}
