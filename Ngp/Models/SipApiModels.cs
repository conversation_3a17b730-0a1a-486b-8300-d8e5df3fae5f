using System.ComponentModel.DataAnnotations;

namespace Ngp.Models;

/// <summary>
/// Request model for sending SIP messages
/// </summary>
public class SendMessageRequest
{
    /// <summary>
    /// Gets or sets the target extension
    /// </summary>
    [Required]
    [StringLength(20, MinimumLength = 1)]
    public string ToExtension { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the message content
    /// </summary>
    [Required]
    [StringLength(1000, MinimumLength = 1)]
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the content type (default: text/plain)
    /// </summary>
    [StringLength(100)]
    public string ContentType { get; set; } = "text/plain";
}

/// <summary>
/// Response model for sending SIP messages
/// </summary>
public class SendMessageResponse
{
    /// <summary>
    /// Gets or sets whether the operation was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Gets or sets the message ID for tracking
    /// </summary>
    public string? MessageId { get; set; }

    /// <summary>
    /// Gets or sets the current status
    /// </summary>
    public string? Status { get; set; }

    /// <summary>
    /// Gets or sets the error message if any
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Gets or sets when the message was created
    /// </summary>
    public DateTime? CreatedAt { get; set; }
}

/// <summary>
/// Response model for message status queries
/// </summary>
public class MessageStatusResponse
{
    /// <summary>
    /// Gets or sets whether the operation was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Gets or sets the message ID
    /// </summary>
    public string? MessageId { get; set; }

    /// <summary>
    /// Gets or sets the target extension
    /// </summary>
    public string? ToExtension { get; set; }

    /// <summary>
    /// Gets or sets the message content
    /// </summary>
    public string? Content { get; set; }

    /// <summary>
    /// Gets or sets the content type
    /// </summary>
    public string? ContentType { get; set; }

    /// <summary>
    /// Gets or sets the current status
    /// </summary>
    public string? Status { get; set; }

    /// <summary>
    /// Gets or sets when the message was created
    /// </summary>
    public DateTime? CreatedAt { get; set; }

    /// <summary>
    /// Gets or sets when the message was sent
    /// </summary>
    public DateTime? SentAt { get; set; }

    /// <summary>
    /// Gets or sets when the response was received
    /// </summary>
    public DateTime? ResponseAt { get; set; }

    /// <summary>
    /// Gets or sets the retry count
    /// </summary>
    public int RetryCount { get; set; }

    /// <summary>
    /// Gets or sets the error message if any
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// Response model for SIP system status
/// </summary>
public class SipProxyStatusResponse
{
    /// <summary>
    /// Gets or sets whether the SIP service is running
    /// </summary>
    public bool IsRunning { get; set; }

    /// <summary>
    /// Gets or sets the registration state
    /// </summary>
    public string RegistrationState { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the configuration information
    /// </summary>
    public SipConfigurationInfo Configuration { get; set; } = new();

    /// <summary>
    /// Gets or sets the message statistics
    /// </summary>
    public MessageStatistics Statistics { get; set; } = new();
}

/// <summary>
/// SIP configuration information for status responses
/// </summary>
public class SipConfigurationInfo
{
    /// <summary>
    /// Gets or sets the server IP address
    /// </summary>
    public string ServerIp { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the server port
    /// </summary>
    public int ServerPort { get; set; }

    /// <summary>
    /// Gets or sets the extension/username
    /// </summary>
    public string Extension { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the domain
    /// </summary>
    public string Domain { get; set; } = string.Empty;
}

/// <summary>
/// Message statistics for status responses
/// </summary>
public class MessageStatistics
{
    /// <summary>
    /// Gets or sets the total number of messages
    /// </summary>
    public int TotalMessages { get; set; }

    /// <summary>
    /// Gets or sets the number of successful messages
    /// </summary>
    public int SuccessfulMessages { get; set; }

    /// <summary>
    /// Gets or sets the number of failed messages
    /// </summary>
    public int FailedMessages { get; set; }

    /// <summary>
    /// Gets or sets the number of pending messages
    /// </summary>
    public int PendingMessages { get; set; }
}
