using Ngp.Calculation.LogicEngine.Enums;
using Ngp.Calculation.LogicEngine.Models;
using Ngp.Calculation.LogicEngine.Enums;
using Ngp.Services;
using System.ComponentModel.DataAnnotations;

namespace Ngp.Models;

/// <summary>
/// Request to update an input value
/// </summary>
public class UpdateInputRequest
{
    /// <summary>
    /// Engine identifier
    /// </summary>
    [Required]
    public string EngineId { get; set; } = string.Empty;

    /// <summary>
    /// Input identifier
    /// </summary>
    [Required]
    public string InputId { get; set; } = string.Empty;

    /// <summary>
    /// New value
    /// </summary>
    [Required]
    public object Value { get; set; } = null!;
}

/// <summary>
/// Request to read a value
/// </summary>
public class ReadValueRequest
{
    /// <summary>
    /// Engine identifier
    /// </summary>
    [Required]
    public string EngineId { get; set; } = string.Empty;

    /// <summary>
    /// Value identifier (input or formula)
    /// </summary>
    [Required]
    public string ValueId { get; set; } = string.Empty;
}

/// <summary>
/// Request to create a new formula
/// </summary>
public class CreateFormulaRequest
{
    /// <summary>
    /// Engine identifier
    /// </summary>
    [Required]
    public string EngineId { get; set; } = string.Empty;

    /// <summary>
    /// Formula identifier
    /// </summary>
    [Required]
    public string FormulaId { get; set; } = string.Empty;

    /// <summary>
    /// Formula expression
    /// </summary>
    [Required]
    public string Expression { get; set; } = string.Empty;

    /// <summary>
    /// Formula description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Formula priority
    /// </summary>
    public int Priority { get; set; } = 1;

    /// <summary>
    /// Whether the formula is enabled
    /// </summary>
    public bool IsEnabled { get; set; } = true;
}

/// <summary>
/// Request to create a new input
/// </summary>
public class CreateInputRequest
{
    /// <summary>
    /// Engine identifier
    /// </summary>
    [Required]
    public string EngineId { get; set; } = string.Empty;

    /// <summary>
    /// Input identifier
    /// </summary>
    [Required]
    public string InputId { get; set; } = string.Empty;

    /// <summary>
    /// Data type
    /// </summary>
    public Ngp.Calculation.LogicEngine.Enums.DataType DataType { get; set; } = Ngp.Calculation.LogicEngine.Enums.DataType.Double;

    /// <summary>
    /// Initial value
    /// </summary>
    public object? InitialValue { get; set; }

    /// <summary>
    /// Scaling factor
    /// </summary>
    public double ScalingFactor { get; set; } = 1.0;

    /// <summary>
    /// Offset value
    /// </summary>
    public double Offset { get; set; } = 0.0;

    /// <summary>
    /// Input description
    /// </summary>
    public string? Description { get; set; }
}

/// <summary>
/// Engine status information
/// </summary>
public class EngineStatusInfo
{
    /// <summary>
    /// Engine identifier
    /// </summary>
    public string EngineId { get; set; } = string.Empty;

    /// <summary>
    /// Engine state
    /// </summary>
    public EngineState State { get; set; }

    /// <summary>
    /// State description
    /// </summary>
    public string StateDescription { get; set; } = string.Empty;

    /// <summary>
    /// Total calculations performed
    /// </summary>
    public long TotalCalculations { get; set; }

    /// <summary>
    /// Number of active formulas
    /// </summary>
    public int ActiveFormulas { get; set; }

    /// <summary>
    /// Number of registered inputs
    /// </summary>
    public int RegisteredInputs { get; set; }

    /// <summary>
    /// Memory usage in bytes
    /// </summary>
    public long MemoryUsage { get; set; }

    /// <summary>
    /// Calculations per second
    /// </summary>
    public double CalculationsPerSecond { get; set; }

    /// <summary>
    /// Last update time
    /// </summary>
    public DateTime LastUpdate { get; set; }
}

/// <summary>
/// Request model for creating a logic point
/// </summary>
public class CreateLogicPointRequest
{
    public required string PointId { get; set; }
    public required string InputId { get; set; }
    public required string OutputId { get; set; }
    public required string Formula { get; set; }
    public Ngp.Calculation.LogicEngine.Enums.DataType InputDataType { get; set; } = Ngp.Calculation.LogicEngine.Enums.DataType.Double;
    public object InitialValue { get; set; } = 0.0;
    public string? InputDescription { get; set; }
    public string? OutputDescription { get; set; }
}

/// <summary>
/// Request model for batch creating logic points
/// </summary>
public class BatchCreateLogicPointsRequest
{
    public required List<CreateLogicPointRequest> LogicPoints { get; set; }
}

/// <summary>
/// Response model for batch creating logic points
/// </summary>
public class BatchCreateLogicPointsResponse
{
    public required Dictionary<string, bool> Results { get; set; }
    public required List<string> Errors { get; set; }
    public int SuccessCount { get; set; }
    public int TotalCount { get; set; }
}

/// <summary>
/// System status information
/// </summary>
public class LogicEngineSystemStatus
{
    /// <summary>
    /// Service status
    /// </summary>
    public string ServiceStatus { get; set; } = string.Empty;

    /// <summary>
    /// Number of active engines
    /// </summary>
    public int ActiveEngines { get; set; }

    /// <summary>
    /// Engine status list
    /// </summary>
    public List<EngineStatusInfo> Engines { get; set; } = new();

    /// <summary>
    /// Service uptime
    /// </summary>
    public TimeSpan Uptime { get; set; }

    /// <summary>
    /// Total calculations across all engines
    /// </summary>
    public long TotalCalculations { get; set; }

    /// <summary>
    /// Total memory usage
    /// </summary>
    public long TotalMemoryUsage { get; set; }
}

/// <summary>
/// Value information
/// </summary>
public class ValueInfo
{
    /// <summary>
    /// Engine identifier
    /// </summary>
    public string EngineId { get; set; } = string.Empty;

    /// <summary>
    /// Value identifier
    /// </summary>
    public string ValueId { get; set; } = string.Empty;

    /// <summary>
    /// Value type (Input or Formula)
    /// </summary>
    public string ValueType { get; set; } = string.Empty;

    /// <summary>
    /// Current value
    /// </summary>
    public object? CurrentValue { get; set; }

    /// <summary>
    /// Data type
    /// </summary>
    public string DataType { get; set; } = string.Empty;

    /// <summary>
    /// Description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Last update time
    /// </summary>
    public DateTime LastUpdate { get; set; }

    /// <summary>
    /// Whether the value is available
    /// </summary>
    public bool IsAvailable { get; set; }
}

/// <summary>
/// Formula information
/// </summary>
public class FormulaInfo
{
    /// <summary>
    /// Engine identifier
    /// </summary>
    public string EngineId { get; set; } = string.Empty;

    /// <summary>
    /// Formula identifier
    /// </summary>
    public string FormulaId { get; set; } = string.Empty;

    /// <summary>
    /// Formula expression
    /// </summary>
    public string Expression { get; set; } = string.Empty;

    /// <summary>
    /// Current value
    /// </summary>
    public object? CurrentValue { get; set; }

    /// <summary>
    /// Description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Priority
    /// </summary>
    public int Priority { get; set; }

    /// <summary>
    /// Whether the formula is enabled
    /// </summary>
    public bool IsEnabled { get; set; }

    /// <summary>
    /// Number of calculations performed
    /// </summary>
    public long CalculationCount { get; set; }

    /// <summary>
    /// Average execution time in milliseconds
    /// </summary>
    public double AverageExecutionTime { get; set; }

    /// <summary>
    /// Number of errors
    /// </summary>
    public int ErrorCount { get; set; }

    /// <summary>
    /// Last calculation time
    /// </summary>
    public DateTime? LastCalculation { get; set; }
}

/// <summary>
/// Input information
/// </summary>
public class InputInfo
{
    /// <summary>
    /// Engine identifier
    /// </summary>
    public string EngineId { get; set; } = string.Empty;

    /// <summary>
    /// Input identifier
    /// </summary>
    public string InputId { get; set; } = string.Empty;

    /// <summary>
    /// Current raw value
    /// </summary>
    public object? RawValue { get; set; }

    /// <summary>
    /// Current scaled value
    /// </summary>
    public object? ScaledValue { get; set; }

    /// <summary>
    /// Data type
    /// </summary>
    public Ngp.Calculation.LogicEngine.Enums.DataType DataType { get; set; }

    /// <summary>
    /// Scaling factor
    /// </summary>
    public double ScalingFactor { get; set; }

    /// <summary>
    /// Offset value
    /// </summary>
    public double Offset { get; set; }

    /// <summary>
    /// Description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Last update time
    /// </summary>
    public DateTime? LastUpdate { get; set; }

    /// <summary>
    /// Whether the input has validation
    /// </summary>
    public bool HasValidation { get; set; }

    /// <summary>
    /// Whether the current value is valid
    /// </summary>
    public bool IsValid { get; set; }
}
