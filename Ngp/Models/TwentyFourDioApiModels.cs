using System.ComponentModel.DataAnnotations;

namespace Ngp.Models;

/// <summary>
/// Request model for setting DO states
/// </summary>
public class SetDORequest
{
    /// <summary>
    /// Gets or sets the endpoint identifier
    /// </summary>
    [Required]
    public string EndpointId { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the 8-bit DO states string (e.g., "10101010")
    /// </summary>
    [Required]
    [StringLength(8, MinimumLength = 8)]
    [RegularExpression("^[01]{8}$", ErrorMessage = "DO states must be exactly 8 binary digits (0 or 1)")]
    public string DoStates { get; set; } = string.Empty;
}

/// <summary>
/// Request model for clearing DO states
/// </summary>
public class ClearDORequest
{
    /// <summary>
    /// Gets or sets the endpoint identifier
    /// </summary>
    [Required]
    public string EndpointId { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the 8-bit DO states string (e.g., "10101010")
    /// </summary>
    [Required]
    [StringLength(8, MinimumLength = 8)]
    [RegularExpression("^[01]{8}$", ErrorMessage = "DO states must be exactly 8 binary digits (0 or 1)")]
    public string DoStates { get; set; } = string.Empty;
}

/// <summary>
/// Request model for pinning DO states
/// </summary>
public class PinDORequest
{
    /// <summary>
    /// Gets or sets the endpoint identifier
    /// </summary>
    [Required]
    public string EndpointId { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the 8-bit DO states string (e.g., "10101010")
    /// </summary>
    [Required]
    [StringLength(8, MinimumLength = 8)]
    [RegularExpression("^[01]{8}$", ErrorMessage = "DO states must be exactly 8 binary digits (0 or 1)")]
    public string DoStates { get; set; } = string.Empty;
}

/// <summary>
/// Response model for DIO state queries
/// </summary>
public class DioStateResponse
{
    /// <summary>
    /// Gets or sets the endpoint identifier
    /// </summary>
    public string EndpointId { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the connection state
    /// </summary>
    public string ConnectionState { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the 24-bit DI states string
    /// </summary>
    public string? DiStates { get; set; }

    /// <summary>
    /// Gets or sets the 8-bit DO states string
    /// </summary>
    public string? DoStates { get; set; }

    /// <summary>
    /// Gets or sets the timestamp when the state was captured
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// Gets or sets whether the poller is running
    /// </summary>
    public bool IsRunning { get; set; }
}

/// <summary>
/// Response model for command execution
/// </summary>
public class CommandResponse
{
    /// <summary>
    /// Gets or sets whether the command was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Gets or sets the response message
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the timestamp when the command was executed
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}
