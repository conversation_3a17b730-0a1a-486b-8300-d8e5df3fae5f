using System.ComponentModel.DataAnnotations;
using Ngp.Communication.NotifyEngine.Enums;

namespace Ngp.Models;

/// <summary>
/// Request model for sending notifications
/// </summary>
public class SendNotificationRequest
{
    /// <summary>
    /// Gets or sets the message content
    /// </summary>
    [Required]
    [StringLength(2000, MinimumLength = 1)]
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the message subject (for email)
    /// </summary>
    [StringLength(200)]
    public string? Subject { get; set; }

    /// <summary>
    /// Gets or sets the message priority
    /// </summary>
    public NotificationPriority Priority { get; set; } = NotificationPriority.Normal;

    /// <summary>
    /// Gets or sets the recipients for each channel
    /// </summary>
    [Required]
    [MinLength(1)]
    public Dictionary<NotificationChannel, List<string>> Recipients { get; set; } = new();
}

/// <summary>
/// Request model for sending notifications to a single channel
/// </summary>
public class SendSingleChannelNotificationRequest
{
    /// <summary>
    /// Gets or sets the notification channel
    /// </summary>
    [Required]
    public NotificationChannel Channel { get; set; }

    /// <summary>
    /// Gets or sets the message content
    /// </summary>
    [Required]
    [StringLength(2000, MinimumLength = 1)]
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the message subject (for email)
    /// </summary>
    [StringLength(200)]
    public string? Subject { get; set; }

    /// <summary>
    /// Gets or sets the recipients
    /// </summary>
    [Required]
    [MinLength(1)]
    public List<string> Recipients { get; set; } = new();

    /// <summary>
    /// Gets or sets the message priority
    /// </summary>
    public NotificationPriority Priority { get; set; } = NotificationPriority.Normal;
}

/// <summary>
/// Response model for send notification operations
/// </summary>
public class SendNotificationResponse
{
    /// <summary>
    /// Gets or sets the message IDs
    /// </summary>
    public List<string> MessageIds { get; set; } = new();

    /// <summary>
    /// Gets or sets the number of messages sent
    /// </summary>
    public int MessageCount { get; set; }

    /// <summary>
    /// Gets or sets the channels used
    /// </summary>
    public List<NotificationChannel> ChannelsUsed { get; set; } = new();
}

/// <summary>
/// Response model for single channel send notification operations
/// </summary>
public class SendSingleChannelNotificationResponse
{
    /// <summary>
    /// Gets or sets the message ID
    /// </summary>
    public string MessageId { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the channel used
    /// </summary>
    public NotificationChannel Channel { get; set; }

    /// <summary>
    /// Gets or sets the number of recipients
    /// </summary>
    public int RecipientCount { get; set; }
}

/// <summary>
/// Response model for notification status
/// </summary>
public class NotificationStatusResponse
{
    /// <summary>
    /// Gets or sets the message ID
    /// </summary>
    public string MessageId { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the notification channel
    /// </summary>
    public NotificationChannel Channel { get; set; }

    /// <summary>
    /// Gets or sets the message status
    /// </summary>
    public NotificationStatus Status { get; set; }

    /// <summary>
    /// Gets or sets the message content
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the message subject
    /// </summary>
    public string? Subject { get; set; }

    /// <summary>
    /// Gets or sets the recipients
    /// </summary>
    public List<string> Recipients { get; set; } = new();

    /// <summary>
    /// Gets or sets the message priority
    /// </summary>
    public NotificationPriority Priority { get; set; }

    /// <summary>
    /// Gets or sets when the message was created
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Gets or sets when the message was sent
    /// </summary>
    public DateTime? SentAt { get; set; }

    /// <summary>
    /// Gets or sets when the message was delivered
    /// </summary>
    public DateTime? DeliveredAt { get; set; }

    /// <summary>
    /// Gets or sets the error message if delivery failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Gets or sets the number of retry attempts made
    /// </summary>
    public int RetryCount { get; set; }

    /// <summary>
    /// Gets or sets the duration from send to delivery in milliseconds
    /// </summary>
    public double? DurationMs { get; set; }

    /// <summary>
    /// Gets whether the message was delivered successfully
    /// </summary>
    public bool IsDelivered { get; set; }

    /// <summary>
    /// Gets whether the message delivery failed
    /// </summary>
    public bool IsFailed { get; set; }

    /// <summary>
    /// Gets whether the message can be retried
    /// </summary>
    public bool CanRetry { get; set; }
}

/// <summary>
/// Response model for notification statistics
/// </summary>
public class NotificationStatisticsResponse
{
    /// <summary>
    /// Gets or sets the total number of messages sent
    /// </summary>
    public long TotalMessages { get; set; }

    /// <summary>
    /// Gets or sets the number of successfully delivered messages
    /// </summary>
    public long DeliveredMessages { get; set; }

    /// <summary>
    /// Gets or sets the number of failed messages
    /// </summary>
    public long FailedMessages { get; set; }

    /// <summary>
    /// Gets or sets the number of pending messages
    /// </summary>
    public long PendingMessages { get; set; }

    /// <summary>
    /// Gets or sets the delivery rate as a percentage
    /// </summary>
    public double DeliveryRate { get; set; }

    /// <summary>
    /// Gets or sets the failure rate as a percentage
    /// </summary>
    public double FailureRate { get; set; }

    /// <summary>
    /// Gets or sets statistics by channel
    /// </summary>
    public Dictionary<NotificationChannel, ChannelStatisticsResponse> ChannelStatistics { get; set; } = new();
}

/// <summary>
/// Response model for channel statistics
/// </summary>
public class ChannelStatisticsResponse
{
    /// <summary>
    /// Gets or sets the notification channel
    /// </summary>
    public NotificationChannel Channel { get; set; }

    /// <summary>
    /// Gets or sets the total number of messages for this channel
    /// </summary>
    public long TotalMessages { get; set; }

    /// <summary>
    /// Gets or sets the number of successfully delivered messages
    /// </summary>
    public long DeliveredMessages { get; set; }

    /// <summary>
    /// Gets or sets the number of failed messages
    /// </summary>
    public long FailedMessages { get; set; }

    /// <summary>
    /// Gets or sets the number of pending messages
    /// </summary>
    public long PendingMessages { get; set; }

    /// <summary>
    /// Gets or sets the average delivery time in milliseconds
    /// </summary>
    public double AverageDeliveryTimeMs { get; set; }

    /// <summary>
    /// Gets or sets whether the channel is currently available
    /// </summary>
    public bool IsAvailable { get; set; }

    /// <summary>
    /// Gets or sets the delivery rate as a percentage
    /// </summary>
    public double DeliveryRate { get; set; }

    /// <summary>
    /// Gets or sets the failure rate as a percentage
    /// </summary>
    public double FailureRate { get; set; }
}

/// <summary>
/// Response model for service status
/// </summary>
public class NotifyEngineStatusResponse
{
    /// <summary>
    /// Gets or sets whether the service is running
    /// </summary>
    public bool IsRunning { get; set; }

    /// <summary>
    /// Gets or sets the available notification channels
    /// </summary>
    public List<NotificationChannel> AvailableChannels { get; set; } = new();

    /// <summary>
    /// Gets or sets the channel availability status
    /// </summary>
    public Dictionary<NotificationChannel, bool> ChannelAvailability { get; set; } = new();

    /// <summary>
    /// Gets or sets the service uptime
    /// </summary>
    public TimeSpan? Uptime { get; set; }

    /// <summary>
    /// Gets or sets the last update timestamp
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}


