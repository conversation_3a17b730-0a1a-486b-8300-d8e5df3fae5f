using Mediator;
using Ngp.Entities.Enums;

namespace Ngp.Commands;

/// <summary>
/// Command to update a node's value
/// </summary>
public class UpdateNodeValueCommand : ICommand<UpdateNodeValueResult>
{
    /// <summary>
    /// Node identifier
    /// </summary>
    public required string NodeId { get; set; }
    
    /// <summary>
    /// New value for the node
    /// </summary>
    public required string Value { get; set; }
    
    /// <summary>
    /// Quality indicator for the value
    /// </summary>
    public bool IsGoodQuality { get; set; } = true;
    
    /// <summary>
    /// Timestamp of the update (optional, defaults to current time)
    /// </summary>
    public DateTime? Timestamp { get; set; }
    
    /// <summary>
    /// Whether to force the update even if the value hasn't changed
    /// </summary>
    public bool ForceUpdate { get; set; } = false;
}

/// <summary>
/// Result of updating a node value
/// </summary>
public class UpdateNodeValueResult
{
    /// <summary>
    /// Whether the update was successful
    /// </summary>
    public bool Success { get; set; }
    
    /// <summary>
    /// Error message if the update failed
    /// </summary>
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// Whether the value actually changed
    /// </summary>
    public bool ValueChanged { get; set; }
    
    /// <summary>
    /// Previous value before the update
    /// </summary>
    public string? PreviousValue { get; set; }
    
    /// <summary>
    /// Timestamp when the update was processed
    /// </summary>
    public DateTime ProcessedAt { get; set; } = DateTime.UtcNow;
}
