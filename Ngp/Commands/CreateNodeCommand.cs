using Mediator;
using Ngp.Entities.Enums;

namespace Ngp.Commands;

/// <summary>
/// Command to create a new node
/// </summary>
public class CreateNodeCommand : ICommand<CreateNodeResult>
{
    /// <summary>
    /// Unique identifier for the node
    /// </summary>
    public required string NodeId { get; set; }
    
    /// <summary>
    /// Display name for the node
    /// </summary>
    public required string Name { get; set; }
    
    /// <summary>
    /// Description of the node
    /// </summary>
    public string Description { get; set; } = string.Empty;
    
    /// <summary>
    /// Type of the node
    /// </summary>
    public NodeType NodeType { get; set; }
    
    /// <summary>
    /// Whether the node is enabled
    /// </summary>
    public bool IsEnabled { get; set; } = true;
    
    /// <summary>
    /// Whether logging is enabled
    /// </summary>
    public bool IsLogEnabled { get; set; } = true;
    
    /// <summary>
    /// Whether this is a failure node
    /// </summary>
    public bool IsFailureNode { get; set; } = false;
    
    /// <summary>
    /// Whether to display runtime information
    /// </summary>
    public bool DisplayRuntime { get; set; } = true;
    
    /// <summary>
    /// Number of decimal places for display
    /// </summary>
    public byte DecimalPlaces { get; set; } = 2;
    
    /// <summary>
    /// Unit of measurement
    /// </summary>
    public string Unit { get; set; } = string.Empty;
    
    /// <summary>
    /// Scale factor for value conversion
    /// </summary>
    public double Scale { get; set; } = 1.0;
    
    /// <summary>
    /// Initial value for the node
    /// </summary>
    public string InitialValue { get; set; } = string.Empty;
    
    /// <summary>
    /// Data path ID (optional)
    /// </summary>
    public int? DataPathId { get; set; }
    
    /// <summary>
    /// Custom node template ID (optional)
    /// </summary>
    public int? CustomNodeTemplateId { get; set; }
    
    /// <summary>
    /// Lookup table ID (optional)
    /// </summary>
    public int? LookupTableId { get; set; }
    
    /// <summary>
    /// History recording mode (optional)
    /// </summary>
    public HistoryRecordMode? HistoryRecordMode { get; set; }
    
    /// <summary>
    /// Change threshold for instantaneous recording
    /// </summary>
    public double? ChangeThreshold { get; set; }
    
    /// <summary>
    /// Recording interval in seconds
    /// </summary>
    public int? RecordingIntervalSeconds { get; set; }
    
    /// <summary>
    /// Whether to enable history recording
    /// </summary>
    public bool EnableHistoryRecording { get; set; } = false;
}

/// <summary>
/// Result of creating a node
/// </summary>
public class CreateNodeResult
{
    /// <summary>
    /// Whether the creation was successful
    /// </summary>
    public bool Success { get; set; }
    
    /// <summary>
    /// Error message if creation failed
    /// </summary>
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// ID of the created node
    /// </summary>
    public int? NodeId { get; set; }
    
    /// <summary>
    /// Timestamp when the node was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}
