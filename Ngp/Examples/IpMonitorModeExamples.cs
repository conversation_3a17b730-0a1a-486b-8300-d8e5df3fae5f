using Ngp.Communication.IpMonitor;
using Ngp.Communication.IpMonitor.Enums;

namespace Ngp.Examples;

/// <summary>
/// Examples demonstrating how to switch between different IP monitoring modes
/// </summary>
public static class IpMonitorModeExamples
{
    /// <summary>
    /// Example 1: 即時模式 (Immediate Mode)
    /// 任何成功或失敗都立即反映狀態變化
    /// 適用場景：需要即時反應的監控，如關鍵服務監控
    /// </summary>
    public static void CreateImmediateModeMonitor(ILoggerFactory loggerFactory)
    {
        var ipAddresses = new[] { "*******", "*******", "***********" };

        // 使用統一的Create方法和Fluent API配置即時模式
        var monitor = IpMonitorFactory.Create(loggerFactory)
            .WithId("immediate-monitor")
            .WithMonitoringMode(MonitoringMode.Immediate)  // 設定即時模式
            .WithDetectionMethod(DetectionMethod.IcmpPing)
            .WithMaxConcurrency(100)
            .WithTimeout(3000)
            .WithPollingInterval(500)  // 0.5秒快速輪詢
            .AddIpAddresses(ipAddresses)
            .Build();

        Console.WriteLine("即時模式監控器已建立 - 任何Ping成功/失敗都會立即更新狀態");
    }

    /// <summary>
    /// Example 2: 一般模式 (Normal Mode)
    /// 提供重試機制來確認狀態變化，避免誤報
    /// 適用場景：生產環境的穩定監控
    /// </summary>
    public static void CreateNormalModeMonitor(ILoggerFactory loggerFactory)
    {
        var ipAddresses = new[] { "*******", "*******", "***********" };

        // 使用統一的Create方法和Fluent API配置一般模式
        var monitor = IpMonitorFactory.Create(loggerFactory)
            .WithId("normal-monitor")
            .WithMonitoringMode(MonitoringMode.Normal)  // 設定一般模式
            .WithDetectionMethod(DetectionMethod.IcmpPing)
            .WithMaxConcurrency(50)
            .WithTimeout(5000)
            .WithPollingInterval(2000)  // 2秒輪詢一次
            .WithRetryPolicy(3, 1000)   // 失敗時重試3次，間隔1秒
            .WithConsecutiveSuccessCount(2)  // 需要連續2次成功才確認上線
            .AddIpAddresses(ipAddresses)
            .Build();

        Console.WriteLine("一般模式監控器已建立 - 需要連續失敗3次才確認離線，連續成功2次才確認上線");
    }

    /// <summary>
    /// Example 3: 簡易模式 (Simple Mode)
    /// 在時間窗口內有任何回應就算在線，適合基本的可用性檢查
    /// 適用場景：簡單的服務可用性監控
    /// </summary>
    public static void CreateSimpleModeMonitor(ILoggerFactory loggerFactory)
    {
        var ipAddresses = new[] { "*******", "*******", "***********" };

        // 使用統一的Create方法和Fluent API配置簡易模式
        var monitor = IpMonitorFactory.Create(loggerFactory)
            .WithId("simple-monitor")
            .WithMonitoringMode(MonitoringMode.Simple)  // 設定簡易模式
            .WithDetectionMethod(DetectionMethod.IcmpPing)
            .WithMaxConcurrency(25)
            .WithTimeout(5000)
            .WithPollingInterval(10000)  // 10秒輪詢一次
            .WithMaintenanceTime(300000)  // 5分鐘維持時間
            .AddIpAddresses(ipAddresses)
            .Build();

        Console.WriteLine("簡易模式監控器已建立 - 只要10分鐘內有任何成功回應就算在線");
    }

    /// <summary>
    /// Example 4: 不同場景的模式選擇建議
    /// </summary>
    public static void ScenarioBasedModeSelection(ILoggerFactory loggerFactory)
    {
        // 場景1: 關鍵服務監控 - 使用即時模式
        var criticalServices = new[] { "***********00", "***********01" };
        var criticalMonitor = IpMonitorFactory.Create(loggerFactory)
            .WithId("critical-services")
            .WithMonitoringMode(MonitoringMode.Immediate)
            .WithPollingInterval(500)  // 0.5秒快速輪詢
            .AddIpAddresses(criticalServices, "Critical", "關鍵服務監控")
            .Build();

        // 場景2: 網路設備監控 - 使用一般模式
        var networkDevices = new[] { "***********", "***********", "***********" };
        var networkMonitor = IpMonitorFactory.Create(loggerFactory)
            .WithId("network-devices")
            .WithMonitoringMode(MonitoringMode.Normal)
            .WithRetryPolicy(5, 2000)  // 網路設備可能偶爾不回應，多重試幾次
            .WithPollingInterval(5000)  // 5秒輪詢
            .AddIpAddresses(networkDevices, "Network", "網路設備監控")
            .Build();

        // 場景3: 大範圍IP掃描 - 使用簡易模式
        var ipRange = "***********~***********54";
        var scanMonitor = IpMonitorFactory.Create(loggerFactory)
            .WithId("ip-range-scan")
            .WithMonitoringMode(MonitoringMode.Simple)
            .WithMaintenanceTime(1800000)  // 30分鐘維持時間
            .WithPollingInterval(30000)    // 30秒輪詢
            .WithMaxConcurrency(100)       // 高並行掃描
            .AddIpRange(ipRange, "Scan", "IP範圍掃描")
            .Build();

        Console.WriteLine("已建立不同場景的監控器：");
        Console.WriteLine("- 關鍵服務：即時模式，0.5秒輪詢");
        Console.WriteLine("- 網路設備：一般模式，5秒輪詢，5次重試");
        Console.WriteLine("- IP掃描：簡易模式，30秒輪詢，30分鐘維持");
    }

    /// <summary>
    /// Example 5: 動態切換監控模式的示例
    /// 注意：目前的實作不支援運行時切換模式，需要重新建立監控器
    /// </summary>
    public static async Task DynamicModeSwitch(ILoggerFactory loggerFactory)
    {
        var ipAddresses = new[] { "*******", "*******" };

        // 開始使用即時模式
        var immediateMonitor = IpMonitorFactory.Create(loggerFactory)
            .WithId("dynamic-monitor")
            .WithMonitoringMode(MonitoringMode.Immediate)
            .AddIpAddresses(ipAddresses)
            .Build();

        await immediateMonitor.StartAsync();
        Console.WriteLine("開始使用即時模式監控...");

        // 模擬運行一段時間
        await Task.Delay(10000);

        // 停止即時模式監控器
        await immediateMonitor.StopAsync();
        immediateMonitor.Dispose();
        Console.WriteLine("停止即時模式監控");

        // 切換到一般模式
        var normalMonitor = IpMonitorFactory.Create(loggerFactory)
            .WithId("dynamic-monitor-normal")
            .WithMonitoringMode(MonitoringMode.Normal)
            .WithRetryPolicy(3, 1000)
            .WithConsecutiveSuccessCount(2)
            .AddIpAddresses(ipAddresses)
            .Build();

        await normalMonitor.StartAsync();
        Console.WriteLine("切換到一般模式監控...");

        // 模擬運行一段時間
        await Task.Delay(10000);

        // 停止一般模式監控器
        await normalMonitor.StopAsync();
        normalMonitor.Dispose();
        Console.WriteLine("停止一般模式監控");

        // 切換到簡易模式
        var simpleMonitor = IpMonitorFactory.Create(loggerFactory)
            .WithId("dynamic-monitor-simple")
            .WithMonitoringMode(MonitoringMode.Simple)
            .WithMaintenanceTime(300000)
            .AddIpAddresses(ipAddresses)
            .Build();

        await simpleMonitor.StartAsync();
        Console.WriteLine("切換到簡易模式監控...");

        // 清理
        await Task.Delay(5000);
        await simpleMonitor.StopAsync();
        simpleMonitor.Dispose();
        Console.WriteLine("完成模式切換示例");
    }
}
