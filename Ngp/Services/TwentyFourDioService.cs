using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Ngp.Communication.TwentyFourDioPoller;
using Ngp.Communication.TwentyFourDioPoller.Events;
using Ngp.Communication.TwentyFourDioPoller.Interfaces;
using System.Collections.Concurrent;

namespace Ngp.Services;

/// <summary>
/// Service for managing 24Dio device connections and operations
/// </summary>
public class TwentyFourDioService : BackgroundService
{
    private readonly ILogger<TwentyFourDioService> _logger;
    private readonly ILoggerFactory _loggerFactory;
    private readonly ConcurrentDictionary<string, ITwentyFourDioPoller> _pollers;

    // Error suppression for graceful logging during reconnection
    private readonly ConcurrentDictionary<string, DateTime> _lastErrorLogTimes = new();
    private const int ErrorLogSuppressionSeconds = 30; // Suppress repeated errors for 30 seconds

    /// <summary>
    /// Initializes a new instance of the TwentyFourDioService class
    /// </summary>
    /// <param name="logger">Logger instance</param>
    /// <param name="loggerFactory">Logger factory</param>
    public TwentyFourDioService(ILogger<TwentyFourDioService> logger, ILoggerFactory loggerFactory)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
        _pollers = new ConcurrentDictionary<string, ITwentyFourDioPoller>();
    }

    /// <summary>
    /// Gets all registered pollers
    /// </summary>
    public IReadOnlyDictionary<string, ITwentyFourDioPoller> Pollers => _pollers;

    /// <summary>
    /// Gets a poller by endpoint ID
    /// </summary>
    /// <param name="endpointId">Endpoint identifier</param>
    /// <returns>Poller instance or null if not found</returns>
    public ITwentyFourDioPoller? GetPoller(string endpointId)
    {
        return _pollers.TryGetValue(endpointId, out var poller) ? poller : null;
    }

    /// <summary>
    /// Adds a new 24Dio poller
    /// </summary>
    /// <param name="endpointId">Endpoint identifier</param>
    /// <param name="ipAddress">IP address of the device</param>
    /// <param name="port">TCP port</param>
    /// <returns>True if added successfully</returns>
    public async Task<bool> AddPollerAsync(string endpointId, string ipAddress, ushort port = 5801)
    {
        try
        {
            if (_pollers.ContainsKey(endpointId))
            {
                _logger.LogWarning("Poller for endpoint {EndpointId} already exists", endpointId);
                return false;
            }

            var poller = TwentyFourDioPollerFactory.CreateProduction(endpointId, ipAddress, port, 
                _loggerFactory.CreateLogger<TwentyFourDioPoller>());

            // Wire up events
            poller.ConnectionStateChanged += OnConnectionStateChanged;
            poller.DioValueChanged += OnDioValueChanged;
            poller.DioError += OnDioError;

            // Add to collection
            _pollers[endpointId] = poller;

            // Start the poller
            await poller.StartAsync();

            _logger.LogInformation("Added and started 24Dio poller for endpoint {EndpointId} at {IpAddress}:{Port}", 
                endpointId, ipAddress, port);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to add 24Dio poller for endpoint {EndpointId}", endpointId);
            return false;
        }
    }

    /// <summary>
    /// Removes a 24Dio poller
    /// </summary>
    /// <param name="endpointId">Endpoint identifier</param>
    /// <returns>True if removed successfully</returns>
    public async Task<bool> RemovePollerAsync(string endpointId)
    {
        try
        {
            if (!_pollers.TryRemove(endpointId, out var poller))
            {
                _logger.LogWarning("Poller for endpoint {EndpointId} not found", endpointId);
                return false;
            }

            // Unsubscribe from events
            poller.ConnectionStateChanged -= OnConnectionStateChanged;
            poller.DioValueChanged -= OnDioValueChanged;
            poller.DioError -= OnDioError;

            // Stop and dispose
            await poller.StopAsync();
            poller.Dispose();

            _logger.LogInformation("Removed 24Dio poller for endpoint {EndpointId}", endpointId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to remove 24Dio poller for endpoint {EndpointId}", endpointId);
            return false;
        }
    }

    /// <summary>
    /// Executes the background service
    /// </summary>
    /// <param name="stoppingToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("TwentyFourDioService started");

        try
        {
            // Initialize test poller for ************:5801
            const string testEndpointId = "test-24dio";
            const string testIpAddress = "************";
            const ushort testPort = 5801;

            _logger.LogInformation("Initializing test 24Dio poller for {EndpointId} at {IpAddress}:{Port}", 
                testEndpointId, testIpAddress, testPort);

            await AddPollerAsync(testEndpointId, testIpAddress, testPort);

            // Keep the service running
            while (!stoppingToken.IsCancellationRequested)
            {
                await Task.Delay(1000, stoppingToken);
            }
        }
        catch (OperationCanceledException) when (stoppingToken.IsCancellationRequested)
        {
            // Expected when stopping
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in TwentyFourDioService background execution");
        }

        _logger.LogInformation("TwentyFourDioService stopped");
    }

    /// <summary>
    /// Disposes the service
    /// </summary>
    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Stopping TwentyFourDioService");

        // Stop all pollers
        var stopTasks = _pollers.Values.Select(async poller =>
        {
            try
            {
                await poller.StopAsync(cancellationToken);
                poller.Dispose();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error stopping poller for endpoint {EndpointId}", poller.Configuration.Id);
            }
        });

        await Task.WhenAll(stopTasks);
        _pollers.Clear();

        await base.StopAsync(cancellationToken);
        _logger.LogInformation("TwentyFourDioService stopped");
    }

    /// <summary>
    /// Handles connection state changes
    /// </summary>
    private void OnConnectionStateChanged(object? sender, ConnectionStateChangedEventArgs e)
    {
        _logger.LogInformation("Connection state changed for endpoint {EndpointId}: {PreviousState} -> {CurrentState}", 
            e.EndpointId, e.PreviousState, e.CurrentState);

        if (!string.IsNullOrEmpty(e.ErrorMessage))
        {
            _logger.LogWarning("Connection state change error for endpoint {EndpointId}: {ErrorMessage}", 
                e.EndpointId, e.ErrorMessage);
        }
    }

    /// <summary>
    /// Handles DIO value changes
    /// </summary>
    private void OnDioValueChanged(object? sender, DioValueChangedEventArgs e)
    {
        _logger.LogInformation("DIO values changed for endpoint {EndpointId}: {Changes}", 
            e.EndpointId, e.GetChangesSummary());
    }

    /// <summary>
    /// Handles DIO errors
    /// </summary>
    private void OnDioError(object? sender, DioErrorEventArgs e)
    {
        // Get the poller to check connection state
        var poller = GetPoller(e.EndpointId);
        var connectionState = poller?.ConnectionState ?? Ngp.Communication.TwentyFourDioPoller.Enums.ConnectionState.Disconnected;

        // Adjust log level based on connection state and error type
        LogLevel logLevel;
        if (connectionState == Ngp.Communication.TwentyFourDioPoller.Enums.ConnectionState.Reconnecting)
        {
            // During reconnection, log connection-related errors as debug to reduce noise
            if (e.ErrorMessage.Contains("Connection") || e.ErrorMessage.Contains("not connected") ||
                e.ErrorMessage.Contains("Receive error") || e.ErrorMessage.Contains("Send error"))
            {
                logLevel = LogLevel.Debug;
            }
            else
            {
                logLevel = LogLevel.Warning;
            }
        }
        else if (e.ShouldReconnect)
        {
            logLevel = LogLevel.Warning;
        }
        else
        {
            logLevel = LogLevel.Error;
        }

        _logger.Log(logLevel, e.Exception, "DIO error for endpoint {EndpointId}: {ErrorMessage}",
            e.EndpointId, e.ErrorMessage);
    }
}
