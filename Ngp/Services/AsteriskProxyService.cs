using Ngp.Communication.AsteriskProxy;
using Ngp.Communication.AsteriskProxy.Enums;
using Ngp.Communication.AsteriskProxy.Events;
using Ngp.Communication.AsteriskProxy.Interfaces;
using System.Collections.Concurrent;

namespace Ngp.Services;

/// <summary>
/// Service for managing multiple Asterisk proxy instances
/// </summary>
public class AsteriskProxyService : IHostedService, IDisposable
{
    private readonly ILogger<AsteriskProxyService> _logger;
    private readonly ILoggerFactory _loggerFactory;
    private readonly ConcurrentDictionary<string, IAsteriskProxy> _proxies;
    private readonly CancellationTokenSource _cancellationTokenSource;
    private bool _disposed = false;

    /// <summary>
    /// Event raised when a connection state changes
    /// </summary>
    public event EventHandler<ConnectionStateChangedEventArgs>? ConnectionStateChanged;

    /// <summary>
    /// Event raised when an extension status changes
    /// </summary>
    public event EventHandler<ExtensionStatusChangedEventArgs>? ExtensionStatusChanged;

    /// <summary>
    /// Event raised when an Asterisk error occurs
    /// </summary>
    public event EventHandler<AsteriskErrorEventArgs>? AsteriskError;

    /// <summary>
    /// Initializes a new instance of the AsteriskProxyService class
    /// </summary>
    /// <param name="logger">Logger instance</param>
    /// <param name="loggerFactory">Logger factory</param>
    public AsteriskProxyService(ILogger<AsteriskProxyService> logger, ILoggerFactory loggerFactory)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
        _proxies = new ConcurrentDictionary<string, IAsteriskProxy>();
        _cancellationTokenSource = new CancellationTokenSource();
    }

    /// <summary>
    /// Starts the service and initializes test configuration
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starting AsteriskProxyService");

        try
        {
            // Initialize test configuration as specified in requirements
            await InitializeTestConfigurationAsync(cancellationToken);

            _logger.LogInformation("AsteriskProxyService started successfully with {ProxyCount} proxies", _proxies.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start AsteriskProxyService");
            throw;
        }
    }

    /// <summary>
    /// Stops the service
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Stopping AsteriskProxyService");

        _cancellationTokenSource.Cancel();

        // Stop all proxies
        var stopTasks = _proxies.Values.Select(proxy => proxy.StopAsync(cancellationToken));
        await Task.WhenAll(stopTasks);

        _logger.LogInformation("AsteriskProxyService stopped");
    }

    /// <summary>
    /// Initializes test configuration for FreePBX 17 + Asterisk 22 server
    /// </summary>
    private async Task InitializeTestConfigurationAsync(CancellationToken cancellationToken)
    {
        const string testEndpointId = "FreePBX-Test";
        const string testBaseUrl = "http://192.168.1.7:8088";
        const string testUsername = "weema";
        const string testPassword = "651a8a88bae133a3789047363facd260";

        try
        {
            _logger.LogInformation("Initializing test configuration for {BaseUrl}", testBaseUrl);

            // Create test proxy with comprehensive extension configuration
            var proxy = AsteriskProxyFactory
                .CreateForEndpoint(testEndpointId, testBaseUrl, testUsername, testPassword, _loggerFactory)
                .WithSslVerification(false) // Disable SSL verification for test environment
                .WithApiVersion(AsteriskApiVersion.Version22) // Use Asterisk 22 API
                .WithTimeouts(5000, 10000) // Connection timeout: 5s, Request timeout: 10s
                .WithPollingInterval(2000) // Poll every 2 seconds
                .WithRetryPolicy(3, 2000) // 3 retries with 2s delay
                // Add test extension 8811 as specified in requirements
                .AddExtension("8811", "internal", "TestExt", "Test extension 8811")
                .AddExtension("8814", "internal", "TestExt", "Test extension 8814")
                // Add some additional test extensions for comprehensive testing
                .AddExtension("1001", "internal", "Reception", "Reception desk")
                .AddExtension("1002", "internal", "Manager", "Manager office")
                // Add a range of extensions for testing
                .AddExtensionRange(2000, 2005, "internal", "Employee", "Employee extensions")
                .Build();

            // Wire up events
            proxy.ConnectionStateChanged += OnConnectionStateChanged;
            proxy.ExtensionStatusChanged += OnExtensionStatusChanged;
            proxy.AsteriskError += OnAsteriskError;

            // Add to proxies collection
            _proxies[testEndpointId] = proxy;

            // Start the proxy
            await proxy.StartAsync(cancellationToken);

            _logger.LogInformation("Test proxy initialized and started for {EndpointId}", testEndpointId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize test configuration for {BaseUrl}", testBaseUrl);
            throw;
        }
    }

    /// <summary>
    /// Gets a proxy by endpoint ID
    /// </summary>
    /// <param name="endpointId">Endpoint identifier</param>
    /// <returns>Proxy instance or null if not found</returns>
    public IAsteriskProxy? GetProxy(string endpointId)
    {
        return _proxies.TryGetValue(endpointId, out var proxy) ? proxy : null;
    }

    /// <summary>
    /// Gets all proxy endpoint IDs
    /// </summary>
    /// <returns>Collection of endpoint IDs</returns>
    public IEnumerable<string> GetProxyIds()
    {
        return _proxies.Keys;
    }

    /// <summary>
    /// Gets connection status for all proxies
    /// </summary>
    /// <returns>Dictionary of endpoint ID to connection state</returns>
    public Dictionary<string, ConnectionState> GetConnectionStatus()
    {
        return _proxies.ToDictionary(kvp => kvp.Key, kvp => kvp.Value.ConnectionState);
    }



    /// <summary>
    /// Forces refresh of all extensions for a specific proxy
    /// </summary>
    /// <param name="endpointId">Endpoint identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if successful</returns>
    public async Task<bool> RefreshAllExtensionsAsync(string endpointId, CancellationToken cancellationToken = default)
    {
        var proxy = GetProxy(endpointId);
        if (proxy == null)
        {
            _logger.LogWarning("Proxy not found for endpoint {EndpointId}", endpointId);
            return false;
        }

        try
        {
            await proxy.RefreshAllExtensionsAsync(cancellationToken);
            _logger.LogInformation("Refreshed all extensions for endpoint {EndpointId}", endpointId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error refreshing extensions for endpoint {EndpointId}", endpointId);
            return false;
        }
    }

    /// <summary>
    /// Handles connection state changes
    /// </summary>
    private void OnConnectionStateChanged(object? sender, ConnectionStateChangedEventArgs e)
    {
        _logger.LogInformation("Connection state changed: Endpoint={EndpointId}, State={State}",
            e.EndpointId, e.CurrentState);
        ConnectionStateChanged?.Invoke(this, e);
    }

    /// <summary>
    /// Handles extension status changes
    /// </summary>
    private void OnExtensionStatusChanged(object? sender, ExtensionStatusChangedEventArgs e)
    {
        _logger.LogInformation("Extension status changed: Endpoint={EndpointId}, Extension={Extension}, State={State}",
            e.EndpointId, e.Extension, e.CurrentStatus.State);
        ExtensionStatusChanged?.Invoke(this, e);
    }

    /// <summary>
    /// Handles Asterisk errors
    /// </summary>
    private void OnAsteriskError(object? sender, AsteriskErrorEventArgs e)
    {
        _logger.LogWarning("Asterisk error: Endpoint={EndpointId}, Error={Error}",
            e.EndpointId, e.ErrorMessage);
        AsteriskError?.Invoke(this, e);
    }

    /// <summary>
    /// Disposes the service
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _disposed = true;

            _cancellationTokenSource.Cancel();

            foreach (var proxy in _proxies.Values)
            {
                proxy.Dispose();
            }

            _proxies.Clear();
            _cancellationTokenSource.Dispose();
        }

        GC.SuppressFinalize(this);
    }
}
