using Ngp.Communication.ModbusTcpMaster;
using Ngp.Communication.ModbusTcpMaster.Enums;
using Ngp.Communication.ModbusTcpMaster.Events;
using Ngp.Communication.ModbusTcpMaster.Interfaces;
using Ngp.Communication.ModbusTcpMaster.Models;
using System.Collections.Concurrent;

namespace Ngp.Services;

/// <summary>
/// Service for managing multiple Modbus TCP Master instances
/// </summary>
public class ModbusTcpService : IHostedService, IDisposable
{
    private readonly ILogger<ModbusTcpService> _logger;
    private readonly ILoggerFactory _loggerFactory;
    private readonly ConcurrentDictionary<string, IModbusTcpMaster> _masters;
    private readonly CancellationTokenSource _cancellationTokenSource;
    private bool _disposed = false;

    /// <summary>
    /// Event raised when a connection state changes
    /// </summary>
    public event EventHandler<ConnectionStateChangedEventArgs>? ConnectionStateChanged;

    /// <summary>
    /// Event raised when a register value changes
    /// </summary>
    public event EventHandler<RegisterValueChangedEventArgs>? RegisterValueChanged;

    /// <summary>
    /// Event raised when a Modbus error occurs
    /// </summary>
    public event EventHandler<ModbusErrorEventArgs>? ModbusError;

    /// <summary>
    /// Initializes a new instance of the ModbusTcpService class
    /// </summary>
    /// <param name="logger">Logger instance</param>
    /// <param name="loggerFactory">Logger factory</param>
    public ModbusTcpService(ILogger<ModbusTcpService> logger, ILoggerFactory loggerFactory)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
        _masters = new ConcurrentDictionary<string, IModbusTcpMaster>();
        _cancellationTokenSource = new CancellationTokenSource();
    }

    /// <summary>
    /// Starts the service and initializes test configuration
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starting ModbusTcpService");

        try
        {
            // Initialize test configuration as specified in requirements
            await InitializeTestConfigurationAsync(cancellationToken);

            _logger.LogInformation("ModbusTcpService started successfully with {MasterCount} masters", _masters.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start ModbusTcpService");
            throw;
        }
    }

    /// <summary>
    /// Stops the service
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Stopping ModbusTcpService");

        _cancellationTokenSource.Cancel();

        // Stop all masters
        var stopTasks = _masters.Values.Select(master => master.StopAsync(cancellationToken));
        await Task.WhenAll(stopTasks);

        _logger.LogInformation("ModbusTcpService stopped");
    }

    /// <summary>
    /// Initializes test configuration for **************:502
    /// </summary>
    private async Task InitializeTestConfigurationAsync(CancellationToken cancellationToken)
    {


        {
            const string testEndpointId = "TestSlave";
            const string testIpAddress = "**************";
            const ushort testPort = 502;
            try
            {

                _logger.LogInformation("Initializing test configuration for {IpAddress}:{Port}", testIpAddress, testPort);

                // Create test master with comprehensive register configuration
                var master = ModbusTcpMasterFactory
                    .CreateForEndpoint(testEndpointId, testIpAddress, testPort, _loggerFactory)
                    .WithProtocol(ModbusProtocol.ModbusTcp)
                    .WithWriteMode(WriteMode.Multiple)
                    .WithAddressMode(AddressMode.ZeroBased)
                    .WithTimeouts(500, 500)
                    .WithDelays(0, 0)
                    .WithMaxPollingQuantities(2000, 125)
                    .WithRetryPolicy(3, 1000)
                    .WithParallelization(1, false)
                    .WithDebounceTime(50)
                    // Add test registers to verify packet optimization with MaxAnalogPollingQuantity = 125
                    // Test case 1: Continuous ushort registers that should be grouped into one packet (125 registers)
                    .AddRegisters(1, 0, 124, ModbusFunction.ReadHoldingRegisters, typeof(ushort), EndianType.BigEndian, false, "TestUshort1", "First batch of 125 ushort registers")
                    // Test case 2: Another batch that should create a second packet (75 registers)
                    .AddRegisters(1, 125, 199, ModbusFunction.ReadHoldingRegisters, typeof(ushort), EndianType.BigEndian, false, "TestUshort2", "Second batch of 75 ushort registers")
                    // Test case 3: Non-continuous registers that should create separate packets
                    .AddRegisters(1, 300, 349, ModbusFunction.ReadHoldingRegisters, typeof(ushort), EndianType.BigEndian, false, "TestUshort3", "Third batch of 50 ushort registers (non-continuous)")
                    // Test case 4: Float registers - creates float registers at addresses 1000, 1002, 1004, ..., 1060 (31 float registers, 62 register addresses total)
                    .AddRegisters(1, 1000, 1060, ModbusFunction.ReadHoldingRegisters, typeof(float), EndianType.BigEndian, false, "TestFloat", "31 float registers (62 register addresses)")
                    // Test case 8: Extended address range registers (10000-10099) - these should map to 410000-410099 internally
                    .AddRegisters(1, 10000, 10099, ModbusFunction.ReadHoldingRegisters, typeof(ushort), EndianType.BigEndian, false, "TestExtended", "Extended address range registers (10000-10099)")
                    // Test case 9: Higher extended address range (20000-20049) - these should map to 420000-420049 internally
                    .AddRegisters(1, 20000, 20049, ModbusFunction.ReadHoldingRegisters, typeof(int), EndianType.BigEndian, false, "TestHighExtended", "Higher extended address range registers (20000-20049)")
                    // Test case 10: Additional float registers in extended range (15000-15060) - these should map to 415000-415060 internally
                    .AddRegisters(1, 15000, 15060, ModbusFunction.ReadHoldingRegisters, typeof(float), EndianType.BigEndian, false, "TestFloatExtended", "31 float registers in extended range (62 register addresses)")
                    // Test case 5: Large batch of coils to test digital polling limit
                    .AddRegisters(1, 0, 1999, ModbusFunction.ReadCoils, typeof(bool), EndianType.BigEndian, true, "TestCoil", "2000 coils with debounce")
                    // Test case 6: Input registers
                    .AddRegisters(1, 0, 124, ModbusFunction.ReadInputRegisters, typeof(ushort), EndianType.BigEndian, false, "TestInput", "125 input registers")
                    // Test case 7: Discrete inputs
                    .AddRegisters(1, 0, 999, ModbusFunction.ReadDiscreteInputs, typeof(bool), EndianType.BigEndian, false, "TestDiscrete", "1000 discrete inputs")
                    .Build();

                // Wire up events
                master.ConnectionStateChanged += OnConnectionStateChanged;
                master.RegisterValueChanged += OnRegisterValueChanged;
                master.ModbusError += OnModbusError;

                // Add to masters collection
                _masters[testEndpointId] = master;

                // Start the master
                await master.StartAsync(cancellationToken);

                _logger.LogInformation("Test master initialized and started for {EndpointId}", testEndpointId);


            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize test configuration for {IpAddress}:{Port}", testIpAddress, testPort);
                throw;
            }
        }
        // {
        //     const string testEndpointId = "Marco-PC";
        //     const string testIpAddress = "**************0";
        //     const ushort testPort = 503;
        //     try
        //     {

        //         _logger.LogInformation("Initializing test configuration for {IpAddress}:{Port}", testIpAddress, testPort);

        //         // Create test master with comprehensive register configuration
        //         var master = ModbusTcpMasterFactory
        //             .CreateForEndpoint(testEndpointId, testIpAddress, testPort, _loggerFactory)
        //             .WithProtocol(ModbusProtocol.ModbusTcp)
        //             .WithWriteMode(WriteMode.Multiple)
        //             .WithAddressMode(AddressMode.ZeroBased)
        //             .WithTimeouts(500, 500)
        //             .WithDelays(0, 0)
        //             .WithMaxPollingQuantities(2000, 125)
        //             .WithRetryPolicy(3, 1000)
        //             .WithParallelization(1, false)
        //             .WithDebounceTime(50)
        //             // Add test registers for different data types - using continuous addresses for better grouping
        //             .AddRegisters(1, 0, 99, ModbusFunction.ReadHoldingRegisters, typeof(ushort), EndianType.BigEndian, false, "TestReg", "Test holding register batch")
        //             // Batch import coils with debounce enabled
        //             .AddRegisters(1, 0, 199, ModbusFunction.ReadCoils, typeof(bool), EndianType.BigEndian, true, "TestCoil", "Test coil with debounce")
        //             // Batch import input registers
        //             .AddRegisters(1, 0, 49, ModbusFunction.ReadInputRegisters, typeof(ushort), EndianType.BigEndian, false, "TestInput", "Test input register")
        //             // Batch import discrete inputs
        //             .AddRegisters(1, 0, 19, ModbusFunction.ReadDiscreteInputs, typeof(bool), EndianType.BigEndian, false, "TestDiscrete", "Test discrete input")
        //             .Build();

        //         // Wire up events
        //         master.ConnectionStateChanged += OnConnectionStateChanged;
        //         master.RegisterValueChanged += OnRegisterValueChanged;
        //         master.ModbusError += OnModbusError;

        //         // Add to masters collection
        //         _masters[testEndpointId] = master;

        //         // Start the master
        //         await master.StartAsync(cancellationToken);

        //         _logger.LogInformation("Test master initialized and started for {EndpointId}", testEndpointId);


        //     }
        //     catch (Exception ex)
        //     {
        //         _logger.LogError(ex, "Failed to initialize test configuration for {IpAddress}:{Port}", testIpAddress, testPort);
        //         throw;
        //     }
        // }
    }

    /// <summary>
    /// Gets a master by endpoint ID
    /// </summary>
    /// <param name="endpointId">Endpoint identifier</param>
    /// <returns>Master instance or null if not found</returns>
    public IModbusTcpMaster? GetMaster(string endpointId)
    {
        return _masters.TryGetValue(endpointId, out var master) ? master : null;
    }

    /// <summary>
    /// Gets all master endpoint IDs
    /// </summary>
    /// <returns>Collection of endpoint IDs</returns>
    public IEnumerable<string> GetMasterIds()
    {
        return _masters.Keys;
    }

    /// <summary>
    /// Gets connection status for all masters
    /// </summary>
    /// <returns>Dictionary of endpoint ID to connection state</returns>
    public Dictionary<string, ConnectionState> GetConnectionStatus()
    {
        return _masters.ToDictionary(kvp => kvp.Key, kvp => kvp.Value.ConnectionState);
    }

    /// <summary>
    /// Writes a single coil
    /// </summary>
    /// <param name="endpointId">Endpoint identifier</param>
    /// <param name="slaveId">Slave ID</param>
    /// <param name="address">Coil address</param>
    /// <param name="value">Value to write</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if successful</returns>
    public async Task<bool> WriteSingleCoilAsync(string endpointId, byte slaveId, ushort address, bool value, CancellationToken cancellationToken = default)
    {
        var master = GetMaster(endpointId);
        if (master == null)
        {
            _logger.LogWarning("Master not found for endpoint {EndpointId}", endpointId);
            return false;
        }

        try
        {
            var result = await master.WriteSingleCoilAsync(slaveId, address, value, cancellationToken);
            _logger.LogInformation("Write single coil: Endpoint={EndpointId}, Slave={SlaveId}, Address={Address}, Value={Value}, Result={Result}",
                endpointId, slaveId, address, value, result);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error writing single coil: Endpoint={EndpointId}, Slave={SlaveId}, Address={Address}",
                endpointId, slaveId, address);
            return false;
        }
    }

    /// <summary>
    /// Writes a single register
    /// </summary>
    /// <param name="endpointId">Endpoint identifier</param>
    /// <param name="slaveId">Slave ID</param>
    /// <param name="address">Register address</param>
    /// <param name="value">Value to write</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if successful</returns>
    public async Task<bool> WriteSingleRegisterAsync(string endpointId, byte slaveId, ushort address, ushort value, CancellationToken cancellationToken = default)
    {
        var master = GetMaster(endpointId);
        if (master == null)
        {
            _logger.LogWarning("Master not found for endpoint {EndpointId}", endpointId);
            return false;
        }

        try
        {
            var result = await master.WriteSingleRegisterAsync(slaveId, address, value, cancellationToken);
            _logger.LogInformation("Write single register: Endpoint={EndpointId}, Slave={SlaveId}, Address={Address}, Value={Value}, Result={Result}",
                endpointId, slaveId, address, value, result);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error writing single register: Endpoint={EndpointId}, Slave={SlaveId}, Address={Address}",
                endpointId, slaveId, address);
            return false;
        }
    }

    /// <summary>
    /// Writes a typed value to a register using the register's configured data type
    /// </summary>
    /// <param name="endpointId">Endpoint identifier</param>
    /// <param name="slaveId">Slave ID</param>
    /// <param name="address">Register address</param>
    /// <param name="value">Value to write (will be converted to the register's configured type)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if successful</returns>
    public async Task<bool> WriteTypedRegisterAsync(string endpointId, byte slaveId, ushort address, object value, CancellationToken cancellationToken = default)
    {
        var master = GetMaster(endpointId);
        if (master == null)
        {
            _logger.LogWarning("Master not found for endpoint {EndpointId}", endpointId);
            return false;
        }

        // Get register definition to determine the correct data type
        var registerDef = GetRegisterDefinition(endpointId, slaveId, address, ModbusFunction.ReadHoldingRegisters);
        if (registerDef == null)
        {
            _logger.LogWarning("Register definition not found: Endpoint={EndpointId}, Slave={SlaveId}, Address={Address}",
                endpointId, slaveId, address);
            return false;
        }

        try
        {
            // Convert the input value to the register's configured type
            var convertedValue = ConvertToTargetType(value, registerDef.DataType);

            var result = await master.WriteTypedValueAsync(slaveId, address, convertedValue, registerDef.DataType, registerDef.EndianType, cancellationToken);

            _logger.LogInformation("Write typed register: Endpoint={EndpointId}, Slave={SlaveId}, Address={Address}, Value={Value}, Type={Type}, Result={Result}",
                endpointId, slaveId, address, convertedValue, registerDef.DataType.Name, result);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error writing typed register: Endpoint={EndpointId}, Slave={SlaveId}, Address={Address}",
                endpointId, slaveId, address);
            return false;
        }
    }

    /// <summary>
    /// Converts a value to the target type
    /// </summary>
    /// <param name="value">Input value</param>
    /// <param name="targetType">Target type</param>
    /// <returns>Converted value</returns>
    private static object ConvertToTargetType(object value, Type targetType)
    {
        if (value == null) throw new ArgumentNullException(nameof(value));

        // If already the correct type, return as-is
        if (value.GetType() == targetType)
            return value;

        // Convert string input to target type
        if (value is string stringValue)
        {
            return targetType switch
            {
                Type t when t == typeof(bool) => bool.Parse(stringValue),
                Type t when t == typeof(byte) => byte.Parse(stringValue),
                Type t when t == typeof(short) => short.Parse(stringValue),
                Type t when t == typeof(ushort) => ushort.Parse(stringValue),
                Type t when t == typeof(int) => int.Parse(stringValue),
                Type t when t == typeof(uint) => uint.Parse(stringValue),
                Type t when t == typeof(long) => long.Parse(stringValue),
                Type t when t == typeof(ulong) => ulong.Parse(stringValue),
                Type t when t == typeof(float) => float.Parse(stringValue),
                Type t when t == typeof(double) => double.Parse(stringValue),
                _ => Convert.ChangeType(stringValue, targetType)
            };
        }

        // Use Convert.ChangeType for other conversions
        return Convert.ChangeType(value, targetType);
    }

    /// <summary>
    /// Gets current register value
    /// </summary>
    /// <param name="endpointId">Endpoint identifier</param>
    /// <param name="slaveId">Slave ID</param>
    /// <param name="address">Register address</param>
    /// <param name="function">Function code</param>
    /// <returns>Current value or null if not available</returns>
    public byte[]? GetCurrentValue(string endpointId, byte slaveId, ushort address, ModbusFunction function)
    {
        var master = GetMaster(endpointId);
        return master?.GetCurrentValue(slaveId, address, function);
    }

    /// <summary>
    /// Gets register definition for a specific register
    /// </summary>
    /// <param name="endpointId">Endpoint identifier</param>
    /// <param name="slaveId">Slave ID</param>
    /// <param name="address">Register address</param>
    /// <param name="function">Function code</param>
    /// <returns>Register definition or null if not found</returns>
    public RegisterDefinition? GetRegisterDefinition(string endpointId, byte slaveId, ushort address, ModbusFunction function)
    {
        var master = GetMaster(endpointId);
        if (master == null) return null;

        return master.PollingRegisters.FirstOrDefault(r =>
            r.SlaveId == slaveId &&
            r.Address == address &&
            r.Function == function);
    }

    /// <summary>
    /// Gets current typed value of a register using its configured data type
    /// </summary>
    /// <param name="endpointId">Endpoint identifier</param>
    /// <param name="slaveId">Slave ID</param>
    /// <param name="address">Register address</param>
    /// <param name="function">Function code</param>
    /// <returns>Current typed value or null if not available</returns>
    public object? GetCurrentTypedValue(string endpointId, byte slaveId, ushort address, ModbusFunction function)
    {
        var master = GetMaster(endpointId);
        if (master == null) return null;

        var registerDef = GetRegisterDefinition(endpointId, slaveId, address, function);
        if (registerDef == null) return null;

        return master.GetCurrentTypedValue(slaveId, address, function, registerDef.DataType, registerDef.EndianType);
    }

    /// <summary>
    /// Handles connection state changes
    /// </summary>
    private void OnConnectionStateChanged(object? sender, ConnectionStateChangedEventArgs e)
    {
        _logger.LogInformation("Connection state changed: Endpoint={EndpointId}, State={State}",
            e.EndpointId, e.CurrentState);
        ConnectionStateChanged?.Invoke(this, e);
    }

    /// <summary>
    /// Handles register value changes
    /// </summary>
    private void OnRegisterValueChanged(object? sender, RegisterValueChangedEventArgs e)
    {
        _logger.LogInformation("Register value changed: Endpoint={EndpointId}, Slave={SlaveId}, Address={Address}, Function={Function}, Value={Value}",
            e.EndpointId, e.SlaveId, e.Address, e.Function, e.CurrentConvertedValue);
        RegisterValueChanged?.Invoke(this, e);
    }

    /// <summary>
    /// Handles Modbus errors
    /// </summary>
    private void OnModbusError(object? sender, ModbusErrorEventArgs e)
    {
        _logger.LogWarning("Modbus error: Endpoint={EndpointId}, Slave={SlaveId}, Function={Function}, Error={Error}",
            e.EndpointId, e.SlaveId, e.Function, e.ErrorMessage);
        ModbusError?.Invoke(this, e);
    }

    /// <summary>
    /// Disposes the service
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _disposed = true;

            _cancellationTokenSource.Cancel();

            foreach (var master in _masters.Values)
            {
                master.Dispose();
            }

            _masters.Clear();
            _cancellationTokenSource.Dispose();
        }
    }
}
