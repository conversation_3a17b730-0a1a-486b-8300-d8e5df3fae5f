using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Ngp.Communication.SipMessageProxy;
using Ngp.Communication.SipMessageProxy.Enums;
using Ngp.Communication.SipMessageProxy.Events;
using Ngp.Communication.SipMessageProxy.Interfaces;
using Ngp.Communication.SipMessageProxy.Models;
using System.Collections.Concurrent;

namespace Ngp.Services;

/// <summary>
/// SIP Message Proxy service for sending SIP SIMPLE messages
/// </summary>
public class SipMessageProxyService : BackgroundService
{
    private readonly ILogger<SipMessageProxyService> _logger;
    private readonly ConcurrentDictionary<string, ISipMessageClient> _clients;
    private readonly ConcurrentDictionary<string, SipMessage> _messages;
    private readonly object _lockObject = new();

    public SipMessageProxyService(ILogger<SipMessageProxyService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _clients = new ConcurrentDictionary<string, ISipMessageClient>();
        _messages = new ConcurrentDictionary<string, SipMessage>();
    }

    /// <summary>
    /// Starts the service and initializes test configuration
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Starting SipMessageProxyService");

        try
        {
            // Initialize test configuration as specified in requirements
            await InitializeTestConfigurationAsync(stoppingToken);

            _logger.LogInformation("SipMessageProxyService started successfully with {ClientCount} clients", _clients.Count);

            // Keep the service running
            while (!stoppingToken.IsCancellationRequested)
            {
                await Task.Delay(1000, stoppingToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start SipMessageProxyService - continuing without SIP functionality");

            // Don't throw the exception to prevent application shutdown
            // The service will continue running but without SIP functionality
            _logger.LogWarning("SipMessageProxyService is running in offline mode");

            // Keep the service running even if SIP initialization failed
            while (!stoppingToken.IsCancellationRequested)
            {
                await Task.Delay(5000, stoppingToken);
            }
        }
    }

    /// <summary>
    /// Initializes test configuration with the specified parameters
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    private async Task InitializeTestConfigurationAsync(CancellationToken cancellationToken)
    {
        const string testClientId = "test-client-880001";
        const string testServerIp = "***********";
        const ushort testServerPort = 6088;
        const string testUsername = "880001";
        const string testPassword = "880001";
        const string testDomain = "sipweema.com.tw"; // Domain provided in the testing requirements

        try
        {
            _logger.LogInformation("Initializing test SIP client for {ServerIp}:{ServerPort}", testServerIp, testServerPort);

            // Create test SIP client using factory with enhanced configuration
            var client = SipMessageClientFactory
                .CreateBuilder()
                .WithServer(testServerIp, testServerPort)
                .WithCredentials(testUsername, testPassword, testDomain)
                .WithTransport(SipTransport.Udp)
                .WithRegistrationExpiry(120) // 2 minutes expiry for better NAT handling
                .WithRegistrationRefreshInterval(30) // Refresh every 30 seconds for NAT traversal
                .WithRetryPolicy(3, TimeSpan.FromSeconds(5))
                .WithConnectionSettings(TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(10)) // 30s timeout, 10s retry
                .WithRequestTimeout(TimeSpan.FromSeconds(30))
                .WithAutoReregister(true)
                .WithLogger(_logger)
                .Build();

            // Wire up events
            client.RegistrationStateChanged += OnRegistrationStateChanged;
            client.MessageStatusChanged += OnMessageStatusChanged;
            client.ConnectionStateChanged += OnConnectionStateChanged;

            // Add to clients collection
            _clients[testClientId] = client;

            // Start the client
            await client.StartAsync(cancellationToken);

            _logger.LogInformation("Test SIP client initialized and started for {ClientId}", testClientId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize test configuration for {ServerIp}:{ServerPort}", testServerIp, testServerPort);
            throw;
        }
    }

    /// <summary>
    /// Sends a SIP message
    /// </summary>
    /// <param name="clientId">Client identifier</param>
    /// <param name="toExtension">Target extension</param>
    /// <param name="content">Message content</param>
    /// <param name="contentType">Content type</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Message ID for tracking</returns>
    public async Task<string> SendMessageAsync(string clientId, string toExtension, string content, string contentType = "text/plain", CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(clientId))
            throw new ArgumentException("Client ID cannot be null or empty", nameof(clientId));

        if (string.IsNullOrWhiteSpace(toExtension))
            throw new ArgumentException("Target extension cannot be null or empty", nameof(toExtension));

        if (string.IsNullOrWhiteSpace(content))
            throw new ArgumentException("Content cannot be null or empty", nameof(content));

        if (!_clients.TryGetValue(clientId, out var client))
            throw new InvalidOperationException($"SIP client '{clientId}' not found or SIP service is offline");

        try
        {
            var messageId = await client.SendMessageAsync(toExtension, content, contentType, cancellationToken);

            _logger.LogInformation("Message sent: ClientId={ClientId}, MessageId={MessageId}, To={ToExtension}, Content={Content}",
                clientId, messageId, toExtension, content);

            return messageId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending message: ClientId={ClientId}, To={ToExtension}",
                clientId, toExtension);
            throw;
        }
    }

    /// <summary>
    /// Gets a SIP client by ID
    /// </summary>
    /// <param name="clientId">Client identifier</param>
    /// <returns>SIP client or null if not found</returns>
    public ISipMessageClient? GetClient(string clientId)
    {
        return _clients.TryGetValue(clientId, out var client) ? client : null;
    }

    /// <summary>
    /// Gets all client IDs
    /// </summary>
    /// <returns>Collection of client IDs</returns>
    public IEnumerable<string> GetClientIds()
    {
        return _clients.Keys;
    }

    /// <summary>
    /// Gets message status by message ID
    /// </summary>
    /// <param name="messageId">Message identifier</param>
    /// <returns>Message status or null if not found</returns>
    public SipMessage? GetMessageStatus(string messageId)
    {
        return _messages.TryGetValue(messageId, out var message) ? message : null;
    }

    /// <summary>
    /// Gets all messages
    /// </summary>
    /// <returns>Collection of messages</returns>
    public IEnumerable<SipMessage> GetAllMessages()
    {
        return _messages.Values;
    }

    /// <summary>
    /// Handles registration state changes
    /// </summary>
    private void OnRegistrationStateChanged(object? sender, RegistrationStateChangedEventArgs e)
    {
        _logger.LogInformation("SIP Registration state changed: {PreviousState} -> {CurrentState}",
            e.PreviousState, e.CurrentState);

        if (!string.IsNullOrEmpty(e.ErrorMessage))
        {
            _logger.LogError("SIP Registration error: {ErrorMessage}", e.ErrorMessage);
        }
    }

    /// <summary>
    /// Handles message status changes
    /// </summary>
    private void OnMessageStatusChanged(object? sender, MessageStatusChangedEventArgs e)
    {
        _logger.LogInformation("SIP Message status changed: {MessageId} {PreviousStatus} -> {CurrentStatus}",
            e.Message.MessageId, e.PreviousStatus, e.CurrentStatus);

        // Update message in collection
        _messages.AddOrUpdate(e.Message.MessageId, e.Message, (key, oldValue) => e.Message);
    }

    /// <summary>
    /// Handles connection state changes
    /// </summary>
    private void OnConnectionStateChanged(object? sender, ConnectionStateChangedEventArgs e)
    {
        _logger.LogInformation("SIP Connection state changed: {PreviousState} -> {CurrentState}",
            e.PreviousState, e.CurrentState);

        if (!string.IsNullOrEmpty(e.ErrorMessage))
        {
            _logger.LogError("SIP Connection error: {ErrorMessage}", e.ErrorMessage);
        }
    }

    /// <summary>
    /// Stops the service and cleans up resources
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Stopping SipMessageProxyService");

        try
        {
            // Stop all clients
            var stopTasks = _clients.Values.Select(client => client.StopAsync(cancellationToken));
            await Task.WhenAll(stopTasks);

            // Clear collections
            _clients.Clear();
            _messages.Clear();

            _logger.LogInformation("SipMessageProxyService stopped successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping SipMessageProxyService");
        }

        await base.StopAsync(cancellationToken);
    }

    /// <summary>
    /// Disposes the service and cleans up resources
    /// </summary>
    public override void Dispose()
    {
        try
        {
            // Dispose all clients
            foreach (var client in _clients.Values)
            {
                client?.Dispose();
            }

            _clients.Clear();
            _messages.Clear();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disposing SipMessageProxyService");
        }

        base.Dispose();
    }
}
