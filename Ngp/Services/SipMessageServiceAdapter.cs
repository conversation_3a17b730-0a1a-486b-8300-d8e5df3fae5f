using Ngp.Shared.Interfaces;

namespace Ngp.Services;

/// <summary>
/// Adapter that bridges SipMessageProxyService to ISipMessageService interface
/// </summary>
public class SipMessageServiceAdapter : ISipMessageService
{
    private readonly SipMessageProxyService _sipMessageProxyService;
    private readonly ILogger<SipMessageServiceAdapter> _logger;

    /// <inheritdoc />
    public bool IsAvailable => _sipMessageProxyService.GetClientIds().Any();

    /// <inheritdoc />
    public event EventHandler<SipServiceAvailabilityChangedEventArgs>? AvailabilityChanged;

    /// <summary>
    /// Initializes a new instance of the SipMessageServiceAdapter class
    /// </summary>
    /// <param name="sipMessageProxyService">SIP message proxy service</param>
    /// <param name="logger">Logger</param>
    public SipMessageServiceAdapter(
        SipMessageProxyService sipMessageProxyService,
        ILogger<SipMessageServiceAdapter> logger)
    {
        _sipMessageProxyService = sipMessageProxyService ?? throw new ArgumentNullException(nameof(sipMessageProxyService));
        _logger = logger;

        // Monitor the proxy service for availability changes
        // Note: SipMessageProxyService doesn't expose availability events directly,
        // so we'll check periodically or assume it's available when running
    }

    /// <inheritdoc />
    public async Task<string> SendMessageAsync(
        string toExtension,
        string content,
        string contentType = "text/plain",
        CancellationToken cancellationToken = default)
    {
        if (!IsAvailable)
        {
            throw new InvalidOperationException("SIP message service is not available");
        }

        try
        {
            // Use the default test client ID from SipMessageProxyService
            const string defaultClientId = "TestClient";
            
            var messageId = await _sipMessageProxyService.SendMessageAsync(
                defaultClientId,
                toExtension,
                content,
                contentType,
                cancellationToken);

            _logger.LogDebug("SIP message sent via adapter: MessageId={MessageId}, To={ToExtension}",
                messageId, toExtension);

            return messageId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send SIP message via adapter to {ToExtension}", toExtension);
            throw;
        }
    }

    /// <summary>
    /// Raises the AvailabilityChanged event
    /// </summary>
    /// <param name="isAvailable">Whether the service is available</param>
    /// <param name="reason">Reason for the change</param>
    private void OnAvailabilityChanged(bool isAvailable, string? reason = null)
    {
        AvailabilityChanged?.Invoke(this, new SipServiceAvailabilityChangedEventArgs(isAvailable, reason));
    }
}
