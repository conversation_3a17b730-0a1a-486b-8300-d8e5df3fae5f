using System.Collections.Concurrent;
using System.Net;
using Ngp.Communication.IpMonitor;
using Ngp.Communication.IpMonitor.Enums;
using Ngp.Communication.IpMonitor.Events;
using Ngp.Communication.IpMonitor.Interfaces;
using Ngp.Communication.IpMonitor.Models;

namespace Ngp.Services;

/// <summary>
/// Service for managing IP monitors
/// </summary>
public class IpMonitorService : IHostedService, IDisposable
{
    private readonly ConcurrentDictionary<string, IIpMonitor> _monitors;
    private readonly ILoggerFactory _loggerFactory;
    private readonly ILogger<IpMonitorService> _logger;
    private bool _disposed = false;

    /// <summary>
    /// Event raised when an IP status changes across any monitor
    /// </summary>
    public event EventHandler<IpStatusChangedEventArgs>? IpStatusChanged;

    /// <summary>
    /// Event raised when a monitor error occurs
    /// </summary>
    public event EventHandler<IpMonitorErrorEventArgs>? MonitorError;

    /// <summary>
    /// Initializes a new instance of the IpMonitorService class
    /// </summary>
    /// <param name="loggerFactory">Logger factory</param>
    public IpMonitorService(ILoggerFactory loggerFactory)
    {
        _loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
        _logger = _loggerFactory.CreateLogger<IpMonitorService>();
        _monitors = new ConcurrentDictionary<string, IIpMonitor>();
    }

    /// <summary>
    /// Starts the service
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("IP Monitor Service starting");
        
        // Initialize test monitor for demonstration
        await InitializeTestMonitorAsync(cancellationToken);
        
        _logger.LogInformation("IP Monitor Service started with {MonitorCount} monitors", _monitors.Count);
    }

    /// <summary>
    /// Stops the service
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("IP Monitor Service stopping");

        var stopTasks = _monitors.Values.Select(monitor => monitor.StopAsync(cancellationToken));
        await Task.WhenAll(stopTasks);

        _logger.LogInformation("IP Monitor Service stopped");
    }

    /// <summary>
    /// Creates a new IP monitor using production settings (Normal mode)
    /// </summary>
    /// <param name="monitorId">Monitor identifier</param>
    /// <param name="ipAddresses">IP addresses to monitor</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if created successfully</returns>
    public async Task<bool> CreateMonitorAsync(string monitorId, IEnumerable<string> ipAddresses, CancellationToken cancellationToken = default)
    {
        try
        {
            if (_monitors.ContainsKey(monitorId))
            {
                _logger.LogWarning("Monitor {MonitorId} already exists", monitorId);
                return false;
            }

            var monitor = IpMonitorFactory.Create(_loggerFactory)
                .WithId(monitorId)
                .WithMonitoringMode(MonitoringMode.Normal)
                .WithDetectionMethod(DetectionMethod.IcmpPing)
                .WithMaxConcurrency(50)
                .WithTimeout(5000)
                .WithPollingInterval(2000)
                .WithRetryPolicy(3, 1000)
                .WithConsecutiveSuccessCount(2)
                .AddIpAddresses(ipAddresses)
                .Build();

            // Wire up events
            monitor.IpStatusChanged += OnIpStatusChanged;
            monitor.MonitorError += OnMonitorError;

            if (_monitors.TryAdd(monitorId, monitor))
            {
                await monitor.StartAsync(cancellationToken);
                _logger.LogInformation("Created and started monitor {MonitorId} with {IpCount} IPs using Normal mode",
                    monitorId, monitor.TotalTargetCount);
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create monitor {MonitorId}", monitorId);
            return false;
        }
    }

    /// <summary>
    /// Creates a new IP monitor using immediate mode
    /// </summary>
    /// <param name="monitorId">Monitor identifier</param>
    /// <param name="ipAddresses">IP addresses to monitor</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if created successfully</returns>
    public async Task<bool> CreateImmediateModeMonitorAsync(string monitorId, IEnumerable<string> ipAddresses, CancellationToken cancellationToken = default)
    {
        try
        {
            if (_monitors.ContainsKey(monitorId))
            {
                _logger.LogWarning("Monitor {MonitorId} already exists", monitorId);
                return false;
            }

            var monitor = IpMonitorFactory.Create(_loggerFactory)
                .WithId(monitorId)
                .WithMonitoringMode(MonitoringMode.Immediate)
                .WithDetectionMethod(DetectionMethod.IcmpPing)
                .WithMaxConcurrency(100)
                .WithTimeout(3000)
                .WithPollingInterval(500)
                .AddIpAddresses(ipAddresses)
                .Build();

            // Wire up events
            monitor.IpStatusChanged += OnIpStatusChanged;
            monitor.MonitorError += OnMonitorError;

            if (_monitors.TryAdd(monitorId, monitor))
            {
                await monitor.StartAsync(cancellationToken);
                _logger.LogInformation("Created and started monitor {MonitorId} with {IpCount} IPs using Immediate mode",
                    monitorId, monitor.TotalTargetCount);
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create immediate mode monitor {MonitorId}", monitorId);
            return false;
        }
    }

    /// <summary>
    /// Creates a new IP monitor using normal mode with custom settings
    /// </summary>
    /// <param name="monitorId">Monitor identifier</param>
    /// <param name="ipAddresses">IP addresses to monitor</param>
    /// <param name="retryCount">Number of retries before marking as offline</param>
    /// <param name="retryDelayMs">Delay between retries in milliseconds</param>
    /// <param name="consecutiveSuccessCount">Consecutive successes needed to mark as online</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if created successfully</returns>
    public async Task<bool> CreateNormalModeMonitorAsync(string monitorId, IEnumerable<string> ipAddresses,
        int retryCount = 3, int retryDelayMs = 1000, int consecutiveSuccessCount = 2, CancellationToken cancellationToken = default)
    {
        try
        {
            if (_monitors.ContainsKey(monitorId))
            {
                _logger.LogWarning("Monitor {MonitorId} already exists", monitorId);
                return false;
            }

            var monitor = IpMonitorFactory.Create(_loggerFactory)
                .WithId(monitorId)
                .WithMonitoringMode(MonitoringMode.Normal)
                .WithDetectionMethod(DetectionMethod.IcmpPing)
                .WithMaxConcurrency(50)
                .WithTimeout(5000)
                .WithPollingInterval(2000)
                .WithRetryPolicy(retryCount, retryDelayMs)
                .WithConsecutiveSuccessCount(consecutiveSuccessCount)
                .AddIpAddresses(ipAddresses)
                .Build();

            // Wire up events
            monitor.IpStatusChanged += OnIpStatusChanged;
            monitor.MonitorError += OnMonitorError;

            if (_monitors.TryAdd(monitorId, monitor))
            {
                await monitor.StartAsync(cancellationToken);
                _logger.LogInformation("Created and started monitor {MonitorId} with {IpCount} IPs using Normal mode (retry: {RetryCount}, success: {SuccessCount})",
                    monitorId, monitor.TotalTargetCount, retryCount, consecutiveSuccessCount);
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create normal mode monitor {MonitorId}", monitorId);
            return false;
        }
    }

    /// <summary>
    /// Creates a new IP monitor using simple mode
    /// </summary>
    /// <param name="monitorId">Monitor identifier</param>
    /// <param name="ipAddresses">IP addresses to monitor</param>
    /// <param name="maintenanceTimeMs">Maintenance time window in milliseconds</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if created successfully</returns>
    public async Task<bool> CreateSimpleModeMonitorAsync(string monitorId, IEnumerable<string> ipAddresses,
        int maintenanceTimeMs = 300000, CancellationToken cancellationToken = default)
    {
        try
        {
            if (_monitors.ContainsKey(monitorId))
            {
                _logger.LogWarning("Monitor {MonitorId} already exists", monitorId);
                return false;
            }

            var monitor = IpMonitorFactory.Create(_loggerFactory)
                .WithId(monitorId)
                .WithMonitoringMode(MonitoringMode.Simple)
                .WithDetectionMethod(DetectionMethod.IcmpPing)
                .WithMaxConcurrency(25)
                .WithTimeout(5000)
                .WithPollingInterval(5000)
                .WithMaintenanceTime(maintenanceTimeMs)
                .AddIpAddresses(ipAddresses)
                .Build();

            // Wire up events
            monitor.IpStatusChanged += OnIpStatusChanged;
            monitor.MonitorError += OnMonitorError;

            if (_monitors.TryAdd(monitorId, monitor))
            {
                await monitor.StartAsync(cancellationToken);
                _logger.LogInformation("Created and started monitor {MonitorId} with {IpCount} IPs using Simple mode (maintenance: {MaintenanceTime}ms)",
                    monitorId, monitor.TotalTargetCount, maintenanceTimeMs);
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create simple mode monitor {MonitorId}", monitorId);
            return false;
        }
    }

    /// <summary>
    /// Removes an IP monitor
    /// </summary>
    /// <param name="monitorId">Monitor identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if removed successfully</returns>
    public async Task<bool> RemoveMonitorAsync(string monitorId, CancellationToken cancellationToken = default)
    {
        try
        {
            if (_monitors.TryRemove(monitorId, out var monitor))
            {
                // Unwire events
                monitor.IpStatusChanged -= OnIpStatusChanged;
                monitor.MonitorError -= OnMonitorError;

                await monitor.StopAsync(cancellationToken);
                monitor.Dispose();

                _logger.LogInformation("Removed monitor {MonitorId}", monitorId);
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to remove monitor {MonitorId}", monitorId);
            return false;
        }
    }

    /// <summary>
    /// Gets all monitors
    /// </summary>
    /// <returns>Dictionary of monitors</returns>
    public IReadOnlyDictionary<string, IIpMonitor> GetAllMonitors()
    {
        return _monitors.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
    }

    /// <summary>
    /// Gets a specific monitor
    /// </summary>
    /// <param name="monitorId">Monitor identifier</param>
    /// <returns>Monitor instance or null if not found</returns>
    public IIpMonitor? GetMonitor(string monitorId)
    {
        return _monitors.TryGetValue(monitorId, out var monitor) ? monitor : null;
    }

    /// <summary>
    /// Gets the status of a specific IP across all monitors
    /// </summary>
    /// <param name="ipAddress">IP address to check</param>
    /// <returns>List of target statuses</returns>
    public List<(string MonitorId, IpMonitorTarget Target)> GetIpStatus(string ipAddress)
    {
        var results = new List<(string MonitorId, IpMonitorTarget Target)>();

        if (!IPAddress.TryParse(ipAddress, out var parsedIp))
            return results;

        foreach (var kvp in _monitors)
        {
            var target = kvp.Value.GetIpStatus(parsedIp);
            if (target != null)
            {
                results.Add((kvp.Key, target));
            }
        }

        return results;
    }

    /// <summary>
    /// Performs an immediate ping test
    /// </summary>
    /// <param name="ipAddress">IP address to ping</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Ping result</returns>
    public async Task<PingResult> PingAsync(string ipAddress, CancellationToken cancellationToken = default)
    {
        // Use the first available monitor for ping testing
        var monitor = _monitors.Values.FirstOrDefault();
        if (monitor != null)
        {
            return await monitor.PingAsync(ipAddress, cancellationToken);
        }

        // Create a temporary monitor for ping testing
        var tempMonitor = IpMonitorFactory.Create(_loggerFactory)
            .WithId("temp")
            .WithMonitoringMode(MonitoringMode.Immediate)
            .WithDetectionMethod(DetectionMethod.IcmpPing)
            .WithMaxConcurrency(10)
            .WithTimeout(5000)
            .WithPollingInterval(1000)
            .AddIpAddress(ipAddress)
            .Build();
        try
        {
            return await tempMonitor.PingAsync(ipAddress, cancellationToken);
        }
        finally
        {
            tempMonitor.Dispose();
        }
    }

    /// <summary>
    /// Initializes a test monitor for demonstration
    /// </summary>
    private async Task InitializeTestMonitorAsync(CancellationToken cancellationToken)
    {
        try
        {
            // Create a test monitor with some common IPs
            var testIps = new[]
            {
                "*******",      // Google DNS
                "*******",     // Cloudflare DNS
                "***********", // Common router IP
                "127.0.0.1",     // Localhost
                "************"
            };

            await CreateMonitorAsync("test-monitor", testIps, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize test monitor");
        }
    }

    /// <summary>
    /// Handles IP status change events
    /// </summary>
    private void OnIpStatusChanged(object? sender, IpStatusChangedEventArgs e)
    {
        _logger.LogInformation("IP status changed: {Summary}", e.GetStatusChangeSummary());
        IpStatusChanged?.Invoke(sender, e);
    }

    /// <summary>
    /// Handles monitor error events
    /// </summary>
    private void OnMonitorError(object? sender, IpMonitorErrorEventArgs e)
    {
        _logger.LogError("Monitor error: {ErrorMessage}", e.GetFormattedErrorMessage());
        MonitorError?.Invoke(sender, e);
    }

    /// <summary>
    /// Disposes the service
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            foreach (var monitor in _monitors.Values)
            {
                try
                {
                    monitor.StopAsync().GetAwaiter().GetResult();
                    monitor.Dispose();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error disposing monitor");
                }
            }

            _monitors.Clear();
            _disposed = true;
        }
    }
}
