namespace Ngp.Services.Interfaces;

/// <summary>
/// Interface for node caching service
/// Designed to be replaceable with <PERSON><PERSON> in the future
/// </summary>
public interface INodeCacheService
{
    /// <summary>
    /// Get node value from cache
    /// </summary>
    /// <param name="nodeId">Node identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Cached node value or null if not found</returns>
    Task<string?> GetNodeValueAsync(string nodeId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Set node value in cache
    /// </summary>
    /// <param name="nodeId">Node identifier</param>
    /// <param name="value">Value to cache</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task SetNodeValueAsync(string nodeId, string value, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get node state from cache (includes value, quality, timestamp)
    /// </summary>
    /// <param name="nodeId">Node identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Cached node state or null if not found</returns>
    Task<NodeCacheState?> GetNodeStateAsync(string nodeId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Set node state in cache
    /// </summary>
    /// <param name="nodeId">Node identifier</param>
    /// <param name="state">Node state to cache</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task SetNodeStateAsync(string nodeId, NodeCacheState state, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Remove node from cache
    /// </summary>
    /// <param name="nodeId">Node identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task RemoveNodeAsync(string nodeId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get multiple node states from cache
    /// </summary>
    /// <param name="nodeIds">Collection of node identifiers</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Dictionary of node states</returns>
    Task<Dictionary<string, NodeCacheState>> GetMultipleNodeStatesAsync(
        IEnumerable<string> nodeIds, 
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Set multiple node states in cache
    /// </summary>
    /// <param name="nodeStates">Dictionary of node states to cache</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task SetMultipleNodeStatesAsync(
        Dictionary<string, NodeCacheState> nodeStates, 
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Check if cache is available and healthy
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if cache is healthy</returns>
    Task<bool> IsHealthyAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Clear all cached data
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    Task ClearAllAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Represents cached node state
/// </summary>
public class NodeCacheState
{
    /// <summary>
    /// Current value of the node
    /// </summary>
    public string Value { get; set; } = string.Empty;
    
    /// <summary>
    /// Quality indicator for the value
    /// </summary>
    public bool IsGoodQuality { get; set; } = true;
    
    /// <summary>
    /// Timestamp when the value was last updated
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// Node type for validation
    /// </summary>
    public string NodeType { get; set; } = string.Empty;
}
