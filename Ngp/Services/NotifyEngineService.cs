using Microsoft.Extensions.Hosting;
using Ngp.Communication.NotifyEngine;
using Ngp.Communication.NotifyEngine.Enums;
using Ngp.Communication.NotifyEngine.Events;
using Ngp.Communication.NotifyEngine.Interfaces;
using Ngp.Communication.NotifyEngine.Models;
using Ngp.Shared.Interfaces;

namespace Ngp.Services;

/// <summary>
/// Service for managing notification engine operations
/// </summary>
public class NotifyEngineService : BackgroundService
{
    private readonly ILogger<NotifyEngineService> _logger;
    private readonly ILoggerFactory _loggerFactory;
    private readonly ISipMessageService? _sipMessageService;
    
    private INotifyEngine? _notifyEngine;
    private readonly object _lock = new();

    /// <summary>
    /// Event raised when notification status changes
    /// </summary>
    public event EventHandler<NotificationStatusChangedEventArgs>? NotificationStatusChanged;

    /// <summary>
    /// Event raised when notification error occurs
    /// </summary>
    public event EventHandler<NotificationErrorEventArgs>? NotificationError;

    /// <summary>
    /// Event raised when provider state changes
    /// </summary>
    public event EventHandler<NotificationProviderStateChangedEventArgs>? ProviderStateChanged;

    /// <summary>
    /// Gets whether the service is running
    /// </summary>
    public bool IsRunning => _notifyEngine?.IsRunning ?? false;

    /// <summary>
    /// Gets the available notification channels
    /// </summary>
    public IReadOnlyList<NotificationChannel> AvailableChannels => 
        _notifyEngine?.AvailableChannels ?? new List<NotificationChannel>();

    /// <summary>
    /// Initializes a new instance of the NotifyEngineService class
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="loggerFactory">Logger factory</param>
    /// <param name="sipMessageService">Optional SIP message service</param>
    public NotifyEngineService(
        ILogger<NotifyEngineService> logger,
        ILoggerFactory loggerFactory,
        ISipMessageService? sipMessageService = null)
    {
        _logger = logger;
        _loggerFactory = loggerFactory;
        _sipMessageService = sipMessageService;
    }

    /// <summary>
    /// Initializes the notification engine for testing
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task InitializeTestEngineAsync(CancellationToken cancellationToken = default)
    {
        lock (_lock)
        {
            if (_notifyEngine != null)
            {
                _logger.LogWarning("NotifyEngine is already initialized");
                return;
            }

            _logger.LogInformation("Initializing test NotifyEngine");

            // Create test notification engine with mock configurations
            var builder = NotifyEngineFactory.Create(_loggerFactory)
                .WithTimeout(30000)
                .WithRetryPolicy(3, 1000)
                .WithConcurrency(5);

            // Configure Email (Gmail test configuration)
            builder.WithEmail(
                smtpHost: "smtp.gmail.com",
                smtpPort: 587,
                username: "weemafile",
                password: "fcqf dhoz lnqv ndds",
                fromEmail: "<EMAIL>",
                fromName: "Weema",
                useSsl: true);

            // Configure SMS (Taiwan SMS test configuration)
            builder.WithSms(
                apiUrl: "http://api.twsms.com/json/sms_send.php",
                username: "twgtm",
                password: "twgtm",
                senderId: "TestSender");

            // Configure LINE (test configuration)
            builder.WithLine(
                channelAccessToken: "test-channel-access-token");

            // Configure SIP Message if service is available
            if (_sipMessageService != null)
            {
                builder.WithSipMessage(_sipMessageService);
            }

            // Build the NotifyEngine (allow partial initialization)
            try
            {
                _notifyEngine = builder.Build();
            }
            catch (AggregateException ex)
            {
                _logger.LogWarning(ex, "Some notification providers failed to initialize, but NotifyEngine will continue with available providers");

                // Try to build with only successful providers
                var successfulBuilder = NotifyEngineFactory.Create(_loggerFactory)
                    .WithTimeout(30000)
                    .WithRetryPolicy(3, 1000)
                    .WithConcurrency(5);

                // Add only providers that can initialize successfully
                try
                {
                    successfulBuilder.WithEmail(
                        smtpHost: "smtp.gmail.com",
                        smtpPort: 587,
                        username: "weemafile",
                        password: "fcqf dhoz lnqv ndds",
                        fromEmail: "<EMAIL>",
                        fromName: "Weema",
                        useSsl: true);
                    _logger.LogInformation("Email provider added successfully");
                }
                catch (Exception emailEx)
                {
                    _logger.LogWarning(emailEx, "Email provider failed to initialize");
                }

                try
                {
                    successfulBuilder.WithSms(
                        apiUrl: "http://api.twsms.com/json/sms_send.php",
                        username: "twgtm",
                        password: "twgtm",
                        senderId: "TestSender");
                    _logger.LogInformation("SMS provider added successfully");
                }
                catch (Exception smsEx)
                {
                    _logger.LogWarning(smsEx, "SMS provider failed to initialize");
                }

                try
                {
                    if (_sipMessageService != null)
                    {
                        successfulBuilder.WithSipMessage(_sipMessageService);
                        _logger.LogInformation("SIP message provider added successfully");
                    }
                }
                catch (Exception sipEx)
                {
                    _logger.LogWarning(sipEx, "SIP message provider failed to initialize");
                }

                try
                {
                    successfulBuilder.WithLine(
                        channelAccessToken: "test-channel-access-token");
                    _logger.LogInformation("LINE provider added successfully");
                }
                catch (Exception lineEx)
                {
                    _logger.LogWarning(lineEx, "LINE provider failed to initialize");
                }

                _notifyEngine = successfulBuilder.Build();
            }

            // Subscribe to events
            _notifyEngine.NotificationStatusChanged += OnNotificationStatusChanged;
            _notifyEngine.NotificationError += OnNotificationError;
            _notifyEngine.ProviderStateChanged += OnProviderStateChanged;

            _logger.LogInformation("Test NotifyEngine initialized with {ChannelCount} channels", 
                _notifyEngine.AvailableChannels.Count);
        }

        if (_notifyEngine != null)
        {
            await _notifyEngine.StartAsync(cancellationToken);
            _logger.LogInformation("Test NotifyEngine started successfully");
        }
    }

    /// <summary>
    /// Sends a test notification to multiple channels
    /// </summary>
    /// <param name="content">Message content</param>
    /// <param name="recipients">Recipients for each channel</param>
    /// <param name="subject">Message subject (for email)</param>
    /// <param name="priority">Message priority</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task with list of message IDs</returns>
    public async Task<IReadOnlyList<string>> SendTestNotificationAsync(
        string content,
        Dictionary<NotificationChannel, List<string>> recipients,
        string? subject = null,
        NotificationPriority priority = NotificationPriority.Normal,
        CancellationToken cancellationToken = default)
    {
        if (_notifyEngine == null)
        {
            throw new InvalidOperationException("NotifyEngine is not initialized. Call InitializeTestEngineAsync first.");
        }

        return await _notifyEngine.SendNotificationAsync(content, recipients, subject, priority, cancellationToken);
    }

    /// <summary>
    /// Sends a test notification to a single channel
    /// </summary>
    /// <param name="channel">Notification channel</param>
    /// <param name="content">Message content</param>
    /// <param name="recipients">List of recipients</param>
    /// <param name="subject">Message subject (for email)</param>
    /// <param name="priority">Message priority</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task with message ID</returns>
    public async Task<string> SendTestNotificationAsync(
        NotificationChannel channel,
        string content,
        IEnumerable<string> recipients,
        string? subject = null,
        NotificationPriority priority = NotificationPriority.Normal,
        CancellationToken cancellationToken = default)
    {
        if (_notifyEngine == null)
        {
            throw new InvalidOperationException("NotifyEngine is not initialized. Call InitializeTestEngineAsync first.");
        }

        return await _notifyEngine.SendNotificationAsync(channel, content, recipients, subject, priority, cancellationToken);
    }

    /// <summary>
    /// Gets the status of a specific message
    /// </summary>
    /// <param name="messageId">Message ID</param>
    /// <returns>Message status or null if not found</returns>
    public NotificationMessage? GetMessageStatus(string messageId)
    {
        return _notifyEngine?.GetMessageStatus(messageId);
    }

    /// <summary>
    /// Gets all messages with optional filtering
    /// </summary>
    /// <param name="channel">Optional channel filter</param>
    /// <param name="status">Optional status filter</param>
    /// <param name="fromDate">Optional from date filter</param>
    /// <param name="toDate">Optional to date filter</param>
    /// <returns>List of messages</returns>
    public IReadOnlyList<NotificationMessage> GetMessages(
        NotificationChannel? channel = null,
        NotificationStatus? status = null,
        DateTime? fromDate = null,
        DateTime? toDate = null)
    {
        return _notifyEngine?.GetMessages(channel, status, fromDate, toDate) ?? new List<NotificationMessage>();
    }

    /// <summary>
    /// Gets notification statistics
    /// </summary>
    /// <returns>Notification statistics</returns>
    public NotificationStatistics GetStatistics()
    {
        return _notifyEngine?.GetStatistics() ?? new NotificationStatistics();
    }

    /// <summary>
    /// Checks if a specific channel is available
    /// </summary>
    /// <param name="channel">Notification channel</param>
    /// <returns>True if available, false otherwise</returns>
    public bool IsChannelAvailable(NotificationChannel channel)
    {
        return _notifyEngine?.IsChannelAvailable(channel) ?? false;
    }

    /// <inheritdoc />
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        try
        {
            // Initialize test engine on startup
            await InitializeTestEngineAsync(stoppingToken);

            // Keep the service running
            while (!stoppingToken.IsCancellationRequested)
            {
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
            }
        }
        catch (OperationCanceledException)
        {
            // Expected when cancellation is requested
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in NotifyEngineService execution");
        }
    }

    /// <inheritdoc />
    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Stopping NotifyEngineService");

        if (_notifyEngine != null)
        {
            await _notifyEngine.StopAsync(cancellationToken);
            _notifyEngine.Dispose();
            _notifyEngine = null;
        }

        await base.StopAsync(cancellationToken);
        _logger.LogInformation("NotifyEngineService stopped");
    }

    /// <summary>
    /// Handles notification status changes
    /// </summary>
    /// <param name="sender">Event sender</param>
    /// <param name="e">Event arguments</param>
    private void OnNotificationStatusChanged(object? sender, NotificationStatusChangedEventArgs e)
    {
        _logger.LogDebug("Notification status changed: {MessageId} from {PreviousStatus} to {CurrentStatus}",
            e.Message.MessageId, e.PreviousStatus, e.CurrentStatus);
        
        NotificationStatusChanged?.Invoke(this, e);
    }

    /// <summary>
    /// Handles notification errors
    /// </summary>
    /// <param name="sender">Event sender</param>
    /// <param name="e">Event arguments</param>
    private void OnNotificationError(object? sender, NotificationErrorEventArgs e)
    {
        _logger.LogError("Notification error for {MessageId} on {Channel}: {ErrorMessage}",
            e.Message.MessageId, e.Channel, e.ErrorMessage);
        
        NotificationError?.Invoke(this, e);
    }

    /// <summary>
    /// Handles provider state changes
    /// </summary>
    /// <param name="sender">Event sender</param>
    /// <param name="e">Event arguments</param>
    private void OnProviderStateChanged(object? sender, NotificationProviderStateChangedEventArgs e)
    {
        _logger.LogInformation("Provider state changed: {Channel} is now {IsAvailable}. Reason: {Reason}",
            e.Channel, e.IsAvailable ? "available" : "unavailable", e.Reason);
        
        ProviderStateChanged?.Invoke(this, e);
    }
}
