using Ngp.Calculation.LogicEngine.Enums;

namespace Ngp.Services;

/// <summary>
/// Demonstration service showcasing the "One Logic Point Per Engine" architecture
/// This is the RECOMMENDED approach for independent input-output calculations
/// Each logic point has its own dedicated engine instance for optimal performance
/// </summary>
public class LogicPointDemoService : IHostedService, IDisposable
{
    private readonly ILogger<LogicPointDemoService> _logger;
    private readonly LogicPointManager _logicPointManager;
    private readonly Timer _dataUpdateTimer;
    private readonly Random _random = new();
    private readonly CancellationTokenSource _cancellationTokenSource = new();
    private bool _disposed = false;

    /// <summary>
    /// Initializes a new instance of the LogicPointDemoService class
    /// </summary>
    /// <param name="logger">Logger instance</param>
    /// <param name="logicPointManager">Logic point manager instance</param>
    public LogicPointDemoService(ILogger<LogicPointDemoService> logger, LogicPointManager logicPointManager)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _logicPointManager = logicPointManager ?? throw new ArgumentNullException(nameof(logicPointManager));
        
        // Timer to update input values with simulated data
        _dataUpdateTimer = new Timer(UpdateSimulatedData, null, Timeout.Infinite, Timeout.Infinite);
    }

    /// <summary>
    /// Starts the service and creates demo logic points
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starting LogicPointDemoService - One Logic Point Per Engine Architecture");

        try
        {
            // Create independent logic points for smart building scenarios
            await CreateSmartBuildingLogicPoints();

            // Wire up events
            WireLogicPointEvents();

            // Start data simulation timer (update every 3 seconds)
            _dataUpdateTimer.Change(TimeSpan.Zero, TimeSpan.FromSeconds(3));

            var pointCount = _logicPointManager.GetLogicPointIds().Count;
            _logger.LogInformation("LogicPointDemoService started successfully with {PointCount} logic points", pointCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start LogicPointDemoService");
            throw;
        }
    }

    /// <summary>
    /// Stops the service
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Stopping LogicPointDemoService");

        _cancellationTokenSource.Cancel();
        _dataUpdateTimer.Change(Timeout.Infinite, Timeout.Infinite);

        _logger.LogInformation("LogicPointDemoService stopped");
        await Task.CompletedTask;
    }

    /// <summary>
    /// Creates smart building logic points demonstrating independent calculations
    /// </summary>
    private async Task CreateSmartBuildingLogicPoints()
    {
        _logger.LogInformation("Creating smart building logic points...");

        // Temperature Control Logic Points (each room has independent control)
        await CreateTemperatureControlPoints();

        // Lighting Control Logic Points (each zone has independent control)
        await CreateLightingControlPoints();

        // Energy Management Logic Points (each meter has independent calculation)
        await CreateEnergyManagementPoints();

        // Security Logic Points (each sensor has independent logic)
        await CreateSecurityPoints();

        _logger.LogInformation("Smart building logic points created successfully");
    }

    /// <summary>
    /// Creates temperature control logic points for different rooms
    /// </summary>
    private async Task CreateTemperatureControlPoints()
    {
        var rooms = new[] { "Room-101", "Room-102", "Room-201", "Room-202", "Conference-A", "Lobby" };

        foreach (var room in rooms)
        {
            // Each room has independent temperature control logic
            var pointId = $"Building-A-{room}-TempControl";
            
            var config = new LogicPointConfig
            {
                InputId = "CurrentTemp",
                OutputId = "HeatingDemand",
                Formula = "IIF(CurrentTemp < 22, (22 - CurrentTemp) * 10, 0)",
                InputDataType = DataType.Float,
                InitialValue = 20.0,
                InputDescription = $"{room} current temperature sensor",
                OutputDescription = "Heating demand percentage (0-100)"
            };

            var success = _logicPointManager.CreateLogicPoint(pointId, config);
            if (success)
            {
                _logger.LogDebug("Created temperature control point: {PointId}", pointId);
            }
            else
            {
                _logger.LogWarning("Failed to create temperature control point: {PointId}", pointId);
            }
        }
    }

    /// <summary>
    /// Creates lighting control logic points for different zones
    /// </summary>
    private async Task CreateLightingControlPoints()
    {
        var zones = new[] { "Corridor-1F", "Corridor-2F", "Office-Area-1", "Office-Area-2", "Parking-Garage" };

        foreach (var zone in zones)
        {
            // Each zone has independent lighting control logic
            var pointId = $"Building-A-{zone}-LightControl";
            
            var config = new LogicPointConfig
            {
                InputId = "OccupancyLevel",
                OutputId = "LightBrightness",
                Formula = "IIF(OccupancyLevel > 0, MIN(100, OccupancyLevel * 20), 0)",
                InputDataType = DataType.Integer,
                InitialValue = 0,
                InputDescription = $"{zone} occupancy sensor reading",
                OutputDescription = "Light brightness percentage (0-100)"
            };

            var success = _logicPointManager.CreateLogicPoint(pointId, config);
            if (success)
            {
                _logger.LogDebug("Created lighting control point: {PointId}", pointId);
            }
        }
    }

    /// <summary>
    /// Creates energy management logic points for different meters
    /// </summary>
    private async Task CreateEnergyManagementPoints()
    {
        var meters = new[] { "Main-Meter", "HVAC-Meter", "Lighting-Meter", "Equipment-Meter" };

        foreach (var meter in meters)
        {
            // Each meter has independent energy calculation logic
            var pointId = $"Building-A-{meter}-EnergyCalc";
            
            var config = new LogicPointConfig
            {
                InputId = "PowerReading",
                OutputId = "EnergyCost",
                Formula = "PowerReading * 0.12",  // $0.12 per kWh
                InputDataType = DataType.Float,
                InitialValue = 0.0,
                InputDescription = $"{meter} power reading in kW",
                OutputDescription = "Energy cost per hour in dollars"
            };

            var success = _logicPointManager.CreateLogicPoint(pointId, config);
            if (success)
            {
                _logger.LogDebug("Created energy management point: {PointId}", pointId);
            }
        }
    }

    /// <summary>
    /// Creates security logic points for different sensors
    /// </summary>
    private async Task CreateSecurityPoints()
    {
        var sensors = new[] { "Door-Main", "Door-Emergency", "Window-1F", "Window-2F", "Motion-Lobby" };

        foreach (var sensor in sensors)
        {
            // Each sensor has independent security logic
            var pointId = $"Building-A-{sensor}-SecurityCheck";
            
            var config = new LogicPointConfig
            {
                InputId = "SensorStatus",
                OutputId = "AlarmLevel",
                Formula = "IIF(SensorStatus > 0, 2, 0)",  // 0=Normal, 2=Alert
                InputDataType = DataType.Integer,
                InitialValue = 0,
                InputDescription = $"{sensor} security sensor status",
                OutputDescription = "Alarm level (0=Normal, 1=Warning, 2=Alert)"
            };

            var success = _logicPointManager.CreateLogicPoint(pointId, config);
            if (success)
            {
                _logger.LogDebug("Created security point: {PointId}", pointId);
            }
        }
    }

    /// <summary>
    /// Wires up logic point events
    /// </summary>
    private void WireLogicPointEvents()
    {
        _logicPointManager.OnLogicPointOutputChanged += (pointId, e) =>
        {
            _logger.LogDebug("Logic point '{PointId}' output changed: {OldValue} → {NewValue}",
                pointId, e.OldValue, e.NewValue);
        };

        _logicPointManager.OnLogicPointError += (pointId, e) =>
        {
            _logger.LogWarning("Logic point '{PointId}' calculation error: {ErrorMessage}",
                pointId, e.ErrorMessage);
        };
    }

    /// <summary>
    /// Updates simulated data for all logic points
    /// </summary>
    /// <param name="state">Timer state</param>
    private void UpdateSimulatedData(object? state)
    {
        if (_disposed || _cancellationTokenSource.Token.IsCancellationRequested)
            return;

        try
        {
            var currentTime = DateTime.Now;
            var normalizedTime = (currentTime.Hour * 3600 + currentTime.Minute * 60 + currentTime.Second) / 86400.0;

            // Update temperature control points
            UpdateTemperatureData(normalizedTime);

            // Update lighting control points
            UpdateLightingData(normalizedTime);

            // Update energy management points
            UpdateEnergyData(normalizedTime);

            // Update security points
            UpdateSecurityData(normalizedTime);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating simulated data");
        }
    }

    /// <summary>
    /// Updates temperature control data
    /// </summary>
    private void UpdateTemperatureData(double normalizedTime)
    {
        var pointIds = _logicPointManager.GetLogicPointIds()
            .Where(id => id.Contains("TempControl"))
            .ToList();

        foreach (var pointId in pointIds)
        {
            // Simulate temperature fluctuations for each room independently
            var baseTemp = 20 + Math.Sin(normalizedTime * 2 * Math.PI + pointId.GetHashCode() % 100) * 4;
            var tempNoise = (_random.NextDouble() - 0.5) * 2;
            var temperature = Math.Max(15, Math.Min(30, baseTemp + tempNoise));

            _logicPointManager.UpdateLogicPointInput(pointId, temperature);
        }
    }

    /// <summary>
    /// Updates lighting control data
    /// </summary>
    private void UpdateLightingData(double normalizedTime)
    {
        var pointIds = _logicPointManager.GetLogicPointIds()
            .Where(id => id.Contains("LightControl"))
            .ToList();

        foreach (var pointId in pointIds)
        {
            // Simulate occupancy changes for each zone independently
            var occupancyProbability = Math.Sin(normalizedTime * 4 * Math.PI + pointId.GetHashCode() % 50) * 0.5 + 0.5;
            var occupancy = _random.NextDouble() < occupancyProbability ? _random.Next(1, 6) : 0;

            _logicPointManager.UpdateLogicPointInput(pointId, occupancy);
        }
    }

    /// <summary>
    /// Updates energy management data
    /// </summary>
    private void UpdateEnergyData(double normalizedTime)
    {
        var pointIds = _logicPointManager.GetLogicPointIds()
            .Where(id => id.Contains("EnergyCalc"))
            .ToList();

        foreach (var pointId in pointIds)
        {
            // Simulate power consumption for each meter independently
            var basePower = 50 + Math.Sin(normalizedTime * 6 * Math.PI + pointId.GetHashCode() % 30) * 30;
            var powerNoise = (_random.NextDouble() - 0.5) * 10;
            var power = Math.Max(0, basePower + powerNoise);

            _logicPointManager.UpdateLogicPointInput(pointId, power);
        }
    }

    /// <summary>
    /// Updates security data
    /// </summary>
    private void UpdateSecurityData(double normalizedTime)
    {
        var pointIds = _logicPointManager.GetLogicPointIds()
            .Where(id => id.Contains("SecurityCheck"))
            .ToList();

        foreach (var pointId in pointIds)
        {
            // Simulate occasional security events
            var eventProbability = 0.05; // 5% chance of security event
            var securityStatus = _random.NextDouble() < eventProbability ? 1 : 0;

            _logicPointManager.UpdateLogicPointInput(pointId, securityStatus);
        }
    }

    /// <summary>
    /// Disposes the service
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;
        _cancellationTokenSource.Cancel();
        _dataUpdateTimer.Dispose();
        _cancellationTokenSource.Dispose();

        _logger.LogInformation("LogicPointDemoService disposed");
        GC.SuppressFinalize(this);
    }
}
