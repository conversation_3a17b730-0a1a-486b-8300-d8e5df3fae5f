using Ngp.Calculation.LogicEngine;
using Ngp.Calculation.LogicEngine.Models;
using Ngp.Calculation.LogicEngine.Enums;
using Ngp.Calculation.LogicEngine.Events;
using System.Collections.Concurrent;

namespace Ngp.Services;

/// <summary>
/// Manager for individual logic points, each with its own engine instance
/// Optimized for scenarios where logic points are independent (1 input → 1 output)
/// </summary>
public class LogicPointManager : IDisposable
{
    private readonly ILogger<LogicPointManager> _logger;
    private readonly ConcurrentDictionary<string, LogicEngine> _logicPoints = new();
    private readonly ConcurrentDictionary<string, LogicPointConfig> _configurations = new();
    private bool _disposed = false;

    public LogicPointManager(ILogger<LogicPointManager> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Creates a new logic point with its own dedicated engine
    /// </summary>
    /// <param name="pointId">Unique identifier for the logic point</param>
    /// <param name="config">Configuration for the logic point</param>
    /// <returns>True if created successfully</returns>
    public bool CreateLogicPoint(string pointId, LogicPointConfig config)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(LogicPointManager));

        if (string.IsNullOrWhiteSpace(pointId))
            throw new ArgumentException("Point ID cannot be null or empty", nameof(pointId));

        if (config == null)
            throw new ArgumentNullException(nameof(config));

        try
        {
            // Check if point already exists
            if (_logicPoints.ContainsKey(pointId))
            {
                _logger.LogWarning("Logic point already exists: {PointId}", pointId);
                return false;
            }

            // Create dedicated engine for this logic point
            var engine = LogicEngineFactory.CreateMemoryEfficient();

            // Register input
            engine.RegisterInput(config.InputId)
                .WithDataType(config.InputDataType)
                .WithInitialValue(config.InitialValue)
                .WithDescription(config.InputDescription ?? "")
                .Complete();

            // Define formula
            engine.DefineFormula(config.OutputId)
                .WithExpression(config.Formula)
                .WithDescription(config.OutputDescription ?? "")
                .WithPriority(1)
                .Complete();

            // Wire up events
            engine.OutputChanged += (sender, e) =>
            {
                try
                {
                    OnLogicPointOutputChanged?.Invoke(pointId, e);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in output changed event for logic point {PointId}", pointId);
                }
            };

            engine.CalculationError += (sender, e) =>
            {
                try
                {
                    OnLogicPointError?.Invoke(pointId, e);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in calculation error event for logic point {PointId}", pointId);
                }
            };

            // Start the engine
            engine.Start();

            // Store engine and configuration
            _logicPoints[pointId] = engine;
            _configurations[pointId] = config;

            _logger.LogInformation("Created logic point: {PointId} with formula: {Formula}", pointId, config.Formula);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create logic point: {PointId}", pointId);
            return false;
        }
    }

    /// <summary>
    /// Updates the input value for a specific logic point
    /// </summary>
    /// <param name="pointId">Logic point identifier</param>
    /// <param name="value">New input value</param>
    /// <returns>True if updated successfully</returns>
    public bool UpdateLogicPointInput(string pointId, object value)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(LogicPointManager));

        if (!_logicPoints.TryGetValue(pointId, out var engine))
        {
            _logger.LogWarning("Logic point not found: {PointId}", pointId);
            return false;
        }

        if (!_configurations.TryGetValue(pointId, out var config))
        {
            _logger.LogWarning("Configuration not found for logic point: {PointId}", pointId);
            return false;
        }

        try
        {
            engine.UpdateInput(config.InputId, value);
            _logger.LogDebug("Updated logic point input: {PointId} = {Value}", pointId, value);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update logic point input: {PointId}", pointId);
            return false;
        }
    }

    /// <summary>
    /// Gets the current output value for a specific logic point
    /// </summary>
    /// <param name="pointId">Logic point identifier</param>
    /// <returns>Current output value or null if not found</returns>
    public object? GetLogicPointOutput(string pointId)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(LogicPointManager));

        if (!_logicPoints.TryGetValue(pointId, out var engine))
        {
            return null;
        }

        if (!_configurations.TryGetValue(pointId, out var config))
        {
            return null;
        }

        try
        {
            return engine.GetFormulaValue(config.OutputId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get logic point output: {PointId}", pointId);
            return null;
        }
    }

    /// <summary>
    /// Removes a logic point and disposes its engine
    /// </summary>
    /// <param name="pointId">Logic point identifier</param>
    /// <returns>True if removed successfully</returns>
    public bool RemoveLogicPoint(string pointId)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(LogicPointManager));

        try
        {
            if (_logicPoints.TryRemove(pointId, out var engine))
            {
                engine.Dispose();
                _configurations.TryRemove(pointId, out _);
                _logger.LogInformation("Removed logic point: {PointId}", pointId);
                return true;
            }

            _logger.LogWarning("Logic point not found for removal: {PointId}", pointId);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to remove logic point: {PointId}", pointId);
            return false;
        }
    }

    /// <summary>
    /// Gets the list of all logic point IDs
    /// </summary>
    /// <returns>List of logic point IDs</returns>
    public IReadOnlyList<string> GetLogicPointIds()
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(LogicPointManager));

        return _logicPoints.Keys.ToList();
    }

    /// <summary>
    /// Gets the status of all logic points
    /// </summary>
    /// <returns>Dictionary of logic point statuses</returns>
    public Dictionary<string, LogicPointStatus> GetLogicPointStatuses()
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(LogicPointManager));

        var statuses = new Dictionary<string, LogicPointStatus>();

        foreach (var kvp in _logicPoints)
        {
            try
            {
                var pointId = kvp.Key;
                var engine = kvp.Value;
                var config = _configurations[pointId];

                var engineStatus = engine.GetStatus();
                var output = GetLogicPointOutput(pointId);

                statuses[pointId] = new LogicPointStatus
                {
                    PointId = pointId,
                    InputId = config.InputId,
                    OutputId = config.OutputId,
                    Formula = config.Formula,
                    CurrentOutput = output,
                    EngineState = engineStatus.State,
                    TotalCalculations = engineStatus.TotalCalculations,
                    MemoryUsageBytes = engineStatus.MemoryUsageBytes,
                    LastUpdateTime = engineStatus.LastUpdateTime
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get status for logic point: {PointId}", kvp.Key);
            }
        }

        return statuses;
    }

    /// <summary>
    /// Event raised when a logic point output changes
    /// </summary>
    public event Action<string, OutputChangedEventArgs>? OnLogicPointOutputChanged;

    /// <summary>
    /// Event raised when a logic point calculation error occurs
    /// </summary>
    public event Action<string, CalculationErrorEventArgs>? OnLogicPointError;

    /// <summary>
    /// Disposes all logic point engines
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;

        foreach (var engine in _logicPoints.Values)
        {
            try
            {
                engine.Dispose();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disposing logic point engine");
            }
        }

        _logicPoints.Clear();
        _configurations.Clear();

        _logger.LogInformation("LogicPointManager disposed with {Count} logic points", _logicPoints.Count);
    }
}

/// <summary>
/// Configuration for a logic point
/// </summary>
public class LogicPointConfig
{
    public required string InputId { get; set; }
    public required string OutputId { get; set; }
    public required string Formula { get; set; }
    public DataType InputDataType { get; set; } = DataType.Double;
    public object InitialValue { get; set; } = 0.0;
    public string? InputDescription { get; set; }
    public string? OutputDescription { get; set; }
}

/// <summary>
/// Status information for a logic point
/// </summary>
public class LogicPointStatus
{
    public required string PointId { get; set; }
    public required string InputId { get; set; }
    public required string OutputId { get; set; }
    public required string Formula { get; set; }
    public object? CurrentOutput { get; set; }
    public EngineState EngineState { get; set; }
    public long TotalCalculations { get; set; }
    public long MemoryUsageBytes { get; set; }
    public DateTime LastUpdateTime { get; set; }
}
