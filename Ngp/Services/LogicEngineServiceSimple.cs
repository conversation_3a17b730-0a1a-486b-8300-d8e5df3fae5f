using Ngp.Calculation.LogicEngine;
using Ngp.Calculation.LogicEngine.Enums;
using Ngp.Calculation.LogicEngine.Events;
using Ngp.Calculation.LogicEngine.Interfaces;
using System.Collections.Concurrent;

namespace Ngp.Services;

/// <summary>
/// Simplified Logic Engine service for demonstration
/// </summary>
public class LogicEngineServiceSimple : IHostedService, IDisposable
{
    private readonly ILogger<LogicEngineServiceSimple> _logger;
    private readonly ConcurrentDictionary<string, ILogicEngine> _engines;
    private readonly CancellationTokenSource _cancellationTokenSource;
    private readonly Timer _dataUpdateTimer;
    private readonly Random _random;
    private bool _disposed = false;

    /// <summary>
    /// Event raised when an output value changes
    /// </summary>
    public event EventHandler<OutputChangedEventArgs>? OutputChanged;

    /// <summary>
    /// Event raised when an input value changes
    /// </summary>
    public event EventHandler<InputChangedEventArgs>? InputChanged;

    /// <summary>
    /// Event raised when a calculation error occurs
    /// </summary>
    public event EventHandler<CalculationErrorEventArgs>? CalculationError;

    /// <summary>
    /// Initializes a new instance of the LogicEngineServiceSimple class
    /// </summary>
    /// <param name="logger">Logger instance</param>
    public LogicEngineServiceSimple(ILogger<LogicEngineServiceSimple> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _engines = new ConcurrentDictionary<string, ILogicEngine>();
        _cancellationTokenSource = new CancellationTokenSource();
        _random = new Random();
        
        // Timer to update input values with simulated data
        _dataUpdateTimer = new Timer(UpdateSimulatedData, null, Timeout.Infinite, Timeout.Infinite);
    }

    /// <summary>
    /// Starts the service and initializes test scenarios
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starting LogicEngineServiceSimple");

        try
        {
            // Initialize test scenarios
            await InitializeTestScenariosAsync(cancellationToken);

            // Start data simulation timer (update every 3 seconds)
            _dataUpdateTimer.Change(TimeSpan.Zero, TimeSpan.FromSeconds(3));

            _logger.LogInformation("LogicEngineServiceSimple started successfully with {EngineCount} engines", _engines.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start LogicEngineServiceSimple");
            throw;
        }
    }

    /// <summary>
    /// Stops the service
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Stopping LogicEngineServiceSimple");

        _cancellationTokenSource.Cancel();
        _dataUpdateTimer.Change(Timeout.Infinite, Timeout.Infinite);

        // Stop all engines
        var stopTasks = _engines.Values.Select(engine => Task.Run(() => engine.Stop()));
        await Task.WhenAll(stopTasks);

        _logger.LogInformation("LogicEngineServiceSimple stopped");
    }

    /// <summary>
    /// Initializes test scenarios
    /// </summary>
    private async Task InitializeTestScenariosAsync(CancellationToken cancellationToken)
    {
        // Scenario 1: Simple Temperature Control
        await CreateTemperatureControlEngine();

        // Scenario 2: Mathematical Calculations
        await CreateMathEngine();

        // Scenario 3: Process Monitoring
        await CreateProcessEngine();
    }

    /// <summary>
    /// Creates a temperature control engine
    /// </summary>
    private async Task CreateTemperatureControlEngine()
    {
        const string engineId = "TemperatureControl";
        
        try
        {
            _logger.LogInformation("Creating Temperature Control engine");

            var engine = LogicEngineFactory.CreateSimple();

            // Temperature inputs
            engine.RegisterInput("CurrentTemp")
                .WithDataType(DataType.Float)
                .WithInitialValue(25.0)
                .WithDescription("Current temperature in Celsius")
                .Complete();

            engine.RegisterInput("SetPoint")
                .WithDataType(DataType.Float)
                .WithInitialValue(22.0)
                .WithDescription("Target temperature")
                .Complete();

            engine.RegisterInput("Humidity")
                .WithDataType(DataType.Float)
                .WithInitialValue(60.0)
                .WithDescription("Relative humidity percentage")
                .Complete();

            // Simple formulas
            engine.DefineFormula("TempError")
                .WithExpression("CurrentTemp - SetPoint")
                .WithDescription("Temperature error")
                .WithPriority(1)
                .Complete();

            engine.DefineFormula("HeatingNeeded")
                .WithExpression("IIF(TempError < -1, 1, 0)")
                .WithDescription("Heating required flag")
                .WithPriority(2)
                .Complete();

            engine.DefineFormula("CoolingNeeded")
                .WithExpression("IIF(TempError > 1, 1, 0)")
                .WithDescription("Cooling required flag")
                .WithPriority(3)
                .Complete();

            engine.DefineFormula("ComfortIndex")
                .WithExpression("100 - ABS(TempError) * 10 - ABS(Humidity - 50) * 0.5")
                .WithDescription("Comfort index calculation")
                .WithPriority(4)
                .Complete();

            // Floor examples for temperature control - integer outputs for display
            engine.DefineFormula("CurrentTempFloored")
                .WithExpression("FLOOR(CurrentTemp)")
                .WithDescription("Current temperature as integer for display")
                .WithPriority(5)
                .Complete();

            engine.DefineFormula("ComfortIndexFloored")
                .WithExpression("FLOOR(100 - ABS(TempError) * 10 - ABS(Humidity - 50) * 0.5)")
                .WithDescription("Comfort index as integer percentage")
                .WithPriority(6)
                .Complete();

            engine.DefineFormula("HumidityFloored")
                .WithExpression("FLOOR(Humidity)")
                .WithDescription("Humidity as integer percentage for display")
                .WithPriority(7)
                .Complete();

            // Wire up events
            WireEngineEvents(engine, engineId);

            // Start engine
            engine.Start();
            _engines[engineId] = engine;

            _logger.LogInformation("Temperature Control engine created and started");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create Temperature Control engine");
            throw;
        }
    }

    /// <summary>
    /// Creates a mathematical calculation engine
    /// </summary>
    private async Task CreateMathEngine()
    {
        const string engineId = "MathCalculations";
        
        try
        {
            _logger.LogInformation("Creating Math Calculations engine");

            var engine = LogicEngineFactory.CreateSimple();

            // Math inputs
            engine.RegisterInput("X")
                .WithDataType(DataType.Double)
                .WithInitialValue(5.0)
                .WithDescription("Variable X")
                .Complete();

            engine.RegisterInput("Y")
                .WithDataType(DataType.Double)
                .WithInitialValue(3.0)
                .WithDescription("Variable Y")
                .Complete();

            engine.RegisterInput("Angle")
                .WithDataType(DataType.Float)
                .WithInitialValue(45.0)
                .WithDescription("Angle in degrees")
                .Complete();

            // Math formulas
            engine.DefineFormula("Sum")
                .WithExpression("X + Y")
                .WithDescription("Sum of X and Y")
                .WithPriority(1)
                .Complete();

            engine.DefineFormula("Product")
                .WithExpression("X * Y")
                .WithDescription("Product of X and Y")
                .WithPriority(2)
                .Complete();

            engine.DefineFormula("Hypotenuse")
                .WithExpression("SQRT(X^2 + Y^2)")
                .WithDescription("Hypotenuse calculation")
                .WithPriority(3)
                .Complete();

            engine.DefineFormula("AngleRadians")
                .WithExpression("Angle * 3.14159 / 180")
                .WithDescription("Angle in radians")
                .WithPriority(4)
                .Complete();

            engine.DefineFormula("SineValue")
                .WithExpression("SIN(AngleRadians)")
                .WithDescription("Sine of angle")
                .WithPriority(5)
                .Complete();

            // Floor examples - demonstrating integer output
            engine.DefineFormula("SumFloored")
                .WithExpression("FLOOR(X + Y)")
                .WithDescription("Sum with floor operation")
                .WithPriority(6)
                .Complete();

            engine.DefineFormula("ProductFloored")
                .WithExpression("FLOOR(X * Y)")
                .WithDescription("Product with floor operation")
                .WithPriority(7)
                .Complete();

            engine.DefineFormula("HypotenuseFloored")
                .WithExpression("FLOOR(SQRT(X^2 + Y^2))")
                .WithDescription("Hypotenuse with floor operation")
                .WithPriority(8)
                .Complete();

            // Wire up events
            WireEngineEvents(engine, engineId);

            // Start engine
            engine.Start();
            _engines[engineId] = engine;

            _logger.LogInformation("Math Calculations engine created and started");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create Math Calculations engine");
            throw;
        }
    }

    /// <summary>
    /// Creates a process monitoring engine
    /// </summary>
    private async Task CreateProcessEngine()
    {
        const string engineId = "ProcessMonitoring";
        
        try
        {
            _logger.LogInformation("Creating Process Monitoring engine");

            var engine = LogicEngineFactory.CreateSimple();

            // Process inputs
            engine.RegisterInput("Pressure")
                .WithDataType(DataType.Float)
                .WithInitialValue(1.5)
                .WithDescription("Process pressure in bar")
                .Complete();

            engine.RegisterInput("FlowRate")
                .WithDataType(DataType.Float)
                .WithInitialValue(100.0)
                .WithDescription("Flow rate in L/min")
                .Complete();

            engine.RegisterInput("Temperature")
                .WithDataType(DataType.Float)
                .WithInitialValue(80.0)
                .WithDescription("Process temperature")
                .Complete();

            // Process formulas
            engine.DefineFormula("Efficiency")
                .WithExpression("(FlowRate * Pressure) / Temperature * 100")
                .WithDescription("Process efficiency")
                .WithPriority(1)
                .Complete();

            engine.DefineFormula("SafetyStatus")
                .WithExpression("IIF((Pressure > 3) OR (Temperature > 120), 0, 1)")
                .WithDescription("Safety status (1=safe, 0=alarm)")
                .WithPriority(2)
                .Complete();

            engine.DefineFormula("PowerConsumption")
                .WithExpression("FlowRate * Pressure * 0.1")
                .WithDescription("Estimated power consumption")
                .WithPriority(3)
                .Complete();

            // Floor examples for process monitoring - integer outputs for display/control
            engine.DefineFormula("EfficiencyFloored")
                .WithExpression("FLOOR((FlowRate * Pressure) / Temperature * 100)")
                .WithDescription("Process efficiency as integer percentage")
                .WithPriority(4)
                .Complete();

            engine.DefineFormula("PowerConsumptionFloored")
                .WithExpression("FLOOR(FlowRate * Pressure * 0.1)")
                .WithDescription("Power consumption as integer watts")
                .WithPriority(5)
                .Complete();

            engine.DefineFormula("FlowRateFloored")
                .WithExpression("FLOOR(FlowRate)")
                .WithDescription("Flow rate as integer L/min for display")
                .WithPriority(6)
                .Complete();

            // More visible floor examples with larger ranges
            engine.DefineFormula("PowerConsumptionScaled")
                .WithExpression("FLOOR(FlowRate * Pressure)")
                .WithDescription("Power consumption scaled (more visible changes)")
                .WithPriority(7)
                .Complete();

            engine.DefineFormula("ProcessScore")
                .WithExpression("FLOOR((FlowRate + Pressure * 10 + Temperature) / 3)")
                .WithDescription("Overall process score as integer")
                .WithPriority(8)
                .Complete();

            engine.DefineFormula("AlarmLevel")
                .WithExpression("FLOOR(IIF(Temperature > 90, 3, IIF(Pressure > 2, 2, 1)))")
                .WithDescription("Discrete alarm level (1=Normal, 2=Warning, 3=Critical)")
                .WithPriority(9)
                .Complete();

            // Wire up events
            WireEngineEvents(engine, engineId);

            // Start engine
            engine.Start();
            _engines[engineId] = engine;

            _logger.LogInformation("Process Monitoring engine created and started");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create Process Monitoring engine");
            throw;
        }
    }

    /// <summary>
    /// Wires up engine events
    /// </summary>
    /// <param name="engine">Engine instance</param>
    /// <param name="engineId">Engine identifier</param>
    private void WireEngineEvents(ILogicEngine engine, string engineId)
    {
        engine.OutputChanged += (sender, e) =>
        {
            _logger.LogDebug("[{EngineId}] Output '{FormulaId}' changed: {OldValue} → {NewValue}",
                engineId, e.FormulaId, e.OldValue, e.NewValue);
            OutputChanged?.Invoke(this, e);
        };

        engine.InputChanged += (sender, e) =>
        {
            _logger.LogDebug("[{EngineId}] Input '{InputId}' changed: {OldValue} → {NewValue}",
                engineId, e.InputId, e.OldValue, e.NewValue);
            InputChanged?.Invoke(this, e);
        };

        engine.CalculationError += (sender, e) =>
        {
            _logger.LogWarning("[{EngineId}] Calculation error in '{FormulaId}': {ErrorMessage}",
                engineId, e.FormulaId, e.ErrorMessage);
            CalculationError?.Invoke(this, e);
        };
    }

    /// <summary>
    /// Updates simulated data for all engines
    /// </summary>
    /// <param name="state">Timer state</param>
    private void UpdateSimulatedData(object? state)
    {
        if (_disposed || _cancellationTokenSource.Token.IsCancellationRequested)
            return;

        try
        {
            var currentTime = DateTime.Now;
            var timeOfDay = currentTime.Hour;
            var normalizedTime = (currentTime.Hour * 3600 + currentTime.Minute * 60 + currentTime.Second) / 86400.0;

            // Update Temperature Control data
            UpdateTemperatureData(normalizedTime);

            // Update Math data
            UpdateMathData(normalizedTime);

            // Update Process data
            UpdateProcessData(normalizedTime);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating simulated data");
        }
    }

    /// <summary>
    /// Updates temperature control data
    /// </summary>
    private void UpdateTemperatureData(double normalizedTime)
    {
        if (!_engines.TryGetValue("TemperatureControl", out var engine))
            return;

        // Simulate temperature fluctuations
        var baseTemp = 22 + Math.Sin(normalizedTime * 2 * Math.PI) * 3;
        var tempNoise = (_random.NextDouble() - 0.5) * 2;
        engine.UpdateInput("CurrentTemp", baseTemp + tempNoise);

        // Simulate humidity changes
        var baseHumidity = 60 + Math.Cos(normalizedTime * 4 * Math.PI) * 15;
        var humidityNoise = (_random.NextDouble() - 0.5) * 5;
        engine.UpdateInput("Humidity", Math.Max(30, Math.Min(90, baseHumidity + humidityNoise)));
    }

    /// <summary>
    /// Updates mathematical data
    /// </summary>
    private void UpdateMathData(double normalizedTime)
    {
        if (!_engines.TryGetValue("MathCalculations", out var engine))
            return;

        // Slowly change X and Y variables
        var x = 5.0 + Math.Sin(normalizedTime * 2 * Math.PI) * 2;
        var y = 3.0 + Math.Cos(normalizedTime * 3 * Math.PI) * 1.5;
        
        engine.UpdateInput("X", x);
        engine.UpdateInput("Y", y);

        // Rotate angle
        var angle = (normalizedTime * 360) % 360;
        engine.UpdateInput("Angle", angle);
    }

    /// <summary>
    /// Updates process monitoring data
    /// </summary>
    private void UpdateProcessData(double normalizedTime)
    {
        if (!_engines.TryGetValue("ProcessMonitoring", out var engine))
            return;

        // Simulate process variations
        var basePressure = 1.5 + Math.Sin(normalizedTime * 6 * Math.PI) * 0.3;
        var pressureNoise = (_random.NextDouble() - 0.5) * 0.1;
        engine.UpdateInput("Pressure", Math.Max(0.5, basePressure + pressureNoise));

        var baseFlow = 100 + Math.Cos(normalizedTime * 4 * Math.PI) * 20;
        var flowNoise = (_random.NextDouble() - 0.5) * 10;
        engine.UpdateInput("FlowRate", Math.Max(50, baseFlow + flowNoise));

        var baseTemp = 80 + Math.Sin(normalizedTime * 8 * Math.PI) * 10;
        var tempNoise = (_random.NextDouble() - 0.5) * 5;
        engine.UpdateInput("Temperature", Math.Max(60, baseTemp + tempNoise));
    }

    /// <summary>
    /// Gets an engine by ID
    /// </summary>
    /// <param name="engineId">Engine identifier</param>
    /// <returns>Engine instance or null if not found</returns>
    public ILogicEngine? GetEngine(string engineId)
    {
        return _engines.TryGetValue(engineId, out var engine) ? engine : null;
    }

    /// <summary>
    /// Gets all engine IDs
    /// </summary>
    /// <returns>Collection of engine IDs</returns>
    public IEnumerable<string> GetEngineIds()
    {
        return _engines.Keys;
    }

    /// <summary>
    /// Gets engine status for all engines
    /// </summary>
    /// <returns>Dictionary of engine ID to status</returns>
    public Dictionary<string, IEngineStatus> GetEngineStatus()
    {
        return _engines.ToDictionary(kvp => kvp.Key, kvp => kvp.Value.GetStatus());
    }

    /// <summary>
    /// Updates an input value for a specific engine
    /// </summary>
    /// <param name="engineId">Engine identifier</param>
    /// <param name="inputId">Input identifier</param>
    /// <param name="value">New value</param>
    /// <returns>True if successful</returns>
    public bool UpdateInput(string engineId, string inputId, object value)
    {
        var engine = GetEngine(engineId);
        if (engine == null)
        {
            _logger.LogWarning("Engine not found: {EngineId}", engineId);
            return false;
        }

        try
        {
            engine.UpdateInput(inputId, value);
            _logger.LogDebug("Updated input: Engine={EngineId}, Input={InputId}, Value={Value}",
                engineId, inputId, value);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating input: Engine={EngineId}, Input={InputId}",
                engineId, inputId);
            return false;
        }
    }

    /// <summary>
    /// Gets current input value
    /// </summary>
    /// <param name="engineId">Engine identifier</param>
    /// <param name="inputId">Input identifier</param>
    /// <returns>Current value or null if not found</returns>
    public object? GetInputValue(string engineId, string inputId)
    {
        var engine = GetEngine(engineId);
        return engine?.GetInputValue(inputId);
    }

    /// <summary>
    /// Gets current formula value
    /// </summary>
    /// <param name="engineId">Engine identifier</param>
    /// <param name="formulaId">Formula identifier</param>
    /// <returns>Current value or null if not found</returns>
    public object? GetFormulaValue(string engineId, string formulaId)
    {
        var engine = GetEngine(engineId);
        return engine?.GetFormulaValue(formulaId);
    }

    /// <summary>
    /// Disposes the service
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _disposed = true;

            _cancellationTokenSource.Cancel();
            _dataUpdateTimer.Dispose();

            foreach (var engine in _engines.Values)
            {
                engine.Stop();
                engine.Dispose();
            }

            _engines.Clear();
            _cancellationTokenSource.Dispose();
        }
    }
}
