using FASTER.core;
using Microsoft.Extensions.Logging;
using Ngp.Services.Interfaces;
using System.Text.Json;
using System.Text;

namespace Ngp.Services;

/// <summary>
/// FasterKV-based implementation of node cache service
/// High-performance in-memory cache without persistence
/// </summary>
public class FasterKvNodeCacheService : INodeCacheService, IDisposable
{
    private readonly ILogger<FasterKvNodeCacheService> _logger;
    private readonly FasterKV<string, string> _cache;
    private readonly ClientSession<string, string, string, string, Empty, IFunctions<string, string, string, string, Empty>> _session;
    private readonly SemaphoreSlim _semaphore;
    private readonly JsonSerializerOptions _jsonOptions;
    private bool _disposed = false;

    public FasterKvNodeCacheService(ILogger<FasterKvNodeCacheService> logger)
    {
        _logger = logger;
        _semaphore = new SemaphoreSlim(1, 1);
        
        // Configure JSON serialization options
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };
        
        try
        {
            // Initialize FasterKV with in-memory storage
            var logSettings = new LogSettings
            {
                LogDevice = Devices.CreateLogDevice("", deleteOnClose: true), // In-memory only
                ObjectLogDevice = Devices.CreateLogDevice("", deleteOnClose: true)
            };
            
            _cache = new FasterKV<string, string>(
                size: 1L << 20, // 1M entries
                logSettings: logSettings,
                serializerSettings: new SerializerSettings<string, string>
                {
                    keySerializer = () => new StringSerializer(),
                    valueSerializer = () => new StringSerializer()
                }
            );
            
            _session = _cache.For(new StringFunctions()).NewSession<StringFunctions>();
            
            _logger.LogInformation("FasterKV node cache service initialized successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize FasterKV node cache service");
            throw;
        }
    }

    public async Task<string?> GetNodeValueAsync(string nodeId, CancellationToken cancellationToken = default)
    {
        var state = await GetNodeStateAsync(nodeId, cancellationToken);
        return state?.Value;
    }

    public async Task SetNodeValueAsync(string nodeId, string value, CancellationToken cancellationToken = default)
    {
        var state = new NodeCacheState
        {
            Value = value,
            LastUpdated = DateTime.UtcNow,
            IsGoodQuality = true
        };
        
        await SetNodeStateAsync(nodeId, state, cancellationToken);
    }

    public async Task<NodeCacheState?> GetNodeStateAsync(string nodeId, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(nodeId))
            return null;

        await _semaphore.WaitAsync(cancellationToken);
        try
        {
            var (status, output) = _session.Read(nodeId);
            
            if (status.Found)
            {
                return JsonSerializer.Deserialize<NodeCacheState>(output, _jsonOptions);
            }
            
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reading node state from cache for nodeId: {NodeId}", nodeId);
            return null;
        }
        finally
        {
            _semaphore.Release();
        }
    }

    public async Task SetNodeStateAsync(string nodeId, NodeCacheState state, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(nodeId) || state == null)
            return;

        await _semaphore.WaitAsync(cancellationToken);
        try
        {
            var json = JsonSerializer.Serialize(state, _jsonOptions);
            _session.Upsert(nodeId, json);
            
            _logger.LogDebug("Node state cached for nodeId: {NodeId}", nodeId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting node state in cache for nodeId: {NodeId}", nodeId);
        }
        finally
        {
            _semaphore.Release();
        }
    }

    public async Task RemoveNodeAsync(string nodeId, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(nodeId))
            return;

        await _semaphore.WaitAsync(cancellationToken);
        try
        {
            _session.Delete(nodeId);
            _logger.LogDebug("Node removed from cache: {NodeId}", nodeId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing node from cache for nodeId: {NodeId}", nodeId);
        }
        finally
        {
            _semaphore.Release();
        }
    }

    public async Task<Dictionary<string, NodeCacheState>> GetMultipleNodeStatesAsync(
        IEnumerable<string> nodeIds,
        CancellationToken cancellationToken = default)
    {
        var result = new Dictionary<string, NodeCacheState>();

        if (nodeIds == null)
            return result;

        await _semaphore.WaitAsync(cancellationToken);
        try
        {
            foreach (var nodeId in nodeIds)
            {
                if (string.IsNullOrEmpty(nodeId))
                    continue;

                var (status, output) = _session.Read(nodeId);

                if (status.Found)
                {
                    var state = JsonSerializer.Deserialize<NodeCacheState>(output, _jsonOptions);
                    if (state != null)
                    {
                        result[nodeId] = state;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reading multiple node states from cache");
        }
        finally
        {
            _semaphore.Release();
        }

        return result;
    }

    public async Task SetMultipleNodeStatesAsync(
        Dictionary<string, NodeCacheState> nodeStates,
        CancellationToken cancellationToken = default)
    {
        if (nodeStates == null || !nodeStates.Any())
            return;

        await _semaphore.WaitAsync(cancellationToken);
        try
        {
            foreach (var kvp in nodeStates)
            {
                if (string.IsNullOrEmpty(kvp.Key) || kvp.Value == null)
                    continue;

                var json = JsonSerializer.Serialize(kvp.Value, _jsonOptions);
                _session.Upsert(kvp.Key, json);
            }

            _logger.LogDebug("Multiple node states cached: {Count} nodes", nodeStates.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting multiple node states in cache");
        }
        finally
        {
            _semaphore.Release();
        }
    }

    public async Task<bool> IsHealthyAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            await _semaphore.WaitAsync(cancellationToken);

            // Simple health check by trying to read a test key
            var testKey = "__health_check__";
            var (status, _) = _session.Read(testKey);

            return true; // If we can perform operations, cache is healthy
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Cache health check failed");
            return false;
        }
        finally
        {
            _semaphore.Release();
        }
    }

    public async Task ClearAllAsync(CancellationToken cancellationToken = default)
    {
        await _semaphore.WaitAsync(cancellationToken);
        try
        {
            // FasterKV doesn't have a direct clear all method
            // This is a limitation, but for in-memory cache, we could restart the service
            _logger.LogWarning("ClearAll operation is not efficiently supported by FasterKV. Consider restarting the service.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing cache");
        }
        finally
        {
            _semaphore.Release();
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            try
            {
                _session?.Dispose();
                _cache?.Dispose();
                _semaphore?.Dispose();

                _logger.LogInformation("FasterKV node cache service disposed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disposing FasterKV node cache service");
            }

            _disposed = true;
        }
    }
}

/// <summary>
/// String serializer for FasterKV
/// </summary>
public class StringSerializer : BinaryObjectSerializer<string>
{
    public override void Deserialize(out string obj)
    {
        var length = reader.ReadInt32();
        var bytes = reader.ReadBytes(length);
        obj = Encoding.UTF8.GetString(bytes);
    }

    public override void Serialize(ref string obj)
    {
        var bytes = Encoding.UTF8.GetBytes(obj);
        writer.Write(bytes.Length);
        writer.Write(bytes);
    }
}

/// <summary>
/// String functions for FasterKV operations
/// </summary>
public class StringFunctions : FunctionsBase<string, string, string, string, Empty>
{
    public override void SingleReader(ref string key, ref string input, ref string value, ref string dst, ref ReadInfo readInfo)
    {
        dst = value;
    }

    public override void SingleWriter(ref string key, ref string input, ref string src, ref string dst, ref WriteInfo writeInfo, ref RecordInfo recordInfo)
    {
        dst = src;
    }

    public override bool ConcurrentReader(ref string key, ref string input, ref string value, ref string dst, ref ReadInfo readInfo, ref RecordInfo recordInfo)
    {
        dst = value;
        return true;
    }

    public override bool ConcurrentWriter(ref string key, ref string input, ref string src, ref string dst, ref WriteInfo writeInfo, ref RecordInfo recordInfo)
    {
        dst = src;
        return true;
    }
}
