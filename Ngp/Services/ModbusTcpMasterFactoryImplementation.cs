using Microsoft.Extensions.Logging;
using Ngp.Shared.Factories;
using Ngp.Communication.ModbusTcpMaster;
using Ngp.Communication.ModbusTcpMaster.Interfaces;
using Ngp.Communication.ModbusTcpMaster.Enums;
using Ngp.Shared.Models;

namespace Ngp.Services;

/// <summary>
/// Implementation of IModbusTcpMasterFactory that creates actual ModbusTcp masters
/// </summary>
public class ModbusTcpMasterFactoryImplementation : IModbusTcpMasterFactory
{
    /// <summary>
    /// Create a ModbusTcp master for Soyal device
    /// </summary>
    public Shared.Factories.IModbusTcpMaster CreateSoyalModbusTcpMaster(string endpointId, string host, int port, ILoggerFactory loggerFactory)
    {
        // Create the actual ModbusTcp master
        var master = Communication.ModbusTcpMaster.ModbusTcpMasterFactory
            .CreateForEndpoint(endpointId, host, (ushort)port, loggerFactory)
            .WithProtocol(ModbusProtocol.ModbusTcp)
            .WithAddressMode(AddressMode.ZeroBased)
            .WithTimeouts(3000, 3000)
            .WithRetryPolicy(3, 1000)
            .WithParallelization(1, false)
            .WithDebounceTime(100)
            // Add registers for Soyal device (Area 1, Node 1)
            .AddRegisters(1, 4352, 4353, Communication.ModbusTcpMaster.Enums.ModbusFunction.ReadDiscreteInputs, typeof(bool))
            .AddRegisters(1, 4352, 4353, Communication.ModbusTcpMaster.Enums.ModbusFunction.ReadCoils, typeof(bool))
            .Build();

        // Wrap it in our adapter
        return new ModbusTcpMasterAdapter(master);
    }
}

/// <summary>
/// Adapter that wraps the actual ModbusTcp master to implement our shared interface
/// </summary>
internal class ModbusTcpMasterAdapter : Shared.Factories.IModbusTcpMaster
{
    private readonly Communication.ModbusTcpMaster.Interfaces.IModbusTcpMaster _master;

    public ModbusTcpMasterAdapter(Communication.ModbusTcpMaster.Interfaces.IModbusTcpMaster master)
    {
        _master = master ?? throw new ArgumentNullException(nameof(master));
        
        // Subscribe to events and convert them
        _master.ConnectionStateChanged += OnConnectionStateChanged;
        _master.RegisterValueChanged += OnRegisterValueChanged;
    }

    public event EventHandler<ConnectionStateChangedEventArgs>? ConnectionStateChanged;
    public event EventHandler<ModbusRegisterValueChangedEventArgs>? RegisterValueChanged;

    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        await _master.StartAsync(cancellationToken);
    }

    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        await _master.StopAsync(cancellationToken);
    }

    public byte[]? GetCurrentValue(byte slaveId, ushort address, Shared.Factories.ModbusFunction function)
    {
        var actualFunction = function switch
        {
            Shared.Factories.ModbusFunction.ReadCoils => Communication.ModbusTcpMaster.Enums.ModbusFunction.ReadCoils,
            Shared.Factories.ModbusFunction.ReadDiscreteInputs => Communication.ModbusTcpMaster.Enums.ModbusFunction.ReadDiscreteInputs,
            Shared.Factories.ModbusFunction.ReadHoldingRegisters => Communication.ModbusTcpMaster.Enums.ModbusFunction.ReadHoldingRegisters,
            Shared.Factories.ModbusFunction.ReadInputRegisters => Communication.ModbusTcpMaster.Enums.ModbusFunction.ReadInputRegisters,
            _ => Communication.ModbusTcpMaster.Enums.ModbusFunction.ReadCoils
        };

        return _master.GetCurrentValue(slaveId, address, actualFunction);
    }

    public async Task<bool> WriteSingleCoilAsync(byte slaveId, ushort address, bool value, CancellationToken cancellationToken = default)
    {
        return await _master.WriteSingleCoilAsync(slaveId, address, value, cancellationToken);
    }

    private void OnConnectionStateChanged(object? sender, Communication.ModbusTcpMaster.Events.ConnectionStateChangedEventArgs e)
    {
        var sharedState = e.CurrentState switch
        {
            Communication.ModbusTcpMaster.Enums.ConnectionState.Disconnected => Shared.Enums.ConnectionState.Disconnected,
            Communication.ModbusTcpMaster.Enums.ConnectionState.Connecting => Shared.Enums.ConnectionState.Connecting,
            Communication.ModbusTcpMaster.Enums.ConnectionState.Connected => Shared.Enums.ConnectionState.Connected,
            Communication.ModbusTcpMaster.Enums.ConnectionState.Disconnecting => Shared.Enums.ConnectionState.Disconnecting,
            Communication.ModbusTcpMaster.Enums.ConnectionState.Error => Shared.Enums.ConnectionState.Error,
            _ => Shared.Enums.ConnectionState.Error
        };

        ConnectionStateChanged?.Invoke(this, new ConnectionStateChangedEventArgs
        {
            EndpointId = e.EndpointId,
            PreviousState = sharedState, // Simplified for this example
            CurrentState = sharedState,
            ErrorMessage = e.ErrorMessage
        });
    }

    private void OnRegisterValueChanged(object? sender, Communication.ModbusTcpMaster.Events.RegisterValueChangedEventArgs e)
    {
        var sharedFunction = e.Function switch
        {
            Communication.ModbusTcpMaster.Enums.ModbusFunction.ReadCoils => Shared.Factories.ModbusFunction.ReadCoils,
            Communication.ModbusTcpMaster.Enums.ModbusFunction.ReadDiscreteInputs => Shared.Factories.ModbusFunction.ReadDiscreteInputs,
            Communication.ModbusTcpMaster.Enums.ModbusFunction.ReadHoldingRegisters => Shared.Factories.ModbusFunction.ReadHoldingRegisters,
            Communication.ModbusTcpMaster.Enums.ModbusFunction.ReadInputRegisters => Shared.Factories.ModbusFunction.ReadInputRegisters,
            _ => Shared.Factories.ModbusFunction.ReadCoils
        };

        var registerType = e.Function switch
        {
            Communication.ModbusTcpMaster.Enums.ModbusFunction.ReadCoils => ModbusRegisterType.Coil,
            Communication.ModbusTcpMaster.Enums.ModbusFunction.ReadDiscreteInputs => ModbusRegisterType.DiscreteInput,
            _ => ModbusRegisterType.Coil
        };

        var currentValue = e.CurrentConvertedValue is bool boolValue ? boolValue : false;
        var previousValue = e.PreviousConvertedValue is bool prevBoolValue ? prevBoolValue : false;

        RegisterValueChanged?.Invoke(this, new ModbusRegisterValueChangedEventArgs
        {
            SlaveId = e.SlaveId,
            Address = e.Address,
            PreviousValue = previousValue,
            CurrentValue = currentValue,
            RegisterType = registerType
        });
    }

    public void Dispose()
    {
        _master?.Dispose();
    }
}