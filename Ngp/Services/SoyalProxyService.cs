using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using Ngp.Shared.Models;
using Ngp.Shared.Factories;
using Ngp.Communication.SoyalProxy.Builders;
using Ngp.Communication.SoyalProxy.Models;

namespace Ngp.Services;

/// <summary>
/// Background service for Soyal access control proxy
/// </summary>
public class SoyalProxyService : BackgroundService
{
    private readonly ILogger<SoyalProxyService> _logger;
    private readonly ILoggerFactory _loggerFactory;
    private readonly Communication.SoyalProxy.Services.SoyalProxyService _soyalProxy;
    
    /// <summary>
    /// Event raised when door status changes
    /// </summary>
    public event EventHandler<DoorStatusChangedEventArgs>? DoorStatusChanged;
    
    /// <summary>
    /// Event raised when connection state changes
    /// </summary>
    public event EventHandler<ConnectionStateChangedEventArgs>? ConnectionStateChanged;

    /// <summary>
    /// Initializes a new instance of SoyalProxyService
    /// </summary>
    /// <param name="logger">Logger instance</param>
    /// <param name="loggerFactory">Logger factory</param>
    /// <param name="serviceProvider">Service provider for dependency injection</param>
    public SoyalProxyService(ILogger<SoyalProxyService> logger, ILoggerFactory loggerFactory, IServiceProvider serviceProvider)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
        
        // Create SoyalProxy instance using builder pattern
        var modbusTcpMasterFactory = serviceProvider.GetRequiredService<IModbusTcpMasterFactory>();
        
        _soyalProxy = SoyalProxyBuilderExtensions.CreateSoyalProxy()
            .WithLogger(_loggerFactory.CreateLogger<Communication.SoyalProxy.Services.SoyalProxyService>())
            .WithModbusTcpMasterFactory(modbusTcpMasterFactory)
            .WithModbusTcpServer("192.168.100.100", 502) // Default test server
            .WithJsonServerPort(1631) // Default JSON port
            .WithEngineCount(10) // 10 parallel engines
            .WithPollingInterval(1000) // Poll every second
            .WithJsonCommandTimeout(5000) // 5 second timeout
            .WithArea(1, 1, 1) // Test area: Area 1, Node 1 only for testing
            .Complete();
        
        // Subscribe to events
        _soyalProxy.DoorStatusChanged += OnDoorStatusChanged;
        _soyalProxy.ConnectionStateChanged += OnConnectionStateChanged;
    }

    /// <summary>
    /// Execute the background service
    /// </summary>
    /// <param name="stoppingToken">Stopping token</param>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        try
        {
            _logger.LogInformation("Starting Soyal proxy background service");
            
            await _soyalProxy.StartAsync(stoppingToken);
            
            _logger.LogInformation("Soyal proxy service started, waiting for cancellation");
            
            // Keep running until cancellation is requested
            await Task.Delay(Timeout.Infinite, stoppingToken);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Soyal proxy service stopping due to cancellation");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Soyal proxy background service");
            throw;
        }
        finally
        {
            try
            {
                await _soyalProxy.StopAsync(CancellationToken.None);
                _logger.LogInformation("Soyal proxy service stopped");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error stopping Soyal proxy service");
            }
        }
    }

    /// <summary>
    /// Get the underlying Soyal proxy service instance
    /// </summary>
    public Communication.SoyalProxy.Services.SoyalProxyService GetSoyalProxyService() => _soyalProxy;

    /// <summary>
    /// Handle door status changed events
    /// </summary>
    private void OnDoorStatusChanged(object? sender, DoorStatusChangedEventArgs e)
    {
        _logger.LogInformation("Door status changed - Area: {Area}, Node: {Node}, Door: {DoorOpen}, Lock: {Locked}",
            e.CurrentStatus.Area, e.CurrentStatus.NodeId, e.CurrentStatus.IsDoorOpen, e.CurrentStatus.IsLocked);
        
        DoorStatusChanged?.Invoke(this, e);
    }

    /// <summary>
    /// Handle connection state changed events
    /// </summary>
    private void OnConnectionStateChanged(object? sender, ConnectionStateChangedEventArgs e)
    {
        _logger.LogInformation("Soyal proxy connection state changed: {PreviousState} -> {CurrentState}",
            e.PreviousState, e.CurrentState);
        
        if (!string.IsNullOrEmpty(e.ErrorMessage))
        {
            _logger.LogWarning("Connection state change error: {ErrorMessage}", e.ErrorMessage);
        }
        
        ConnectionStateChanged?.Invoke(this, e);
    }

    /// <summary>
    /// Dispose resources
    /// </summary>
    public override void Dispose()
    {
        if (_soyalProxy is IDisposable disposable)
        {
            disposable.Dispose();
        }
        
        base.Dispose();
    }
}