using Microsoft.AspNetCore.Mvc;
using Ngp.Models;
using Ngp.Services;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics;

namespace Ngp.Endpoints;

/// <summary>
/// Simplified Logic Engine API endpoints
/// </summary>
public static class LogicEngineEndpointsSimple
{
    /// <summary>
    /// Maps Logic Engine endpoints
    /// </summary>
    /// <param name="app">Web application</param>
    /// <returns>Web application</returns>
    public static WebApplication MapLogicEngineEndpoints(this WebApplication app)
    {
        var group = app.MapGroup("/api/logic-engine")
            .WithTags("Logic Engine")
            .WithOpenApi();

        // System status endpoints
        group.MapGet("/status", GetSystemStatus)
            .WithName("GetLogicEngineSystemStatus")
            .WithSummary("Get system status")
            .WithDescription("Returns the current status of the Logic Engine service and all engines")
            .Produces<ApiResponse<LogicEngineSystemStatus>>(StatusCodes.Status200OK);

        group.MapGet("/engines", GetEngineList)
            .WithName("GetEngineList")
            .WithSummary("Get engine list")
            .WithDescription("Returns a list of all available engines")
            .Produces<ApiResponse<List<EngineStatusInfo>>>(StatusCodes.Status200OK);

        group.MapGet("/engines/{engineId}/status", GetEngineStatus)
            .WithName("GetEngineStatus")
            .WithSummary("Get engine status")
            .WithDescription("Returns the status of a specific engine")
            .Produces<ApiResponse<EngineStatusInfo>>(StatusCodes.Status200OK)
            .Produces<ApiResponse<EngineStatusInfo>>(StatusCodes.Status404NotFound);

        // Input operations
        group.MapPost("/inputs/update", UpdateInput)
            .WithName("UpdateInput")
            .WithSummary("Update input value")
            .WithDescription("Updates the value of an input in a specific engine")
            .Accepts<UpdateInputRequest>("application/json")
            .Produces<ApiResponse<bool>>(StatusCodes.Status200OK)
            .Produces<ApiResponse<bool>>(StatusCodes.Status400BadRequest)
            .Produces<ApiResponse<bool>>(StatusCodes.Status404NotFound);

        group.MapPost("/inputs/read", ReadInputValue)
            .WithName("ReadInputValue")
            .WithSummary("Read input value")
            .WithDescription("Reads the current value of an input")
            .Accepts<ReadValueRequest>("application/json")
            .Produces<ApiResponse<ValueInfo>>(StatusCodes.Status200OK)
            .Produces<ApiResponse<ValueInfo>>(StatusCodes.Status404NotFound);

        // Formula operations
        group.MapPost("/formulas/read", ReadFormulaValue)
            .WithName("ReadFormulaValue")
            .WithSummary("Read formula value")
            .WithDescription("Reads the current value of a formula")
            .Accepts<ReadValueRequest>("application/json")
            .Produces<ApiResponse<ValueInfo>>(StatusCodes.Status200OK)
            .Produces<ApiResponse<ValueInfo>>(StatusCodes.Status404NotFound);

        // Engine-specific endpoints
        group.MapGet("/{engineId}/values", GetEngineValues)
            .WithName("GetEngineValues")
            .WithSummary("Get all engine values")
            .WithDescription("Returns all current values (inputs and formulas) for a specific engine")
            .Produces<ApiResponse<Dictionary<string, object>>>(StatusCodes.Status200OK)
            .Produces<ApiResponse<Dictionary<string, object>>>(StatusCodes.Status404NotFound);

        // Convenience endpoints for specific engines
        group.MapPost("/temperature/update/{inputId}", UpdateTemperatureInput)
            .WithName("UpdateTemperatureInput")
            .WithSummary("Update temperature control input")
            .WithDescription("Updates an input value in the Temperature Control engine")
            .Produces<ApiResponse<bool>>(StatusCodes.Status200OK)
            .Produces<ApiResponse<bool>>(StatusCodes.Status400BadRequest);

        group.MapPost("/math/update/{inputId}", UpdateMathInput)
            .WithName("UpdateMathInput")
            .WithSummary("Update math calculation input")
            .WithDescription("Updates an input value in the Math Calculations engine")
            .Produces<ApiResponse<bool>>(StatusCodes.Status200OK)
            .Produces<ApiResponse<bool>>(StatusCodes.Status400BadRequest);

        group.MapPost("/process/update/{inputId}", UpdateProcessInput)
            .WithName("UpdateProcessInput")
            .WithSummary("Update process monitoring input")
            .WithDescription("Updates an input value in the Process Monitoring engine")
            .Produces<ApiResponse<bool>>(StatusCodes.Status200OK)
            .Produces<ApiResponse<bool>>(StatusCodes.Status400BadRequest);

        // Read endpoints for specific engines
        group.MapGet("/temperature/values", GetTemperatureValues)
            .WithName("GetTemperatureValues")
            .WithSummary("Get temperature control values")
            .WithDescription("Returns all current values from the Temperature Control engine")
            .Produces<ApiResponse<Dictionary<string, object>>>(StatusCodes.Status200OK);

        group.MapGet("/math/values", GetMathValues)
            .WithName("GetMathValues")
            .WithSummary("Get math calculation values")
            .WithDescription("Returns all current values from the Math Calculations engine")
            .Produces<ApiResponse<Dictionary<string, object>>>(StatusCodes.Status200OK);

        group.MapGet("/process/values", GetProcessValues)
            .WithName("GetProcessValues")
            .WithSummary("Get process monitoring values")
            .WithDescription("Returns all current values from the Process Monitoring engine")
            .Produces<ApiResponse<Dictionary<string, object>>>(StatusCodes.Status200OK);

        return app;
    }

    /// <summary>
    /// Gets system status
    /// </summary>
    private static Task<IResult> GetSystemStatus(
        LogicEngineServiceSimple logicEngineService,
        ILogger<LogicEngineServiceSimple> logger)
    {
        try
        {
            var engineStatus = logicEngineService.GetEngineStatus();
            var engines = engineStatus.Select(kvp => new EngineStatusInfo
            {
                EngineId = kvp.Key,
                State = kvp.Value.State,
                StateDescription = kvp.Value.State.ToString(),
                TotalCalculations = kvp.Value.TotalCalculations,
                ActiveFormulas = kvp.Value.ActiveFormulas,
                RegisteredInputs = kvp.Value.RegisteredInputs,
                MemoryUsage = 0, // Simplified - not available in interface
                CalculationsPerSecond = kvp.Value.CalculationsPerSecond,
                LastUpdate = DateTime.UtcNow
            }).ToList();

            var status = new LogicEngineSystemStatus
            {
                ServiceStatus = "Running",
                ActiveEngines = engineStatus.Count,
                Engines = engines,
                Uptime = DateTime.UtcNow - Process.GetCurrentProcess().StartTime,
                TotalCalculations = engines.Sum(e => e.TotalCalculations),
                TotalMemoryUsage = engines.Sum(e => e.MemoryUsage)
            };

            return Task.FromResult(Results.Ok(ApiResponse<LogicEngineSystemStatus>.CreateSuccess(status)));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting system status");
            return Task.FromResult(Results.Ok(ApiResponse<LogicEngineSystemStatus>.CreateError($"Error getting system status: {ex.Message}")));
        }
    }

    /// <summary>
    /// Gets engine list
    /// </summary>
    private static Task<IResult> GetEngineList(
        LogicEngineServiceSimple logicEngineService,
        ILogger<LogicEngineServiceSimple> logger)
    {
        try
        {
            var engineStatus = logicEngineService.GetEngineStatus();
            var engines = engineStatus.Select(kvp => new EngineStatusInfo
            {
                EngineId = kvp.Key,
                State = kvp.Value.State,
                StateDescription = kvp.Value.State.ToString(),
                TotalCalculations = kvp.Value.TotalCalculations,
                ActiveFormulas = kvp.Value.ActiveFormulas,
                RegisteredInputs = kvp.Value.RegisteredInputs,
                MemoryUsage = 0, // Simplified
                CalculationsPerSecond = kvp.Value.CalculationsPerSecond,
                LastUpdate = DateTime.UtcNow
            }).ToList();

            return Task.FromResult(Results.Ok(ApiResponse<List<EngineStatusInfo>>.CreateSuccess(engines)));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting engine list");
            return Task.FromResult(Results.Ok(ApiResponse<List<EngineStatusInfo>>.CreateError($"Error getting engine list: {ex.Message}")));
        }
    }

    /// <summary>
    /// Gets engine status
    /// </summary>
    private static Task<IResult> GetEngineStatus(
        string engineId,
        LogicEngineServiceSimple logicEngineService,
        ILogger<LogicEngineServiceSimple> logger)
    {
        try
        {
            var engine = logicEngineService.GetEngine(engineId);
            if (engine == null)
            {
                return Task.FromResult(Results.NotFound(ApiResponse<EngineStatusInfo>.CreateError($"Engine '{engineId}' not found")));
            }

            var status = engine.GetStatus();
            var engineInfo = new EngineStatusInfo
            {
                EngineId = engineId,
                State = status.State,
                StateDescription = status.State.ToString(),
                TotalCalculations = status.TotalCalculations,
                ActiveFormulas = status.ActiveFormulas,
                RegisteredInputs = status.RegisteredInputs,
                MemoryUsage = 0, // Simplified
                CalculationsPerSecond = status.CalculationsPerSecond,
                LastUpdate = DateTime.UtcNow
            };

            return Task.FromResult(Results.Ok(ApiResponse<EngineStatusInfo>.CreateSuccess(engineInfo)));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting engine status for {EngineId}", engineId);
            return Task.FromResult(Results.Problem(
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError,
                title: "Error getting engine status"));
        }
    }

    /// <summary>
    /// Updates an input value
    /// </summary>
    private static Task<IResult> UpdateInput(
        [FromBody] UpdateInputRequest request,
        LogicEngineServiceSimple logicEngineService,
        ILogger<LogicEngineServiceSimple> logger)
    {
        try
        {
            var success = logicEngineService.UpdateInput(request.EngineId, request.InputId, request.Value);
            
            if (success)
            {
                return Task.FromResult(Results.Ok(ApiResponse<bool>.CreateSuccess(true, "Input updated successfully")));
            }
            else
            {
                return Task.FromResult(Results.BadRequest(ApiResponse<bool>.CreateError("Failed to update input")));
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error updating input: Engine={EngineId}, Input={InputId}", request.EngineId, request.InputId);
            return Task.FromResult(Results.BadRequest(ApiResponse<bool>.CreateError($"Error updating input: {ex.Message}")));
        }
    }

    /// <summary>
    /// Reads an input value
    /// </summary>
    private static Task<IResult> ReadInputValue(
        [FromBody] ReadValueRequest request,
        LogicEngineServiceSimple logicEngineService,
        ILogger<LogicEngineServiceSimple> logger)
    {
        try
        {
            var value = logicEngineService.GetInputValue(request.EngineId, request.ValueId);
            
            if (value != null)
            {
                var valueInfo = new ValueInfo
                {
                    EngineId = request.EngineId,
                    ValueId = request.ValueId,
                    ValueType = "Input",
                    CurrentValue = value,
                    DataType = value.GetType().Name,
                    LastUpdate = DateTime.UtcNow,
                    IsAvailable = true
                };

                return Task.FromResult(Results.Ok(ApiResponse<ValueInfo>.CreateSuccess(valueInfo)));
            }
            else
            {
                return Task.FromResult(Results.NotFound(ApiResponse<ValueInfo>.CreateError($"Input '{request.ValueId}' not found in engine '{request.EngineId}'")));
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error reading input value: Engine={EngineId}, Input={ValueId}", request.EngineId, request.ValueId);
            return Task.FromResult(Results.Problem(
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError,
                title: "Error reading input value"));
        }
    }

    /// <summary>
    /// Reads a formula value
    /// </summary>
    private static Task<IResult> ReadFormulaValue(
        [FromBody] ReadValueRequest request,
        LogicEngineServiceSimple logicEngineService,
        ILogger<LogicEngineServiceSimple> logger)
    {
        try
        {
            var value = logicEngineService.GetFormulaValue(request.EngineId, request.ValueId);
            
            if (value != null)
            {
                var valueInfo = new ValueInfo
                {
                    EngineId = request.EngineId,
                    ValueId = request.ValueId,
                    ValueType = "Formula",
                    CurrentValue = value,
                    DataType = value.GetType().Name,
                    LastUpdate = DateTime.UtcNow,
                    IsAvailable = true
                };

                return Task.FromResult(Results.Ok(ApiResponse<ValueInfo>.CreateSuccess(valueInfo)));
            }
            else
            {
                return Task.FromResult(Results.NotFound(ApiResponse<ValueInfo>.CreateError($"Formula '{request.ValueId}' not found in engine '{request.EngineId}'")));
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error reading formula value: Engine={EngineId}, Formula={ValueId}", request.EngineId, request.ValueId);
            return Task.FromResult(Results.Problem(
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError,
                title: "Error reading formula value"));
        }
    }

    /// <summary>
    /// Gets all values for an engine
    /// </summary>
    private static Task<IResult> GetEngineValues(
        string engineId,
        LogicEngineServiceSimple logicEngineService,
        ILogger<LogicEngineServiceSimple> logger)
    {
        try
        {
            var engine = logicEngineService.GetEngine(engineId);
            if (engine == null)
            {
                return Task.FromResult(Results.NotFound(ApiResponse<Dictionary<string, object>>.CreateError($"Engine '{engineId}' not found")));
            }

            var values = new Dictionary<string, object>();

            // Get all input values
            foreach (var inputId in engine.InputIds)
            {
                var value = engine.GetInputValue(inputId);
                if (value != null)
                {
                    values[$"Input_{inputId}"] = value;
                }
            }

            // Get all formula values
            foreach (var formulaId in engine.FormulaIds)
            {
                var value = engine.GetFormulaValue(formulaId);
                if (value != null)
                {
                    values[$"Formula_{formulaId}"] = value;
                }
            }

            return Task.FromResult(Results.Ok(ApiResponse<Dictionary<string, object>>.CreateSuccess(values)));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting engine values for {EngineId}", engineId);
            return Task.FromResult(Results.Problem(
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError,
                title: "Error getting engine values"));
        }
    }

    // Convenience methods for specific engines
    private static Task<IResult> UpdateTemperatureInput(
        string inputId,
        [FromBody] object value,
        LogicEngineServiceSimple logicEngineService,
        ILogger<LogicEngineServiceSimple> logger)
    {
        return UpdateEngineInput("TemperatureControl", inputId, value, logicEngineService, logger);
    }

    private static Task<IResult> UpdateMathInput(
        string inputId,
        [FromBody] object value,
        LogicEngineServiceSimple logicEngineService,
        ILogger<LogicEngineServiceSimple> logger)
    {
        return UpdateEngineInput("MathCalculations", inputId, value, logicEngineService, logger);
    }

    private static Task<IResult> UpdateProcessInput(
        string inputId,
        [FromBody] object value,
        LogicEngineServiceSimple logicEngineService,
        ILogger<LogicEngineServiceSimple> logger)
    {
        return UpdateEngineInput("ProcessMonitoring", inputId, value, logicEngineService, logger);
    }

    private static Task<IResult> UpdateEngineInput(
        string engineId,
        string inputId,
        object value,
        LogicEngineServiceSimple logicEngineService,
        ILogger<LogicEngineServiceSimple> logger)
    {
        try
        {
            var success = logicEngineService.UpdateInput(engineId, inputId, value);

            if (success)
            {
                return Task.FromResult(Results.Ok(ApiResponse<bool>.CreateSuccess(true, $"Input '{inputId}' updated successfully in engine '{engineId}'")));
            }
            else
            {
                return Task.FromResult(Results.BadRequest(ApiResponse<bool>.CreateError($"Failed to update input '{inputId}' in engine '{engineId}'")));
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error updating input: Engine={EngineId}, Input={InputId}", engineId, inputId);
            return Task.FromResult(Results.BadRequest(ApiResponse<bool>.CreateError($"Error updating input: {ex.Message}")));
        }
    }

    // Read methods for specific engines
    private static Task<IResult> GetTemperatureValues(
        LogicEngineServiceSimple logicEngineService,
        ILogger<LogicEngineServiceSimple> logger)
    {
        return GetEngineValues("TemperatureControl", logicEngineService, logger);
    }

    private static Task<IResult> GetMathValues(
        LogicEngineServiceSimple logicEngineService,
        ILogger<LogicEngineServiceSimple> logger)
    {
        return GetEngineValues("MathCalculations", logicEngineService, logger);
    }

    private static Task<IResult> GetProcessValues(
        LogicEngineServiceSimple logicEngineService,
        ILogger<LogicEngineServiceSimple> logger)
    {
        return GetEngineValues("ProcessMonitoring", logicEngineService, logger);
    }
}
