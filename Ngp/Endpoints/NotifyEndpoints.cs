using Microsoft.AspNetCore.Mvc;
using Ngp.Communication.NotifyEngine.Enums;
using Ngp.Communication.NotifyEngine.Models;
using Ngp.Models;
using Ngp.Services;

namespace Ngp.Endpoints;

/// <summary>
/// Notification engine API endpoints
/// </summary>
public static class NotifyEndpoints
{
    /// <summary>
    /// Maps notification engine endpoints
    /// </summary>
    /// <param name="app">Web application</param>
    /// <returns>Web application</returns>
    public static WebApplication MapNotifyEndpoints(this WebApplication app)
    {
        var group = app.MapGroup("/api/notify")
            .WithTags("Notification Engine")
            .WithOpenApi();

        // System status endpoints
        group.MapGet("/status", GetSystemStatus)
            .WithName("GetNotifySystemStatus")
            .WithSummary("Get notification system status")
            .WithDescription("Returns the current status of the notification engine and all channels")
            .Produces<ApiResponse<NotifyEngineStatusResponse>>(StatusCodes.Status200OK);

        group.MapGet("/statistics", GetStatistics)
            .WithName("GetNotificationStatistics")
            .WithSummary("Get notification statistics")
            .WithDescription("Returns comprehensive statistics for all notification channels")
            .Produces<ApiResponse<NotificationStatisticsResponse>>(StatusCodes.Status200OK);

        // Send notification endpoints
        group.MapPost("/send", SendNotification)
            .WithName("SendNotification")
            .WithSummary("Send notification to multiple channels")
            .WithDescription("Sends a notification message to multiple channels simultaneously")
            .Produces<ApiResponse<SendNotificationResponse>>(StatusCodes.Status200OK)
            .Produces<ApiResponse<SendNotificationResponse>>(StatusCodes.Status400BadRequest);

        group.MapPost("/send/single", SendSingleChannelNotification)
            .WithName("SendSingleChannelNotification")
            .WithSummary("Send notification to single channel")
            .WithDescription("Sends a notification message to a single channel")
            .Produces<ApiResponse<SendSingleChannelNotificationResponse>>(StatusCodes.Status200OK)
            .Produces<ApiResponse<SendSingleChannelNotificationResponse>>(StatusCodes.Status400BadRequest);

        // Test endpoints
        group.MapPost("/test/email", SendTestEmail)
            .WithName("SendTestEmail")
            .WithSummary("Send test email")
            .WithDescription("Sends a test email notification")
            .Produces<ApiResponse<SendSingleChannelNotificationResponse>>(StatusCodes.Status200OK)
            .Produces<ApiResponse<SendSingleChannelNotificationResponse>>(StatusCodes.Status400BadRequest);

        group.MapPost("/test/sms", SendTestSms)
            .WithName("SendTestSms")
            .WithSummary("Send test SMS")
            .WithDescription("Sends a test SMS notification")
            .Produces<ApiResponse<SendSingleChannelNotificationResponse>>(StatusCodes.Status200OK)
            .Produces<ApiResponse<SendSingleChannelNotificationResponse>>(StatusCodes.Status400BadRequest);

        group.MapPost("/test/line", SendTestLine)
            .WithName("SendTestLine")
            .WithSummary("Send test LINE message")
            .WithDescription("Sends a test LINE notification")
            .Produces<ApiResponse<SendSingleChannelNotificationResponse>>(StatusCodes.Status200OK)
            .Produces<ApiResponse<SendSingleChannelNotificationResponse>>(StatusCodes.Status400BadRequest);

        group.MapPost("/test/sip", SendTestSipMessage)
            .WithName("SendTestSipNotification")
            .WithSummary("Send test SIP message")
            .WithDescription("Sends a test SIP message notification")
            .Produces<ApiResponse<SendSingleChannelNotificationResponse>>(StatusCodes.Status200OK)
            .Produces<ApiResponse<SendSingleChannelNotificationResponse>>(StatusCodes.Status400BadRequest);

        // Message tracking endpoints
        group.MapGet("/message/{messageId}", GetMessageStatus)
            .WithName("GetMessageStatus")
            .WithSummary("Get message status")
            .WithDescription("Gets the current status of a specific message")
            .Produces<ApiResponse<NotificationStatusResponse>>(StatusCodes.Status200OK)
            .Produces<ApiResponse<NotificationStatusResponse>>(StatusCodes.Status404NotFound);

        group.MapGet("/messages", GetMessages)
            .WithName("GetMessages")
            .WithSummary("Get messages")
            .WithDescription("Gets a list of messages with optional filtering")
            .Produces<ApiResponse<List<NotificationStatusResponse>>>(StatusCodes.Status200OK);

        // Channel availability endpoints
        group.MapGet("/channels", GetAvailableChannels)
            .WithName("GetAvailableChannels")
            .WithSummary("Get available channels")
            .WithDescription("Gets a list of currently available notification channels")
            .Produces<ApiResponse<List<NotificationChannel>>>(StatusCodes.Status200OK);

        group.MapGet("/channel/{channel}/status", GetChannelStatus)
            .WithName("GetChannelStatus")
            .WithSummary("Get channel status")
            .WithDescription("Gets the availability status of a specific channel")
            .Produces<ApiResponse<bool>>(StatusCodes.Status200OK);

        return app;
    }

    /// <summary>
    /// Gets the system status
    /// </summary>
    private static Task<IResult> GetSystemStatus(
        NotifyEngineService notifyService,
        ILogger<NotifyEngineService> logger)
    {
        try
        {
            var availableChannels = notifyService.AvailableChannels;
            var channelAvailability = new Dictionary<NotificationChannel, bool>();

            foreach (var channel in Enum.GetValues<NotificationChannel>())
            {
                channelAvailability[channel] = notifyService.IsChannelAvailable(channel);
            }

            var status = new NotifyEngineStatusResponse
            {
                IsRunning = notifyService.IsRunning,
                AvailableChannels = availableChannels.ToList(),
                ChannelAvailability = channelAvailability
            };

            return Task.FromResult(Results.Ok(ApiResponse<NotifyEngineStatusResponse>.CreateSuccess(status)));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting notification system status");
            return Task.FromResult(Results.Ok(ApiResponse<NotifyEngineStatusResponse>.CreateError($"Error getting system status: {ex.Message}")));
        }
    }

    /// <summary>
    /// Gets notification statistics
    /// </summary>
    private static Task<IResult> GetStatistics(
        NotifyEngineService notifyService,
        ILogger<NotifyEngineService> logger)
    {
        try
        {
            var stats = notifyService.GetStatistics();
            
            var response = new NotificationStatisticsResponse
            {
                TotalMessages = stats.TotalMessages,
                DeliveredMessages = stats.DeliveredMessages,
                FailedMessages = stats.FailedMessages,
                PendingMessages = stats.PendingMessages,
                DeliveryRate = stats.DeliveryRate,
                FailureRate = stats.FailureRate
            };

            foreach (var channelStat in stats.ChannelStatistics)
            {
                response.ChannelStatistics[channelStat.Key] = new ChannelStatisticsResponse
                {
                    Channel = channelStat.Value.Channel,
                    TotalMessages = channelStat.Value.TotalMessages,
                    DeliveredMessages = channelStat.Value.DeliveredMessages,
                    FailedMessages = channelStat.Value.FailedMessages,
                    PendingMessages = channelStat.Value.PendingMessages,
                    AverageDeliveryTimeMs = channelStat.Value.AverageDeliveryTimeMs,
                    IsAvailable = channelStat.Value.IsAvailable,
                    DeliveryRate = channelStat.Value.DeliveryRate,
                    FailureRate = channelStat.Value.FailureRate
                };
            }

            return Task.FromResult(Results.Ok(ApiResponse<NotificationStatisticsResponse>.CreateSuccess(response)));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting notification statistics");
            return Task.FromResult(Results.Ok(ApiResponse<NotificationStatisticsResponse>.CreateError($"Error getting statistics: {ex.Message}")));
        }
    }

    /// <summary>
    /// Sends a notification to multiple channels
    /// </summary>
    private static async Task<IResult> SendNotification(
        [FromBody] SendNotificationRequest request,
        NotifyEngineService notifyService,
        ILogger<NotifyEngineService> logger,
        CancellationToken cancellationToken)
    {
        try
        {
            var messageIds = await notifyService.SendTestNotificationAsync(
                request.Content,
                request.Recipients,
                request.Subject,
                request.Priority,
                cancellationToken);

            var response = new SendNotificationResponse
            {
                MessageIds = messageIds.ToList(),
                MessageCount = messageIds.Count,
                ChannelsUsed = request.Recipients.Keys.ToList()
            };

            return Results.Ok(ApiResponse<SendNotificationResponse>.CreateSuccess(response));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error sending notification");
            return Results.BadRequest(ApiResponse<SendNotificationResponse>.CreateError($"Error sending notification: {ex.Message}"));
        }
    }

    /// <summary>
    /// Sends a notification to a single channel
    /// </summary>
    private static async Task<IResult> SendSingleChannelNotification(
        [FromBody] SendSingleChannelNotificationRequest request,
        NotifyEngineService notifyService,
        ILogger<NotifyEngineService> logger,
        CancellationToken cancellationToken)
    {
        try
        {
            var messageId = await notifyService.SendTestNotificationAsync(
                request.Channel,
                request.Content,
                request.Recipients,
                request.Subject,
                request.Priority,
                cancellationToken);

            var response = new SendSingleChannelNotificationResponse
            {
                MessageId = messageId,
                Channel = request.Channel,
                RecipientCount = request.Recipients.Count
            };

            return Results.Ok(ApiResponse<SendSingleChannelNotificationResponse>.CreateSuccess(response));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error sending single channel notification");
            return Results.BadRequest(ApiResponse<SendSingleChannelNotificationResponse>.CreateError($"Error sending notification: {ex.Message}"));
        }
    }

    /// <summary>
    /// Sends a test email
    /// </summary>
    private static async Task<IResult> SendTestEmail(
        [FromQuery] string recipient,
        [FromQuery] string? subject,
        [FromQuery] string? content,
        NotifyEngineService notifyService,
        ILogger<NotifyEngineService> logger,
        CancellationToken cancellationToken)
    {
        try
        {
            var messageId = await notifyService.SendTestNotificationAsync(
                NotificationChannel.Email,
                content ?? "This is a test email from the notification engine.",
                new[] { recipient },
                subject ?? "Test Email",
                NotificationPriority.Normal,
                cancellationToken);

            var response = new SendSingleChannelNotificationResponse
            {
                MessageId = messageId,
                Channel = NotificationChannel.Email,
                RecipientCount = 1
            };

            return Results.Ok(ApiResponse<SendSingleChannelNotificationResponse>.CreateSuccess(response));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error sending test email");
            return Results.BadRequest(ApiResponse<SendSingleChannelNotificationResponse>.CreateError($"Error sending test email: {ex.Message}"));
        }
    }

    /// <summary>
    /// Sends a test SMS
    /// </summary>
    private static async Task<IResult> SendTestSms(
        [FromQuery] string recipient,
        [FromQuery] string? content,
        NotifyEngineService notifyService,
        ILogger<NotifyEngineService> logger,
        CancellationToken cancellationToken)
    {
        try
        {
            var messageId = await notifyService.SendTestNotificationAsync(
                NotificationChannel.Sms,
                content ?? "This is a test SMS from the notification engine.",
                new[] { recipient },
                null,
                NotificationPriority.Normal,
                cancellationToken);

            var response = new SendSingleChannelNotificationResponse
            {
                MessageId = messageId,
                Channel = NotificationChannel.Sms,
                RecipientCount = 1
            };

            return Results.Ok(ApiResponse<SendSingleChannelNotificationResponse>.CreateSuccess(response));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error sending test SMS");
            return Results.BadRequest(ApiResponse<SendSingleChannelNotificationResponse>.CreateError($"Error sending test SMS: {ex.Message}"));
        }
    }

    /// <summary>
    /// Sends a test LINE message
    /// </summary>
    private static async Task<IResult> SendTestLine(
        [FromQuery] string recipient,
        [FromQuery] string? content,
        NotifyEngineService notifyService,
        ILogger<NotifyEngineService> logger,
        CancellationToken cancellationToken)
    {
        try
        {
            var messageId = await notifyService.SendTestNotificationAsync(
                NotificationChannel.Line,
                content ?? "This is a test LINE message from the notification engine.",
                new[] { recipient },
                null,
                NotificationPriority.Normal,
                cancellationToken);

            var response = new SendSingleChannelNotificationResponse
            {
                MessageId = messageId,
                Channel = NotificationChannel.Line,
                RecipientCount = 1
            };

            return Results.Ok(ApiResponse<SendSingleChannelNotificationResponse>.CreateSuccess(response));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error sending test LINE message");
            return Results.BadRequest(ApiResponse<SendSingleChannelNotificationResponse>.CreateError($"Error sending test LINE message: {ex.Message}"));
        }
    }

    /// <summary>
    /// Sends a test SIP message
    /// </summary>
    private static async Task<IResult> SendTestSipMessage(
        [FromQuery] string recipient,
        [FromQuery] string? content,
        NotifyEngineService notifyService,
        ILogger<NotifyEngineService> logger,
        CancellationToken cancellationToken)
    {
        try
        {
            var messageId = await notifyService.SendTestNotificationAsync(
                NotificationChannel.SipMessage,
                content ?? "This is a test SIP message from the notification engine.",
                new[] { recipient },
                null,
                NotificationPriority.Normal,
                cancellationToken);

            var response = new SendSingleChannelNotificationResponse
            {
                MessageId = messageId,
                Channel = NotificationChannel.SipMessage,
                RecipientCount = 1
            };

            return Results.Ok(ApiResponse<SendSingleChannelNotificationResponse>.CreateSuccess(response));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error sending test SIP message");
            return Results.BadRequest(ApiResponse<SendSingleChannelNotificationResponse>.CreateError($"Error sending test SIP message: {ex.Message}"));
        }
    }

    /// <summary>
    /// Gets the status of a specific message
    /// </summary>
    private static Task<IResult> GetMessageStatus(
        string messageId,
        NotifyEngineService notifyService,
        ILogger<NotifyEngineService> logger)
    {
        try
        {
            var message = notifyService.GetMessageStatus(messageId);

            if (message == null)
            {
                return Task.FromResult(Results.NotFound(ApiResponse<NotificationStatusResponse>.CreateError("Message not found")));
            }

            var response = MapToNotificationStatusResponse(message);
            return Task.FromResult(Results.Ok(ApiResponse<NotificationStatusResponse>.CreateSuccess(response)));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting message status");
            return Task.FromResult(Results.Ok(ApiResponse<NotificationStatusResponse>.CreateError($"Error getting message status: {ex.Message}")));
        }
    }

    /// <summary>
    /// Gets messages with optional filtering
    /// </summary>
    private static Task<IResult> GetMessages(
        [FromQuery] NotificationChannel? channel,
        [FromQuery] NotificationStatus? status,
        [FromQuery] DateTime? fromDate,
        [FromQuery] DateTime? toDate,
        NotifyEngineService notifyService,
        ILogger<NotifyEngineService> logger)
    {
        try
        {
            var messages = notifyService.GetMessages(channel, status, fromDate, toDate);
            var response = messages.Select(MapToNotificationStatusResponse).ToList();

            return Task.FromResult(Results.Ok(ApiResponse<List<NotificationStatusResponse>>.CreateSuccess(response)));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting messages");
            return Task.FromResult(Results.Ok(ApiResponse<List<NotificationStatusResponse>>.CreateError($"Error getting messages: {ex.Message}")));
        }
    }

    /// <summary>
    /// Gets available channels
    /// </summary>
    private static Task<IResult> GetAvailableChannels(
        NotifyEngineService notifyService,
        ILogger<NotifyEngineService> logger)
    {
        try
        {
            var channels = notifyService.AvailableChannels.ToList();
            return Task.FromResult(Results.Ok(ApiResponse<List<NotificationChannel>>.CreateSuccess(channels)));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting available channels");
            return Task.FromResult(Results.Ok(ApiResponse<List<NotificationChannel>>.CreateError($"Error getting available channels: {ex.Message}")));
        }
    }

    /// <summary>
    /// Gets channel status
    /// </summary>
    private static Task<IResult> GetChannelStatus(
        NotificationChannel channel,
        NotifyEngineService notifyService,
        ILogger<NotifyEngineService> logger)
    {
        try
        {
            var isAvailable = notifyService.IsChannelAvailable(channel);
            return Task.FromResult(Results.Ok(ApiResponse<bool>.CreateSuccess(isAvailable)));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting channel status");
            return Task.FromResult(Results.Ok(ApiResponse<bool>.CreateError($"Error getting channel status: {ex.Message}")));
        }
    }

    /// <summary>
    /// Maps a NotificationMessage to NotificationStatusResponse
    /// </summary>
    /// <param name="message">Notification message</param>
    /// <returns>Notification status response</returns>
    private static NotificationStatusResponse MapToNotificationStatusResponse(NotificationMessage message)
    {
        return new NotificationStatusResponse
        {
            MessageId = message.MessageId,
            Channel = message.Channel,
            Status = message.Status,
            Content = message.Content,
            Subject = message.Subject,
            Recipients = message.Recipients,
            Priority = message.Priority,
            CreatedAt = message.CreatedAt,
            SentAt = message.SentAt,
            DeliveredAt = message.DeliveredAt,
            ErrorMessage = message.ErrorMessage,
            RetryCount = message.RetryCount,
            DurationMs = message.DurationMs,
            IsDelivered = message.IsDelivered,
            IsFailed = message.IsFailed,
            CanRetry = message.CanRetry
        };
    }
}
