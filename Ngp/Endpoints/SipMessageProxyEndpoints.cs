using Microsoft.AspNetCore.Mvc;
using Ngp.Models;
using Ngp.Services;
using System.ComponentModel.DataAnnotations;

namespace Ngp.Endpoints;

/// <summary>
/// SIP Message Proxy API endpoints
/// </summary>
public static class SipMessageProxyEndpoints
{
    /// <summary>
    /// Maps SIP Message Proxy endpoints
    /// </summary>
    /// <param name="app">Web application</param>
    /// <returns>Web application</returns>
    public static WebApplication MapSipMessageProxyEndpoints(this WebApplication app)
    {
        var group = app.MapGroup("/api/sip")
            .WithTags("SIP Message Proxy")
            .WithOpenApi();

        // System status endpoints
        group.MapGet("/status", GetSystemStatus)
            .WithName("GetSipSystemStatus")
            .WithSummary("Get SIP system status")
            .WithDescription("Returns the current status of the SIP message proxy service and all connections")
            .Produces<SipProxyStatusResponse>(StatusCodes.Status200OK);

        // Message operations
        group.MapPost("/message/send", SendMessage)
            .WithName("SendSipMessage")
            .WithSummary("Send SIP message")
            .WithDescription("Sends a SIP SIMPLE message to the specified extension")
            .Accepts<SendMessageRequest>("application/json")
            .Produces<SendMessageResponse>(StatusCodes.Status200OK)
            .Produces<SendMessageResponse>(StatusCodes.Status400BadRequest)
            .Produces<SendMessageResponse>(StatusCodes.Status500InternalServerError);

        group.MapGet("/message/{messageId}/status", GetMessageStatus)
            .WithName("GetSipMessageStatus")
            .WithSummary("Get message status")
            .WithDescription("Gets the current status of a specific message")
            .Produces<MessageStatusResponse>(StatusCodes.Status200OK)
            .Produces<MessageStatusResponse>(StatusCodes.Status404NotFound);

        group.MapGet("/messages", GetAllMessages)
            .WithName("GetAllSipMessages")
            .WithSummary("Get all messages")
            .WithDescription("Gets all messages with optional filtering")
            .Produces<List<MessageStatusResponse>>(StatusCodes.Status200OK);

        // Test endpoints for the specified configuration
        group.MapPost("/test/send", SendTestMessage)
            .WithName("SendTestMessage")
            .WithSummary("Send test SIP message")
            .WithDescription("Sends a test SIP message to extension 880002 using the configured test client")
            .Accepts<TestMessageRequest>("application/json")
            .Produces<SendMessageResponse>(StatusCodes.Status200OK)
            .Produces<SendMessageResponse>(StatusCodes.Status400BadRequest);

        return app;
    }

    /// <summary>
    /// Gets SIP system status
    /// </summary>
    private static Task<IResult> GetSystemStatus(
        SipMessageProxyService sipService,
        ILogger<SipMessageProxyService> logger)
    {
        try
        {
            var clientIds = sipService.GetClientIds().ToList();
            var messages = sipService.GetAllMessages().ToList();

            var status = new SipProxyStatusResponse
            {
                IsRunning = clientIds.Any(),
                RegistrationState = clientIds.Any() ? "Registered" : "Not Registered",
                Configuration = new SipConfigurationInfo
                {
                    ServerIp = "***********",
                    ServerPort = 6088,
                    Extension = "880001",
                    Domain = "sipweema.com.tw"
                },
                Statistics = new MessageStatistics
                {
                    TotalMessages = messages.Count,
                    SuccessfulMessages = messages.Count(m => m.Status == Ngp.Communication.SipMessageProxy.Enums.MessageStatus.Delivered),
                    FailedMessages = messages.Count(m => m.Status == Ngp.Communication.SipMessageProxy.Enums.MessageStatus.Failed),
                    PendingMessages = messages.Count(m => m.Status == Ngp.Communication.SipMessageProxy.Enums.MessageStatus.Queued ||
                                                          m.Status == Ngp.Communication.SipMessageProxy.Enums.MessageStatus.Sending)
                }
            };

            return Task.FromResult(Results.Ok(status));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting SIP system status");
            return Task.FromResult(Results.Problem(
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError,
                title: "Error getting SIP system status"));
        }
    }

    /// <summary>
    /// Sends a SIP message
    /// </summary>
    private static async Task<IResult> SendMessage(
        [FromBody] SendMessageRequest request,
        SipMessageProxyService sipService,
        ILogger<SipMessageProxyService> logger,
        CancellationToken cancellationToken)
    {
        try
        {
            // Validate request
            var validationResults = new List<ValidationResult>();
            var validationContext = new ValidationContext(request);
            if (!Validator.TryValidateObject(request, validationContext, validationResults, true))
            {
                var errors = string.Join(", ", validationResults.Select(r => r.ErrorMessage));
                return Results.BadRequest(new SendMessageResponse
                {
                    Success = false,
                    ErrorMessage = $"Validation failed: {errors}"
                });
            }

            const string testClientId = "test-client-880001";
            var messageId = await sipService.SendMessageAsync(
                testClientId, request.ToExtension, request.Content, request.ContentType, cancellationToken);

            var response = new SendMessageResponse
            {
                Success = true,
                MessageId = messageId,
                Status = "Queued",
                CreatedAt = DateTime.UtcNow
            };

            return Results.Ok(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error sending SIP message");
            return Results.Problem(
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError,
                title: "Error sending SIP message");
        }
    }

    /// <summary>
    /// Gets message status
    /// </summary>
    private static Task<IResult> GetMessageStatus(
        string messageId,
        SipMessageProxyService sipService,
        ILogger<SipMessageProxyService> logger)
    {
        try
        {
            var message = sipService.GetMessageStatus(messageId);
            if (message == null)
            {
                return Task.FromResult(Results.NotFound(new MessageStatusResponse
                {
                    Success = false,
                    ErrorMessage = "Message not found"
                }));
            }

            var response = new MessageStatusResponse
            {
                Success = true,
                MessageId = message.MessageId,
                ToExtension = message.ToExtension,
                Content = message.Content,
                ContentType = message.ContentType,
                Status = message.Status.ToString(),
                CreatedAt = message.CreatedAt,
                SentAt = message.SentAt,
                ResponseAt = message.ResponseAt,
                RetryCount = message.RetryCount,
                ErrorMessage = message.ErrorMessage
            };

            return Task.FromResult(Results.Ok(response));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting message status");
            return Task.FromResult(Results.Problem(
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError,
                title: "Error getting message status"));
        }
    }

    /// <summary>
    /// Gets all messages
    /// </summary>
    private static Task<IResult> GetAllMessages(
        SipMessageProxyService sipService,
        ILogger<SipMessageProxyService> logger)
    {
        try
        {
            var messages = sipService.GetAllMessages()
                .Select(m => new MessageStatusResponse
                {
                    Success = true,
                    MessageId = m.MessageId,
                    ToExtension = m.ToExtension,
                    Content = m.Content,
                    ContentType = m.ContentType,
                    Status = m.Status.ToString(),
                    CreatedAt = m.CreatedAt,
                    SentAt = m.SentAt,
                    ResponseAt = m.ResponseAt,
                    RetryCount = m.RetryCount,
                    ErrorMessage = m.ErrorMessage
                })
                .ToList();

            return Task.FromResult(Results.Ok(messages));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting all messages");
            return Task.FromResult(Results.Problem(
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError,
                title: "Error getting all messages"));
        }
    }

    /// <summary>
    /// Sends a test message to extension 880002
    /// </summary>
    private static async Task<IResult> SendTestMessage(
        [FromBody] TestMessageRequest request,
        SipMessageProxyService sipService,
        ILogger<SipMessageProxyService> logger,
        CancellationToken cancellationToken)
    {
        try
        {
            // Validate request
            var validationResults = new List<ValidationResult>();
            var validationContext = new ValidationContext(request);
            if (!Validator.TryValidateObject(request, validationContext, validationResults, true))
            {
                var errors = string.Join(", ", validationResults.Select(r => r.ErrorMessage));
                return Results.BadRequest(new SendMessageResponse
                {
                    Success = false,
                    ErrorMessage = $"Validation failed: {errors}"
                });
            }

            const string testClientId = "test-client-880001";
            const string testTargetExtension = "880002";
            
            var messageId = await sipService.SendMessageAsync(
                testClientId, testTargetExtension, request.Content, "text/plain", cancellationToken);

            var response = new SendMessageResponse
            {
                Success = true,
                MessageId = messageId,
                Status = "Queued",
                CreatedAt = DateTime.UtcNow
            };

            return Results.Ok(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error sending test SIP message");
            return Results.BadRequest(new SendMessageResponse
            {
                Success = false,
                ErrorMessage = $"Error sending test message: {ex.Message}"
            });
        }
    }
}

/// <summary>
/// Request model for test messages
/// </summary>
public class TestMessageRequest
{
    /// <summary>
    /// Gets or sets the message content
    /// </summary>
    [Required]
    [StringLength(1000, MinimumLength = 1)]
    public string Content { get; set; } = string.Empty;
}
