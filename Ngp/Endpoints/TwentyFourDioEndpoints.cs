using Microsoft.AspNetCore.Mvc;
using Ngp.Models;
using Ngp.Services;
using System.ComponentModel.DataAnnotations;

namespace Ngp.Endpoints;

/// <summary>
/// API endpoints for 24Dio device operations
/// </summary>
public static class TwentyFourDioEndpoints
{
    /// <summary>
    /// Maps 24Dio API endpoints
    /// </summary>
    /// <param name="app">Web application</param>
    public static void MapTwentyFourDioEndpoints(this WebApplication app)
    {
        var group = app.MapGroup("/api/24dio")
            .WithTags("24Dio")
            .WithOpenApi();

        // Get DIO state
        group.MapGet("/{endpointId}/state", GetDioState)
            .WithName("GetDioState")
            .WithSummary("Get current DI/DO states")
            .WithDescription("Retrieves the current state of all DI and DO points for the specified endpoint");

        // Get DI states
        group.MapGet("/{endpointId}/di", GetDI)
            .WithName("GetDI")
            .WithSummary("Get current DI states")
            .WithDescription("Retrieves the current state of all 24 DI points");

        // Get DO states
        group.MapGet("/{endpointId}/do", GetDO)
            .WithName("GetDO")
            .WithSummary("Get current DO states")
            .WithDescription("Retrieves the current state of all 8 DO points");

        // Set DO states
        group.MapPost("/{endpointId}/do/set", SetDO)
            .WithName("SetDO")
            .WithSummary("Set DO points to 1")
            .WithDescription("Sets specific DO points to 1 based on the provided bit pattern");

        // Clear DO states
        group.MapPost("/{endpointId}/do/clear", ClearDO)
            .WithName("ClearDO")
            .WithSummary("Clear DO points to 0")
            .WithDescription("Clears specific DO points to 0 based on the provided bit pattern");

        // Pin DO states
        group.MapPost("/{endpointId}/do/pin", PinDO)
            .WithName("PinDO")
            .WithSummary("Pin all DO points")
            .WithDescription("Sets all DO points to the exact states specified in the bit pattern");

        // Force reconnect
        group.MapPost("/{endpointId}/reconnect", ForceReconnect)
            .WithName("ForceReconnect")
            .WithSummary("Force reconnection")
            .WithDescription("Forces a reconnection to the 24Dio device");

        // List all endpoints
        group.MapGet("/endpoints", ListEndpoints)
            .WithName("ListEndpoints")
            .WithSummary("List all endpoints")
            .WithDescription("Lists all registered 24Dio endpoints and their states");
    }

    /// <summary>
    /// Gets the current DIO state
    /// </summary>
    private static async Task<IResult> GetDioState(
        [FromRoute] string endpointId,
        [FromServices] TwentyFourDioService service)
    {
        var poller = service.GetPoller(endpointId);
        if (poller == null)
        {
            return Results.NotFound($"Endpoint '{endpointId}' not found");
        }

        var currentState = poller.CurrentState;
        var response = new DioStateResponse
        {
            EndpointId = endpointId,
            ConnectionState = poller.ConnectionState.ToString(),
            DiStates = currentState?.GetDIBinaryString(),
            DoStates = currentState?.GetDOBinaryString(),
            Timestamp = currentState?.Timestamp ?? DateTime.UtcNow,
            IsRunning = poller.IsRunning
        };

        return Results.Ok(response);
    }

    /// <summary>
    /// Gets the current DI states
    /// </summary>
    private static async Task<IResult> GetDI(
        [FromRoute] string endpointId,
        [FromServices] TwentyFourDioService service)
    {
        var poller = service.GetPoller(endpointId);
        if (poller == null)
        {
            return Results.NotFound($"Endpoint '{endpointId}' not found");
        }

        try
        {
            var diStates = await poller.GetDIAsync();
            return Results.Ok(new { EndpointId = endpointId, DiStates = diStates, Timestamp = DateTime.UtcNow });
        }
        catch (Exception ex)
        {
            return Results.Problem($"Failed to get DI states: {ex.Message}");
        }
    }

    /// <summary>
    /// Gets the current DO states
    /// </summary>
    private static async Task<IResult> GetDO(
        [FromRoute] string endpointId,
        [FromServices] TwentyFourDioService service)
    {
        var poller = service.GetPoller(endpointId);
        if (poller == null)
        {
            return Results.NotFound($"Endpoint '{endpointId}' not found");
        }

        try
        {
            var doStates = await poller.GetDOAsync();
            return Results.Ok(new { EndpointId = endpointId, DoStates = doStates, Timestamp = DateTime.UtcNow });
        }
        catch (Exception ex)
        {
            return Results.Problem($"Failed to get DO states: {ex.Message}");
        }
    }

    /// <summary>
    /// Sets DO points to 1
    /// </summary>
    private static async Task<IResult> SetDO(
        [FromRoute] string endpointId,
        [FromBody] SetDORequest request,
        [FromServices] TwentyFourDioService service)
    {
        if (!ModelState.IsValid(request, out var validationResults))
        {
            return Results.ValidationProblem(validationResults);
        }

        var poller = service.GetPoller(endpointId);
        if (poller == null)
        {
            return Results.NotFound($"Endpoint '{endpointId}' not found");
        }

        try
        {
            var success = await poller.SetDOAsync(request.DoStates);
            var response = new CommandResponse
            {
                Success = success,
                Message = success ? "DO states set successfully" : "Failed to set DO states"
            };

            return success ? Results.Ok(response) : Results.Problem(response.Message);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Failed to set DO states: {ex.Message}");
        }
    }

    /// <summary>
    /// Clears DO points to 0
    /// </summary>
    private static async Task<IResult> ClearDO(
        [FromRoute] string endpointId,
        [FromBody] ClearDORequest request,
        [FromServices] TwentyFourDioService service)
    {
        if (!ModelState.IsValid(request, out var validationResults))
        {
            return Results.ValidationProblem(validationResults);
        }

        var poller = service.GetPoller(endpointId);
        if (poller == null)
        {
            return Results.NotFound($"Endpoint '{endpointId}' not found");
        }

        try
        {
            var success = await poller.ClearDOAsync(request.DoStates);
            var response = new CommandResponse
            {
                Success = success,
                Message = success ? "DO states cleared successfully" : "Failed to clear DO states"
            };

            return success ? Results.Ok(response) : Results.Problem(response.Message);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Failed to clear DO states: {ex.Message}");
        }
    }

    /// <summary>
    /// Pins all DO points
    /// </summary>
    private static async Task<IResult> PinDO(
        [FromRoute] string endpointId,
        [FromBody] PinDORequest request,
        [FromServices] TwentyFourDioService service)
    {
        if (!ModelState.IsValid(request, out var validationResults))
        {
            return Results.ValidationProblem(validationResults);
        }

        var poller = service.GetPoller(endpointId);
        if (poller == null)
        {
            return Results.NotFound($"Endpoint '{endpointId}' not found");
        }

        try
        {
            var success = await poller.PinDOAsync(request.DoStates);
            var response = new CommandResponse
            {
                Success = success,
                Message = success ? "DO states pinned successfully" : "Failed to pin DO states"
            };

            return success ? Results.Ok(response) : Results.Problem(response.Message);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Failed to pin DO states: {ex.Message}");
        }
    }

    /// <summary>
    /// Forces a reconnection
    /// </summary>
    private static async Task<IResult> ForceReconnect(
        [FromRoute] string endpointId,
        [FromServices] TwentyFourDioService service)
    {
        var poller = service.GetPoller(endpointId);
        if (poller == null)
        {
            return Results.NotFound($"Endpoint '{endpointId}' not found");
        }

        try
        {
            await poller.ForceReconnectAsync();
            var response = new CommandResponse
            {
                Success = true,
                Message = "Reconnection initiated successfully"
            };

            return Results.Ok(response);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Failed to initiate reconnection: {ex.Message}");
        }
    }

    /// <summary>
    /// Lists all endpoints
    /// </summary>
    private static IResult ListEndpoints([FromServices] TwentyFourDioService service)
    {
        var endpoints = service.Pollers.Select(kvp => new
        {
            EndpointId = kvp.Key,
            IpAddress = kvp.Value.Configuration.IpAddress,
            Port = kvp.Value.Configuration.Port,
            ConnectionState = kvp.Value.ConnectionState.ToString(),
            IsRunning = kvp.Value.IsRunning,
            CurrentState = kvp.Value.CurrentState != null ? new
            {
                DiStates = kvp.Value.CurrentState.GetDIBinaryString(),
                DoStates = kvp.Value.CurrentState.GetDOBinaryString(),
                Timestamp = kvp.Value.CurrentState.Timestamp
            } : null
        }).ToList();

        return Results.Ok(endpoints);
    }
}

/// <summary>
/// Model state validation helper
/// </summary>
public static class ModelState
{
    /// <summary>
    /// Validates a model and returns validation results
    /// </summary>
    public static bool IsValid<T>(T model, out Dictionary<string, string[]> validationResults)
    {
        validationResults = new Dictionary<string, string[]>();
        var context = new ValidationContext(model!);
        var results = new List<ValidationResult>();

        var isValid = Validator.TryValidateObject(model!, context, results, true);

        if (!isValid)
        {
            validationResults = results
                .GroupBy(r => r.MemberNames.FirstOrDefault() ?? "")
                .ToDictionary(
                    g => g.Key,
                    g => g.Select(r => r.ErrorMessage ?? "").ToArray()
                );
        }

        return isValid;
    }
}
