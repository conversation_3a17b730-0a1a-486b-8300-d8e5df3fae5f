using System.Net;
using Microsoft.AspNetCore.Mvc;
using Ngp.Communication.IpMonitor.Enums;
using Ngp.Services;

namespace Ngp.Endpoints;

/// <summary>
/// Minimal API endpoints for IP monitoring
/// </summary>
public static class IpMonitorEndpoints
{
    /// <summary>
    /// Maps IP monitor endpoints
    /// </summary>
    /// <param name="app">Web application</param>
    public static void MapIpMonitorEndpoints(this WebApplication app)
    {
        var group = app.MapGroup("/api/ipmonitor")
            .WithTags("IP Monitor")
            .WithOpenApi();

        // Get all monitors
        group.MapGet("/monitors", GetAllMonitors)
            .WithName("GetAllMonitors")
            .WithSummary("Get all IP monitors")
            .WithDescription("Returns information about all active IP monitors");

        // Get specific monitor
        group.MapGet("/monitors/{monitorId}", GetMonitor)
            .WithName("GetMonitor")
            .WithSummary("Get specific IP monitor")
            .WithDescription("Returns information about a specific IP monitor");

        // Create new monitor
        group.MapPost("/monitors", CreateMonitor)
            .WithName("CreateMonitor")
            .WithSummary("Create new IP monitor")
            .WithDescription("Creates a new IP monitor with specified configuration");

        // Create monitor with specific mode
        group.MapPost("/monitors/immediate", CreateImmediateModeMonitor)
            .WithName("CreateImmediateModeMonitor")
            .WithSummary("Create immediate mode IP monitor")
            .WithDescription("Creates an IP monitor using immediate mode (instant status changes)");

        group.MapPost("/monitors/normal", CreateNormalModeMonitor)
            .WithName("CreateNormalModeMonitor")
            .WithSummary("Create normal mode IP monitor")
            .WithDescription("Creates an IP monitor using normal mode (with retry mechanism)");

        group.MapPost("/monitors/simple", CreateSimpleModeMonitor)
            .WithName("CreateSimpleModeMonitor")
            .WithSummary("Create simple mode IP monitor")
            .WithDescription("Creates an IP monitor using simple mode (time window based)");

        // Delete monitor
        group.MapDelete("/monitors/{monitorId}", DeleteMonitor)
            .WithName("DeleteMonitor")
            .WithSummary("Delete IP monitor")
            .WithDescription("Stops and removes an IP monitor");

        // Get IP status across all monitors
        group.MapGet("/status/{ipAddress}", GetIpStatus)
            .WithName("GetIpStatus")
            .WithSummary("Get IP status")
            .WithDescription("Gets the status of a specific IP address across all monitors");

        // Ping specific IP
        group.MapPost("/ping", PingIp)
            .WithName("PingIp")
            .WithSummary("Ping IP address")
            .WithDescription("Performs an immediate ping test to a specific IP address");

        // Add IP to monitor
        group.MapPost("/monitors/{monitorId}/ips", AddIpToMonitor)
            .WithName("AddIpToMonitor")
            .WithSummary("Add IP to monitor")
            .WithDescription("Adds an IP address or range to an existing monitor");

        // Remove IP from monitor
        group.MapDelete("/monitors/{monitorId}/ips/{ipAddress}", RemoveIpFromMonitor)
            .WithName("RemoveIpFromMonitor")
            .WithSummary("Remove IP from monitor")
            .WithDescription("Removes an IP address from an existing monitor");

        // Get monitor statistics
        group.MapGet("/monitors/{monitorId}/stats", GetMonitorStats)
            .WithName("GetMonitorStats")
            .WithSummary("Get monitor statistics")
            .WithDescription("Returns statistics for a specific monitor");
    }

    /// <summary>
    /// Gets all monitors
    /// </summary>
    private static IResult GetAllMonitors(IpMonitorService service)
    {
        try
        {
            var monitors = service.GetAllMonitors();
            var result = monitors.Select(kvp => new
            {
                MonitorId = kvp.Key,
                Configuration = new
                {
                    kvp.Value.Configuration.Id,
                    kvp.Value.Configuration.MonitoringMode,
                    kvp.Value.Configuration.DetectionMethod,
                    kvp.Value.Configuration.MaxConcurrency,
                    kvp.Value.Configuration.TimeoutMs,
                    kvp.Value.Configuration.PollingIntervalMs
                },
                Status = new
                {
                    kvp.Value.IsRunning,
                    kvp.Value.TotalTargetCount,
                    kvp.Value.OnlineCount,
                    kvp.Value.OfflineCount
                }
            });

            return Results.Ok(result);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error getting monitors: {ex.Message}");
        }
    }

    /// <summary>
    /// Gets a specific monitor
    /// </summary>
    private static IResult GetMonitor(string monitorId, IpMonitorService service)
    {
        try
        {
            var monitor = service.GetMonitor(monitorId);
            if (monitor == null)
            {
                return Results.NotFound($"Monitor '{monitorId}' not found");
            }

            var result = new
            {
                MonitorId = monitorId,
                Configuration = new
                {
                    monitor.Configuration.Id,
                    monitor.Configuration.MonitoringMode,
                    monitor.Configuration.DetectionMethod,
                    monitor.Configuration.MaxConcurrency,
                    monitor.Configuration.TimeoutMs,
                    monitor.Configuration.PollingIntervalMs,
                    monitor.Configuration.RetryCount,
                    monitor.Configuration.RetryDelayMs,
                    monitor.Configuration.ConsecutiveSuccessCount,
                    monitor.Configuration.MaintenanceTimeMs
                },
                Status = new
                {
                    monitor.IsRunning,
                    monitor.TotalTargetCount,
                    monitor.OnlineCount,
                    monitor.OfflineCount
                },
                Targets = monitor.MonitoredTargets.Select(t => new
                {
                    IpAddress = t.IpAddress.ToString(),
                    t.CurrentStatus,
                    t.PreviousStatus,
                    t.LastSuccessTime,
                    t.LastFailureTime,
                    t.LastStatusChangeTime,
                    t.TotalPingCount,
                    t.SuccessfulPingCount,
                    t.FailedPingCount,
                    t.AverageResponseTimeMs,
                    t.LastResponseTimeMs,
                    t.Tag,
                    t.Description
                })
            };

            return Results.Ok(result);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error getting monitor: {ex.Message}");
        }
    }

    /// <summary>
    /// Creates a new monitor
    /// </summary>
    private static async Task<IResult> CreateMonitor([FromBody] CreateMonitorRequest request, IpMonitorService service)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.MonitorId))
            {
                return Results.BadRequest("Monitor ID is required");
            }

            if (request.IpAddresses == null || !request.IpAddresses.Any())
            {
                return Results.BadRequest("At least one IP address is required");
            }

            var success = await service.CreateMonitorAsync(request.MonitorId, request.IpAddresses);
            if (success)
            {
                return Results.Created($"/api/ipmonitor/monitors/{request.MonitorId}",
                    new { MonitorId = request.MonitorId, Message = "Monitor created successfully", Mode = "Production (Normal)" });
            }
            else
            {
                return Results.Conflict($"Monitor '{request.MonitorId}' already exists or creation failed");
            }
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error creating monitor: {ex.Message}");
        }
    }

    /// <summary>
    /// Creates an immediate mode monitor
    /// </summary>
    private static async Task<IResult> CreateImmediateModeMonitor([FromBody] CreateMonitorRequest request, IpMonitorService service)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.MonitorId))
            {
                return Results.BadRequest("Monitor ID is required");
            }

            if (request.IpAddresses == null || !request.IpAddresses.Any())
            {
                return Results.BadRequest("At least one IP address is required");
            }

            var success = await service.CreateImmediateModeMonitorAsync(request.MonitorId, request.IpAddresses);
            if (success)
            {
                return Results.Created($"/api/ipmonitor/monitors/{request.MonitorId}",
                    new { MonitorId = request.MonitorId, Message = "Immediate mode monitor created successfully", Mode = "Immediate" });
            }
            else
            {
                return Results.Conflict($"Monitor '{request.MonitorId}' already exists or creation failed");
            }
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error creating immediate mode monitor: {ex.Message}");
        }
    }

    /// <summary>
    /// Creates a normal mode monitor
    /// </summary>
    private static async Task<IResult> CreateNormalModeMonitor([FromBody] CreateNormalModeMonitorRequest request, IpMonitorService service)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.MonitorId))
            {
                return Results.BadRequest("Monitor ID is required");
            }

            if (request.IpAddresses == null || !request.IpAddresses.Any())
            {
                return Results.BadRequest("At least one IP address is required");
            }

            var success = await service.CreateNormalModeMonitorAsync(request.MonitorId, request.IpAddresses,
                request.RetryCount, request.RetryDelayMs, request.ConsecutiveSuccessCount);
            if (success)
            {
                return Results.Created($"/api/ipmonitor/monitors/{request.MonitorId}",
                    new {
                        MonitorId = request.MonitorId,
                        Message = "Normal mode monitor created successfully",
                        Mode = "Normal",
                        RetryCount = request.RetryCount,
                        RetryDelayMs = request.RetryDelayMs,
                        ConsecutiveSuccessCount = request.ConsecutiveSuccessCount
                    });
            }
            else
            {
                return Results.Conflict($"Monitor '{request.MonitorId}' already exists or creation failed");
            }
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error creating normal mode monitor: {ex.Message}");
        }
    }

    /// <summary>
    /// Creates a simple mode monitor
    /// </summary>
    private static async Task<IResult> CreateSimpleModeMonitor([FromBody] CreateSimpleModeMonitorRequest request, IpMonitorService service)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.MonitorId))
            {
                return Results.BadRequest("Monitor ID is required");
            }

            if (request.IpAddresses == null || !request.IpAddresses.Any())
            {
                return Results.BadRequest("At least one IP address is required");
            }

            var success = await service.CreateSimpleModeMonitorAsync(request.MonitorId, request.IpAddresses, request.MaintenanceTimeMs);
            if (success)
            {
                return Results.Created($"/api/ipmonitor/monitors/{request.MonitorId}",
                    new {
                        MonitorId = request.MonitorId,
                        Message = "Simple mode monitor created successfully",
                        Mode = "Simple",
                        MaintenanceTimeMs = request.MaintenanceTimeMs
                    });
            }
            else
            {
                return Results.Conflict($"Monitor '{request.MonitorId}' already exists or creation failed");
            }
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error creating simple mode monitor: {ex.Message}");
        }
    }

    /// <summary>
    /// Deletes a monitor
    /// </summary>
    private static async Task<IResult> DeleteMonitor(string monitorId, IpMonitorService service)
    {
        try
        {
            var success = await service.RemoveMonitorAsync(monitorId);
            if (success)
            {
                return Results.Ok(new { Message = $"Monitor '{monitorId}' deleted successfully" });
            }
            else
            {
                return Results.NotFound($"Monitor '{monitorId}' not found");
            }
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error deleting monitor: {ex.Message}");
        }
    }

    /// <summary>
    /// Gets IP status across all monitors
    /// </summary>
    private static IResult GetIpStatus(string ipAddress, IpMonitorService service)
    {
        try
        {
            if (!IPAddress.TryParse(ipAddress, out _))
            {
                return Results.BadRequest($"Invalid IP address: {ipAddress}");
            }

            var statuses = service.GetIpStatus(ipAddress);
            var result = statuses.Select(s => new
            {
                s.MonitorId,
                IpAddress = s.Target.IpAddress.ToString(),
                s.Target.CurrentStatus,
                s.Target.PreviousStatus,
                s.Target.LastSuccessTime,
                s.Target.LastFailureTime,
                s.Target.LastStatusChangeTime,
                s.Target.TotalPingCount,
                s.Target.SuccessfulPingCount,
                s.Target.FailedPingCount,
                s.Target.AverageResponseTimeMs,
                s.Target.LastResponseTimeMs,
                s.Target.Tag,
                s.Target.Description
            });

            return Results.Ok(result);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error getting IP status: {ex.Message}");
        }
    }

    /// <summary>
    /// Pings a specific IP
    /// </summary>
    private static async Task<IResult> PingIp([FromBody] PingRequest request, IpMonitorService service)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.IpAddress))
            {
                return Results.BadRequest("IP address is required");
            }

            if (!IPAddress.TryParse(request.IpAddress, out _))
            {
                return Results.BadRequest($"Invalid IP address: {request.IpAddress}");
            }

            var result = await service.PingAsync(request.IpAddress);
            return Results.Ok(new
            {
                IpAddress = result.IpAddress.ToString(),
                result.IsSuccess,
                result.ResponseTimeMs,
                result.Status,
                result.Timestamp,
                result.ErrorMessage
            });
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error pinging IP: {ex.Message}");
        }
    }

    /// <summary>
    /// Adds IP to monitor (placeholder - would need to extend IpMonitor interface)
    /// </summary>
    private static IResult AddIpToMonitor(string monitorId, [FromBody] AddIpRequest request, IpMonitorService service)
    {
        // This would require extending the IIpMonitor interface to support runtime IP addition
        return Results.Problem("Runtime IP addition not yet implemented", statusCode: 501);
    }

    /// <summary>
    /// Removes IP from monitor (placeholder - would need to extend IpMonitor interface)
    /// </summary>
    private static IResult RemoveIpFromMonitor(string monitorId, string ipAddress, IpMonitorService service)
    {
        // This would require extending the IIpMonitor interface to support runtime IP removal
        return Results.Problem("Runtime IP removal not yet implemented", statusCode: 501);
    }

    /// <summary>
    /// Gets monitor statistics
    /// </summary>
    private static IResult GetMonitorStats(string monitorId, IpMonitorService service)
    {
        try
        {
            var monitor = service.GetMonitor(monitorId);
            if (monitor == null)
            {
                return Results.NotFound($"Monitor '{monitorId}' not found");
            }

            var targets = monitor.MonitoredTargets;
            var stats = new
            {
                MonitorId = monitorId,
                TotalTargets = targets.Count,
                OnlineTargets = targets.Count(t => t.CurrentStatus == IpStatus.Online),
                OfflineTargets = targets.Count(t => t.CurrentStatus == IpStatus.Offline),
                UnknownTargets = targets.Count(t => t.CurrentStatus == IpStatus.Unknown),
                TotalPings = targets.Sum(t => t.TotalPingCount),
                SuccessfulPings = targets.Sum(t => t.SuccessfulPingCount),
                FailedPings = targets.Sum(t => t.FailedPingCount),
                AverageResponseTime = targets.Where(t => t.AverageResponseTimeMs > 0).Average(t => t.AverageResponseTimeMs),
                SuccessRate = targets.Sum(t => t.TotalPingCount) > 0 
                    ? (double)targets.Sum(t => t.SuccessfulPingCount) / targets.Sum(t => t.TotalPingCount) * 100 
                    : 0
            };

            return Results.Ok(stats);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error getting monitor statistics: {ex.Message}");
        }
    }
}

/// <summary>
/// Request model for creating a monitor
/// </summary>
public record CreateMonitorRequest(string MonitorId, IEnumerable<string> IpAddresses);

/// <summary>
/// Request model for creating a normal mode monitor
/// </summary>
public record CreateNormalModeMonitorRequest(
    string MonitorId,
    IEnumerable<string> IpAddresses,
    int RetryCount = 3,
    int RetryDelayMs = 1000,
    int ConsecutiveSuccessCount = 2);

/// <summary>
/// Request model for creating a simple mode monitor
/// </summary>
public record CreateSimpleModeMonitorRequest(
    string MonitorId,
    IEnumerable<string> IpAddresses,
    int MaintenanceTimeMs = 300000); // 5 minutes default

/// <summary>
/// Request model for pinging an IP
/// </summary>
public record PingRequest(string IpAddress);

/// <summary>
/// Request model for adding IP to monitor
/// </summary>
public record AddIpRequest(string IpAddress, string? Tag = null, string? Description = null);
