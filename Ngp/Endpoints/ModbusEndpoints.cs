using Microsoft.AspNetCore.Mvc;
using Ngp.Communication.ModbusTcpMaster.Enums;
using Ngp.Communication.ModbusTcpMaster.Utilities;
using Ngp.Models;
using Ngp.Services;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics;

namespace Ngp.Endpoints;

/// <summary>
/// Modbus TCP API endpoints
/// </summary>
public static class ModbusEndpoints
{
    /// <summary>
    /// Maps Modbus TCP endpoints
    /// </summary>
    /// <param name="app">Web application</param>
    /// <returns>Web application</returns>
    public static WebApplication MapModbusEndpoints(this WebApplication app)
    {
        var group = app.MapGroup("/api/modbus")
            .WithTags("Modbus TCP")
            .WithOpenApi();

        // System status endpoints
        group.MapGet("/status", GetSystemStatus)
            .WithName("GetSystemStatus")
            .WithSummary("Get system status")
            .WithDescription("Returns the current status of the Modbus TCP service and all connections")
            .Produces<ApiResponse<SystemStatusInfo>>(StatusCodes.Status200OK);

        group.MapGet("/connections", GetConnectionStatus)
            .WithName("GetConnectionStatus")
            .WithSummary("Get connection status")
            .WithDescription("Returns the connection status for all configured endpoints")
            .Produces<ApiResponse<List<ConnectionStatusInfo>>>(StatusCodes.Status200OK);

        // Write operations
        group.MapPost("/write/coil", WriteSingleCoil)
            .WithName("WriteSingleCoil")
            .WithSummary("Write single coil")
            .WithDescription("Writes a value to a single coil (discrete output)")
            .Accepts<WriteSingleCoilRequest>("application/json")
            .Produces<ApiResponse<bool>>(StatusCodes.Status200OK)
            .Produces<ApiResponse<bool>>(StatusCodes.Status400BadRequest)
            .Produces<ApiResponse<bool>>(StatusCodes.Status500InternalServerError);

        group.MapPost("/write/register", WriteSingleRegister)
            .WithName("WriteSingleRegister")
            .WithSummary("Write single register")
            .WithDescription("Writes a value to a single holding register")
            .Accepts<WriteSingleRegisterRequest>("application/json")
            .Produces<ApiResponse<bool>>(StatusCodes.Status200OK)
            .Produces<ApiResponse<bool>>(StatusCodes.Status400BadRequest)
            .Produces<ApiResponse<bool>>(StatusCodes.Status500InternalServerError);

        // Read operations
        group.MapPost("/read/register", ReadRegisterValue)
            .WithName("ReadRegisterValue")
            .WithSummary("Read register value")
            .WithDescription("Reads the current value of a register")
            .Accepts<ReadRegisterRequest>("application/json")
            .Produces<ApiResponse<RegisterValueInfo>>(StatusCodes.Status200OK)
            .Produces<ApiResponse<RegisterValueInfo>>(StatusCodes.Status400BadRequest)
            .Produces<ApiResponse<RegisterValueInfo>>(StatusCodes.Status404NotFound);

        // Endpoint-specific operations
        group.MapPost("/{endpointId}/write/coil/{address}", WriteCoilWithEndpoint)
            .WithName("WriteCoilWithEndpoint")
            .WithSummary("Write coil to specific endpoint")
            .WithDescription("Writes a value to a coil on the specified endpoint")
            .Produces<ApiResponse<bool>>(StatusCodes.Status200OK)
            .Produces<ApiResponse<bool>>(StatusCodes.Status400BadRequest);

        group.MapPost("/{endpointId}/write/register/{address}", WriteRegisterWithEndpoint)
            .WithName("WriteRegisterWithEndpoint")
            .WithSummary("Write register to specific endpoint")
            .WithDescription("Writes a value to a holding register on the specified endpoint. Supports addresses 10000-65535 (mapped to 410000-465535) and standard 40001-49999 range.")
            .Produces<ApiResponse<bool>>(StatusCodes.Status200OK)
            .Produces<ApiResponse<bool>>(StatusCodes.Status400BadRequest);

        group.MapGet("/{endpointId}/read/register/{address}", ReadRegisterWithEndpoint)
            .WithName("ReadRegisterWithEndpoint")
            .WithSummary("Read register from specific endpoint")
            .WithDescription("Reads the current value of a holding register from the specified endpoint. Supports addresses 10000-65535 (mapped to 410000-465535) and standard 40001-49999 range.")
            .Produces<ApiResponse<RegisterValueInfo>>(StatusCodes.Status200OK)
            .Produces<ApiResponse<RegisterValueInfo>>(StatusCodes.Status404NotFound);

        group.MapGet("/{endpointId}/read/coil/{address}", ReadCoilWithEndpoint)
            .WithName("ReadCoilWithEndpoint")
            .WithSummary("Read coil from specific endpoint")
            .WithDescription("Reads the current value of a coil from the specified endpoint")
            .Produces<ApiResponse<RegisterValueInfo>>(StatusCodes.Status200OK)
            .Produces<ApiResponse<RegisterValueInfo>>(StatusCodes.Status404NotFound);

        // Convenience endpoints for test slave (backward compatibility)
        group.MapPost("/test/write/coil/{address}", WriteTestCoil)
            .WithName("WriteTestCoil")
            .WithSummary("Write test coil")
            .WithDescription("Writes a value to a coil on the test slave (**************:502)")
            .Produces<ApiResponse<bool>>(StatusCodes.Status200OK)
            .Produces<ApiResponse<bool>>(StatusCodes.Status400BadRequest);

        group.MapPost("/test/write/register/{address}", WriteTestRegister)
            .WithName("WriteTestRegister")
            .WithSummary("Write test register")
            .WithDescription("Writes a value to a holding register on the test slave (**************:502). Supports addresses 10000-65535 (mapped to 410000-465535) and standard 40001-49999 range.")
            .Produces<ApiResponse<bool>>(StatusCodes.Status200OK)
            .Produces<ApiResponse<bool>>(StatusCodes.Status400BadRequest);

        group.MapGet("/test/read/register/{address}", ReadTestRegister)
            .WithName("ReadTestRegister")
            .WithSummary("Read test register")
            .WithDescription("Reads the current value of a holding register on the test slave. Supports addresses 10000-65535 (mapped to 410000-465535) and standard 40001-49999 range.")
            .Produces<ApiResponse<RegisterValueInfo>>(StatusCodes.Status200OK)
            .Produces<ApiResponse<RegisterValueInfo>>(StatusCodes.Status404NotFound);

        group.MapGet("/test/read/coil/{address}", ReadTestCoil)
            .WithName("ReadTestCoil")
            .WithSummary("Read test coil")
            .WithDescription("Reads the current value of a coil on the test slave")
            .Produces<ApiResponse<RegisterValueInfo>>(StatusCodes.Status200OK)
            .Produces<ApiResponse<RegisterValueInfo>>(StatusCodes.Status404NotFound);

        return app;
    }

    /// <summary>
    /// Gets system status
    /// </summary>
    private static Task<IResult> GetSystemStatus(
        ModbusTcpService modbusService,
        ILogger<ModbusTcpService> logger)
    {
        try
        {
            var connectionStatus = modbusService.GetConnectionStatus();
            var connections = connectionStatus.Select(kvp => new ConnectionStatusInfo
            {
                EndpointId = kvp.Key,
                State = kvp.Value,
                StateDescription = kvp.Value.ToString(),
                IsConnected = kvp.Value == ConnectionState.Connected
            }).ToList();

            var status = new SystemStatusInfo
            {
                ServiceStatus = "Running",
                ActiveMasters = connectionStatus.Count,
                Connections = connections,
                Uptime = DateTime.UtcNow - Process.GetCurrentProcess().StartTime
            };

            return Task.FromResult(Results.Ok(ApiResponse<SystemStatusInfo>.CreateSuccess(status)));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting system status");
            return Task.FromResult(Results.Ok(ApiResponse<SystemStatusInfo>.CreateError($"Error getting system status: {ex.Message}")));
        }
    }

    /// <summary>
    /// Gets connection status
    /// </summary>
    private static Task<IResult> GetConnectionStatus(
        ModbusTcpService modbusService,
        ILogger<ModbusTcpService> logger)
    {
        try
        {
            var connectionStatus = modbusService.GetConnectionStatus();
            var connections = connectionStatus.Select(kvp => new ConnectionStatusInfo
            {
                EndpointId = kvp.Key,
                State = kvp.Value,
                StateDescription = kvp.Value.ToString(),
                IsConnected = kvp.Value == ConnectionState.Connected
            }).ToList();

            return Task.FromResult(Results.Ok(ApiResponse<List<ConnectionStatusInfo>>.CreateSuccess(connections)));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting connection status");
            return Task.FromResult(Results.Ok(ApiResponse<List<ConnectionStatusInfo>>.CreateError($"Error getting connection status: {ex.Message}")));
        }
    }

    /// <summary>
    /// Writes a single coil
    /// </summary>
    private static async Task<IResult> WriteSingleCoil(
        [FromBody] WriteSingleCoilRequest request,
        ModbusTcpService modbusService,
        ILogger<ModbusTcpService> logger,
        CancellationToken cancellationToken)
    {
        try
        {
            // Validate request
            var validationResults = new List<ValidationResult>();
            var validationContext = new ValidationContext(request);
            if (!Validator.TryValidateObject(request, validationContext, validationResults, true))
            {
                var errors = string.Join(", ", validationResults.Select(r => r.ErrorMessage));
                return Results.BadRequest(ApiResponse<bool>.CreateError($"Validation failed: {errors}"));
            }

            var result = await modbusService.WriteSingleCoilAsync(
                request.EndpointId, request.SlaveId, request.Address, request.Value, cancellationToken);

            return Results.Ok(ApiResponse<bool>.CreateSuccess(result));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error writing single coil");
            return Results.Problem(
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError,
                title: "Error writing single coil");
        }
    }

    /// <summary>
    /// Writes a single register
    /// </summary>
    private static async Task<IResult> WriteSingleRegister(
        [FromBody] WriteSingleRegisterRequest request,
        ModbusTcpService modbusService,
        ILogger<ModbusTcpService> logger,
        CancellationToken cancellationToken)
    {
        try
        {
            // Validate request
            var validationResults = new List<ValidationResult>();
            var validationContext = new ValidationContext(request);
            if (!Validator.TryValidateObject(request, validationContext, validationResults, true))
            {
                var errors = string.Join(", ", validationResults.Select(r => r.ErrorMessage));
                return Results.BadRequest(ApiResponse<bool>.CreateError($"Validation failed: {errors}"));
            }

            var result = await modbusService.WriteSingleRegisterAsync(
                request.EndpointId, request.SlaveId, request.Address, request.Value, cancellationToken);

            return Results.Ok(ApiResponse<bool>.CreateSuccess(result));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error writing single register");
            return Results.Problem(
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError,
                title: "Error writing single register");
        }
    }

    /// <summary>
    /// Reads a register value
    /// </summary>
    private static Task<IResult> ReadRegisterValue(
        [FromBody] ReadRegisterRequest request,
        ModbusTcpService modbusService,
        ILogger<ModbusTcpService> logger)
    {
        try
        {
            // Validate request
            var validationResults = new List<ValidationResult>();
            var validationContext = new ValidationContext(request);
            if (!Validator.TryValidateObject(request, validationContext, validationResults, true))
            {
                var errors = string.Join(", ", validationResults.Select(r => r.ErrorMessage));
                return Task.FromResult(Results.BadRequest(ApiResponse<RegisterValueInfo>.CreateError($"Validation failed: {errors}")));
            }

            var rawValue = modbusService.GetCurrentValue(request.EndpointId, request.SlaveId, request.Address, request.Function);
            
            var valueInfo = new RegisterValueInfo
            {
                EndpointId = request.EndpointId,
                SlaveId = request.SlaveId,
                Address = request.Address,
                Function = request.Function,
                RawBytes = rawValue,
                RawValue = rawValue != null ? Convert.ToHexString(rawValue) : null,
                IsAvailable = rawValue != null,
                LastRead = rawValue != null ? DateTime.UtcNow : null
            };

            if (rawValue == null)
            {
                return Task.FromResult(Results.NotFound(ApiResponse<RegisterValueInfo>.CreateError("Register value not available")));
            }

            return Task.FromResult(Results.Ok(ApiResponse<RegisterValueInfo>.CreateSuccess(valueInfo)));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error reading register value");
            return Task.FromResult(Results.Problem(
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError,
                title: "Error reading register value"));
        }
    }

    /// <summary>
    /// Writes a test coil
    /// </summary>
    private static async Task<IResult> WriteTestCoil(
        ushort address,
        [FromQuery] bool value,
        ModbusTcpService modbusService,
        ILogger<ModbusTcpService> logger,
        CancellationToken cancellationToken)
    {
        try
        {
            const string testEndpointId = "TestSlave";
            const byte testSlaveId = 1;

            var result = await modbusService.WriteSingleCoilAsync(testEndpointId, testSlaveId, address, value, cancellationToken);
            return Results.Ok(ApiResponse<bool>.CreateSuccess(result));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error writing test coil");
            return Results.BadRequest(ApiResponse<bool>.CreateError($"Error writing test coil: {ex.Message}"));
        }
    }

    /// <summary>
    /// Writes a test register
    /// </summary>
    private static async Task<IResult> WriteTestRegister(
        uint address,
        [FromQuery] string value,
        ModbusTcpService modbusService,
        ILogger<ModbusTcpService> logger,
        CancellationToken cancellationToken)
    {
        try
        {
            const string testEndpointId = "TestSlave";
            const byte testSlaveId = 1;

            // Convert API address to internal address using AddressConverter
            var internalAddress = ConvertApiAddressToInternal(address);

            logger.LogDebug("API address {ApiAddress} converted to internal address {InternalAddress}", address, internalAddress);

            // Use typed register write which will automatically convert to the correct type
            var result = await modbusService.WriteTypedRegisterAsync(testEndpointId, testSlaveId, internalAddress, value, cancellationToken);
            return Results.Ok(ApiResponse<bool>.CreateSuccess(result));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error writing test register");
            return Results.BadRequest(ApiResponse<bool>.CreateError($"Error writing test register: {ex.Message}"));
        }
    }

    /// <summary>
    /// Reads a test register
    /// </summary>
    private static Task<IResult> ReadTestRegister(
        uint address,
        ModbusTcpService modbusService,
        ILogger<ModbusTcpService> logger)
    {
        try
        {
            const string testEndpointId = "TestSlave";
            const byte testSlaveId = 1;

            // Convert API address to internal address using AddressConverter
            var internalAddress = ConvertApiAddressToInternal(address);

            logger.LogDebug("API address {ApiAddress} converted to internal address {InternalAddress}", address, internalAddress);

            var rawValue = modbusService.GetCurrentValue(testEndpointId, testSlaveId, internalAddress, ModbusFunction.ReadHoldingRegisters);

            var valueInfo = new RegisterValueInfo
            {
                EndpointId = testEndpointId,
                SlaveId = testSlaveId,
                Address = internalAddress, // Use internal address for consistency
                Function = ModbusFunction.ReadHoldingRegisters,
                RawBytes = rawValue,
                RawValue = rawValue != null ? Convert.ToHexString(rawValue) : null,
                IsAvailable = rawValue != null,
                LastRead = rawValue != null ? DateTime.UtcNow : null
            };

            if (rawValue == null)
            {
                return Task.FromResult(Results.NotFound(ApiResponse<RegisterValueInfo>.CreateError("Register value not available")));
            }

            return Task.FromResult(Results.Ok(ApiResponse<RegisterValueInfo>.CreateSuccess(valueInfo)));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error reading test register");
            return Task.FromResult(Results.Problem(
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError,
                title: "Error reading test register"));
        }
    }

    /// <summary>
    /// Reads a test coil
    /// </summary>
    private static Task<IResult> ReadTestCoil(
        ushort address,
        ModbusTcpService modbusService,
        ILogger<ModbusTcpService> logger)
    {
        try
        {
            const string testEndpointId = "TestSlave";
            const byte testSlaveId = 1;

            var rawValue = modbusService.GetCurrentValue(testEndpointId, testSlaveId, address, ModbusFunction.ReadCoils);
            
            var valueInfo = new RegisterValueInfo
            {
                EndpointId = testEndpointId,
                SlaveId = testSlaveId,
                Address = address,
                Function = ModbusFunction.ReadCoils,
                RawBytes = rawValue,
                RawValue = rawValue != null ? Convert.ToHexString(rawValue) : null,
                IsAvailable = rawValue != null,
                LastRead = rawValue != null ? DateTime.UtcNow : null
            };

            if (rawValue == null)
            {
                return Task.FromResult(Results.NotFound(ApiResponse<RegisterValueInfo>.CreateError("Coil value not available")));
            }

            return Task.FromResult(Results.Ok(ApiResponse<RegisterValueInfo>.CreateSuccess(valueInfo)));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error reading test coil");
            return Task.FromResult(Results.Problem(
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError,
                title: "Error reading test coil"));
        }
    }

    /// <summary>
    /// Writes a coil to specific endpoint
    /// </summary>
    private static async Task<IResult> WriteCoilWithEndpoint(
        string endpointId,
        ushort address,
        ModbusTcpService modbusService,
        ILogger<ModbusTcpService> logger,
        CancellationToken cancellationToken,
        [FromQuery] bool value,
        [FromQuery] byte slaveId = 1)
    {
        try
        {
            var result = await modbusService.WriteSingleCoilAsync(endpointId, slaveId, address, value, cancellationToken);
            return Results.Ok(ApiResponse<bool>.CreateSuccess(result));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error writing coil to endpoint {EndpointId}", endpointId);
            return Results.BadRequest(ApiResponse<bool>.CreateError($"Error writing coil: {ex.Message}"));
        }
    }

    /// <summary>
    /// Writes a register to specific endpoint
    /// </summary>
    private static async Task<IResult> WriteRegisterWithEndpoint(
        string endpointId,
        uint address,
        ModbusTcpService modbusService,
        ILogger<ModbusTcpService> logger,
        CancellationToken cancellationToken,
        [FromQuery] string value,
        [FromQuery] byte slaveId = 1)
    {
        try
        {
            // Convert API address to internal address using AddressConverter
            var internalAddress = ConvertApiAddressToInternal(address);

            logger.LogDebug("API address {ApiAddress} converted to internal address {InternalAddress} for endpoint {EndpointId}",
                address, internalAddress, endpointId);

            // Use typed register write which will automatically convert to the correct type
            var result = await modbusService.WriteTypedRegisterAsync(endpointId, slaveId, internalAddress, value, cancellationToken);
            return Results.Ok(ApiResponse<bool>.CreateSuccess(result));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error writing register to endpoint {EndpointId}", endpointId);
            return Results.BadRequest(ApiResponse<bool>.CreateError($"Error writing register: {ex.Message}"));
        }
    }

    /// <summary>
    /// Reads a register from specific endpoint
    /// </summary>
    private static Task<IResult> ReadRegisterWithEndpoint(
        string endpointId,
        uint address,
        ModbusTcpService modbusService,
        ILogger<ModbusTcpService> logger,
        [FromQuery] byte slaveId = 1)
    {
        try
        {
            // Convert API address to internal address using AddressConverter
            var internalAddress = ConvertApiAddressToInternal(address);

            logger.LogDebug("API address {ApiAddress} converted to internal address {InternalAddress} for endpoint {EndpointId}",
                address, internalAddress, endpointId);

            var rawValue = modbusService.GetCurrentValue(endpointId, slaveId, internalAddress, ModbusFunction.ReadHoldingRegisters);

            var valueInfo = new RegisterValueInfo
            {
                EndpointId = endpointId,
                SlaveId = slaveId,
                Address = internalAddress, // Use internal address for consistency
                Function = ModbusFunction.ReadHoldingRegisters,
                RawBytes = rawValue,
                RawValue = rawValue != null ? Convert.ToHexString(rawValue) : null,
                IsAvailable = rawValue != null,
                LastRead = rawValue != null ? DateTime.UtcNow : null
            };

            if (rawValue == null)
            {
                return Task.FromResult(Results.NotFound(ApiResponse<RegisterValueInfo>.CreateError("Register value not available")));
            }

            return Task.FromResult(Results.Ok(ApiResponse<RegisterValueInfo>.CreateSuccess(valueInfo)));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error reading register from endpoint {EndpointId}", endpointId);
            return Task.FromResult(Results.Problem(
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError,
                title: "Error reading register"));
        }
    }

    /// <summary>
    /// Reads a coil from specific endpoint
    /// </summary>
    private static Task<IResult> ReadCoilWithEndpoint(
        string endpointId,
        ushort address,
        ModbusTcpService modbusService,
        ILogger<ModbusTcpService> logger,
        [FromQuery] byte slaveId = 1)
    {
        try
        {
            var rawValue = modbusService.GetCurrentValue(endpointId, slaveId, address, ModbusFunction.ReadCoils);

            var valueInfo = new RegisterValueInfo
            {
                EndpointId = endpointId,
                SlaveId = slaveId,
                Address = address,
                Function = ModbusFunction.ReadCoils,
                RawBytes = rawValue,
                RawValue = rawValue != null ? Convert.ToHexString(rawValue) : null,
                IsAvailable = rawValue != null,
                LastRead = rawValue != null ? DateTime.UtcNow : null
            };

            if (rawValue == null)
            {
                return Task.FromResult(Results.NotFound(ApiResponse<RegisterValueInfo>.CreateError("Coil value not available")));
            }

            return Task.FromResult(Results.Ok(ApiResponse<RegisterValueInfo>.CreateSuccess(valueInfo)));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error reading coil from endpoint {EndpointId}", endpointId);
            return Task.FromResult(Results.Problem(
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError,
                title: "Error reading coil"));
        }
    }

    /// <summary>
    /// Converts API address to internal address for holding registers
    /// </summary>
    /// <param name="apiAddress">Address from API (e.g., 10010)</param>
    /// <returns>Internal address for register lookup</returns>
    private static ushort ConvertApiAddressToInternal(uint apiAddress)
    {
        // For holding registers, convert using AddressConverter with ModbusRegister mode
        // This handles the mapping: 10010 -> 410010 -> zero-based internal address
        return AddressConverter.ToZeroBased((ushort)apiAddress, AddressMode.ModbusRegister, ModbusFunction.ReadHoldingRegisters);
    }
}
