using Microsoft.AspNetCore.Mvc;
using Ngp.Communication.AsteriskProxy.Enums;
using Ngp.Models;
using Ngp.Services;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics;

namespace Ngp.Endpoints;

/// <summary>
/// Asterisk Proxy API endpoints
/// </summary>
public static class AsteriskProxyEndpoints
{
    /// <summary>
    /// Maps Asterisk Proxy endpoints
    /// </summary>
    /// <param name="app">Web application</param>
    /// <returns>Web application</returns>
    public static WebApplication MapAsteriskProxyEndpoints(this WebApplication app)
    {
        var group = app.MapGroup("/api/asterisk")
            .WithTags("Asterisk Proxy")
            .WithOpenApi();

        // System status endpoints
        group.MapGet("/status", GetSystemStatus)
            .WithName("GetAsteriskSystemStatus")
            .WithSummary("Get Asterisk system status")
            .WithDescription("Returns the current status of the Asterisk proxy service and all connections")
            .Produces<ApiResponse<AsteriskSystemStatusInfo>>(StatusCodes.Status200OK);

        group.MapGet("/connections", GetConnectionStatus)
            .WithName("GetAsteriskConnectionStatus")
            .WithSummary("Get Asterisk connection status")
            .WithDescription("Returns the connection status for all configured Asterisk endpoints")
            .Produces<ApiResponse<List<AsteriskConnectionStatusInfo>>>(StatusCodes.Status200OK);

        // Extension status endpoints
        group.MapGet("/extensions", GetAllExtensions)
            .WithName("GetAllExtensions")
            .WithSummary("Get all extension statuses")
            .WithDescription("Returns the status of all monitored extensions across all endpoints")
            .Produces<ApiResponse<List<ExtensionStatusInfo>>>(StatusCodes.Status200OK);

        group.MapGet("/{endpointId}/extensions", GetExtensionsByEndpoint)
            .WithName("GetExtensionsByEndpoint")
            .WithSummary("Get extensions for specific endpoint")
            .WithDescription("Returns the status of all monitored extensions for the specified endpoint")
            .Produces<ApiResponse<List<ExtensionStatusInfo>>>(StatusCodes.Status200OK)
            .Produces<ApiResponse<List<ExtensionStatusInfo>>>(StatusCodes.Status404NotFound);

        group.MapGet("/{endpointId}/extensions/{extension}", GetExtensionStatus)
            .WithName("GetExtensionStatus")
            .WithSummary("Get specific extension status")
            .WithDescription("Returns the status of a specific extension")
            .Produces<ApiResponse<ExtensionStatusInfo>>(StatusCodes.Status200OK)
            .Produces<ApiResponse<ExtensionStatusInfo>>(StatusCodes.Status404NotFound);

        // Management endpoints
        group.MapPost("/{endpointId}/refresh", RefreshExtensions)
            .WithName("RefreshExtensions")
            .WithSummary("Refresh extension statuses")
            .WithDescription("Forces a refresh of all extension statuses for the specified endpoint")
            .Produces<ApiResponse<bool>>(StatusCodes.Status200OK)
            .Produces<ApiResponse<bool>>(StatusCodes.Status404NotFound);

        // Test endpoints for FreePBX test server
        group.MapGet("/test/extensions", GetTestExtensions)
            .WithName("GetTestExtensions")
            .WithSummary("Get test server extensions")
            .WithDescription("Returns the status of all extensions on the test FreePBX server (***********)")
            .Produces<ApiResponse<List<ExtensionStatusInfo>>>(StatusCodes.Status200OK);

        group.MapGet("/test/extensions/{extension}", GetTestExtensionStatus)
            .WithName("GetTestExtensionStatus")
            .WithSummary("Get test extension status")
            .WithDescription("Returns the status of a specific extension on the test FreePBX server")
            .Produces<ApiResponse<ExtensionStatusInfo>>(StatusCodes.Status200OK)
            .Produces<ApiResponse<ExtensionStatusInfo>>(StatusCodes.Status404NotFound);

        group.MapPost("/test/refresh", RefreshTestExtensions)
            .WithName("RefreshTestExtensions")
            .WithSummary("Refresh test server extensions")
            .WithDescription("Forces a refresh of all extension statuses on the test FreePBX server")
            .Produces<ApiResponse<bool>>(StatusCodes.Status200OK);

        return app;
    }

    /// <summary>
    /// Gets system status
    /// </summary>
    private static Task<IResult> GetSystemStatus(
        AsteriskProxyService asteriskService,
        ILogger<AsteriskProxyService> logger)
    {
        try
        {
            var connectionStatus = asteriskService.GetConnectionStatus();
            var connections = connectionStatus.Select(kvp => new AsteriskConnectionStatusInfo
            {
                EndpointId = kvp.Key,
                State = kvp.Value,
                StateDescription = kvp.Value.ToString(),
                IsConnected = kvp.Value == ConnectionState.Connected
            }).ToList();

            var status = new AsteriskSystemStatusInfo
            {
                ServiceStatus = "Running",
                ActiveProxies = connectionStatus.Count,
                Connections = connections,
                Uptime = DateTime.UtcNow - Process.GetCurrentProcess().StartTime
            };

            return Task.FromResult(Results.Ok(ApiResponse<AsteriskSystemStatusInfo>.CreateSuccess(status)));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting Asterisk system status");
            return Task.FromResult(Results.Ok(ApiResponse<AsteriskSystemStatusInfo>.CreateError($"Error getting system status: {ex.Message}")));
        }
    }

    /// <summary>
    /// Gets connection status
    /// </summary>
    private static Task<IResult> GetConnectionStatus(
        AsteriskProxyService asteriskService,
        ILogger<AsteriskProxyService> logger)
    {
        try
        {
            var connectionStatus = asteriskService.GetConnectionStatus();
            var connections = connectionStatus.Select(kvp => new AsteriskConnectionStatusInfo
            {
                EndpointId = kvp.Key,
                State = kvp.Value,
                StateDescription = kvp.Value.ToString(),
                IsConnected = kvp.Value == ConnectionState.Connected
            }).ToList();

            return Task.FromResult(Results.Ok(ApiResponse<List<AsteriskConnectionStatusInfo>>.CreateSuccess(connections)));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting Asterisk connection status");
            return Task.FromResult(Results.Ok(ApiResponse<List<AsteriskConnectionStatusInfo>>.CreateError($"Error getting connection status: {ex.Message}")));
        }
    }

    /// <summary>
    /// Gets all extension statuses across all endpoints
    /// </summary>
    private static Task<IResult> GetAllExtensions(
        AsteriskProxyService asteriskService,
        ILogger<AsteriskProxyService> logger)
    {
        try
        {
            var allExtensions = new List<ExtensionStatusInfo>();

            foreach (var endpointId in asteriskService.GetProxyIds())
            {
                var proxy = asteriskService.GetProxy(endpointId);
                if (proxy != null)
                {
                    var extensionStatuses = proxy.GetAllExtensionStatuses();
                    var extensionDefinitions = proxy.MonitoredExtensions;

                    foreach (var kvp in extensionStatuses)
                    {
                        var definition = extensionDefinitions.FirstOrDefault(d => d.GetKey() == kvp.Key);
                        allExtensions.Add(new ExtensionStatusInfo
                        {
                            EndpointId = endpointId,
                            Extension = kvp.Value.Extension,
                            Context = kvp.Value.Context,
                            State = kvp.Value.State,
                            StateDescription = kvp.Value.State.ToString(),
                            LastUpdated = kvp.Value.LastUpdated,
                            Tag = definition?.Tag,
                            Description = definition?.Description
                        });
                    }
                }
            }

            return Task.FromResult(Results.Ok(ApiResponse<List<ExtensionStatusInfo>>.CreateSuccess(allExtensions)));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting all extensions");
            return Task.FromResult(Results.Ok(ApiResponse<List<ExtensionStatusInfo>>.CreateError($"Error getting extensions: {ex.Message}")));
        }
    }

    /// <summary>
    /// Gets extensions for a specific endpoint
    /// </summary>
    private static Task<IResult> GetExtensionsByEndpoint(
        string endpointId,
        AsteriskProxyService asteriskService,
        ILogger<AsteriskProxyService> logger)
    {
        try
        {
            var proxy = asteriskService.GetProxy(endpointId);
            if (proxy == null)
            {
                return Task.FromResult(Results.NotFound(ApiResponse<List<ExtensionStatusInfo>>.CreateError($"Endpoint {endpointId} not found")));
            }

            var extensionStatuses = proxy.GetAllExtensionStatuses();
            var extensionDefinitions = proxy.MonitoredExtensions;

            var extensions = extensionStatuses.Select(kvp =>
            {
                var definition = extensionDefinitions.FirstOrDefault(d => d.GetKey() == kvp.Key);
                return new ExtensionStatusInfo
                {
                    EndpointId = endpointId,
                    Extension = kvp.Value.Extension,
                    Context = kvp.Value.Context,
                    State = kvp.Value.State,
                    StateDescription = kvp.Value.State.ToString(),
                    LastUpdated = kvp.Value.LastUpdated,
                    Tag = definition?.Tag,
                    Description = definition?.Description
                };
            }).ToList();

            return Task.FromResult(Results.Ok(ApiResponse<List<ExtensionStatusInfo>>.CreateSuccess(extensions)));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting extensions for endpoint {EndpointId}", endpointId);
            return Task.FromResult(Results.Ok(ApiResponse<List<ExtensionStatusInfo>>.CreateError($"Error getting extensions: {ex.Message}")));
        }
    }

    /// <summary>
    /// Gets status for a specific extension
    /// </summary>
    private static Task<IResult> GetExtensionStatus(
        string endpointId,
        string extension,
        AsteriskProxyService asteriskService,
        ILogger<AsteriskProxyService> logger,
        [FromQuery] string context = "internal")
    {
        try
        {
            var proxy = asteriskService.GetProxy(endpointId);
            if (proxy == null)
            {
                return Task.FromResult(Results.NotFound(ApiResponse<ExtensionStatusInfo>.CreateError($"Endpoint {endpointId} not found")));
            }

            var extensionStatus = proxy.GetExtensionStatus(extension, context);
            if (extensionStatus == null)
            {
                return Task.FromResult(Results.NotFound(ApiResponse<ExtensionStatusInfo>.CreateError($"Extension {extension} not found on endpoint {endpointId}")));
            }

            var definition = proxy.MonitoredExtensions.FirstOrDefault(d => d.Extension == extension && d.Context == context);
            var statusInfo = new ExtensionStatusInfo
            {
                EndpointId = endpointId,
                Extension = extensionStatus.Extension,
                Context = extensionStatus.Context,
                State = extensionStatus.State,
                StateDescription = extensionStatus.State.ToString(),
                LastUpdated = extensionStatus.LastUpdated,
                Tag = definition?.Tag,
                Description = definition?.Description
            };

            return Task.FromResult(Results.Ok(ApiResponse<ExtensionStatusInfo>.CreateSuccess(statusInfo)));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting extension status for {Extension} on endpoint {EndpointId}", extension, endpointId);
            return Task.FromResult(Results.Ok(ApiResponse<ExtensionStatusInfo>.CreateError($"Error getting extension status: {ex.Message}")));
        }
    }

    /// <summary>
    /// Refreshes extension statuses for an endpoint
    /// </summary>
    private static async Task<IResult> RefreshExtensions(
        string endpointId,
        AsteriskProxyService asteriskService,
        ILogger<AsteriskProxyService> logger,
        CancellationToken cancellationToken)
    {
        try
        {
            var result = await asteriskService.RefreshAllExtensionsAsync(endpointId, cancellationToken);
            if (!result)
            {
                return Results.NotFound(ApiResponse<bool>.CreateError($"Endpoint {endpointId} not found"));
            }

            return Results.Ok(ApiResponse<bool>.CreateSuccess(true));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error refreshing extensions for endpoint {EndpointId}", endpointId);
            return Results.Problem(
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError,
                title: "Error refreshing extensions");
        }
    }

    /// <summary>
    /// Gets test server extensions
    /// </summary>
    private static Task<IResult> GetTestExtensions(
        AsteriskProxyService asteriskService,
        ILogger<AsteriskProxyService> logger)
    {
        const string testEndpointId = "FreePBX-Test";
        return GetExtensionsByEndpoint(testEndpointId, asteriskService, logger);
    }

    /// <summary>
    /// Gets test extension status
    /// </summary>
    private static Task<IResult> GetTestExtensionStatus(
        string extension,
        AsteriskProxyService asteriskService,
        ILogger<AsteriskProxyService> logger,
        [FromQuery] string context = "internal")
    {
        const string testEndpointId = "FreePBX-Test";
        return GetExtensionStatus(testEndpointId, extension, asteriskService, logger, context);
    }

    /// <summary>
    /// Refreshes test server extensions
    /// </summary>
    private static async Task<IResult> RefreshTestExtensions(
        AsteriskProxyService asteriskService,
        ILogger<AsteriskProxyService> logger,
        CancellationToken cancellationToken)
    {
        const string testEndpointId = "FreePBX-Test";
        return await RefreshExtensions(testEndpointId, asteriskService, logger, cancellationToken);
    }
}
