using Microsoft.AspNetCore.Mvc;
using Ngp.Services;
using Ngp.Models;

namespace Ngp.Endpoints;

/// <summary>
/// API endpoints for individual logic points management
/// Each logic point has its own dedicated engine instance for optimal performance
/// </summary>
public static class LogicPointEndpoints
{
    /// <summary>
    /// Maps logic point endpoints
    /// </summary>
    /// <param name="app">Web application</param>
    /// <returns>Web application</returns>
    public static WebApplication MapLogicPointEndpoints(this WebApplication app)
    {
        var group = app.MapGroup("/api/logic-points")
            .WithTags("Logic Points")
            .WithOpenApi();

        // Create a new logic point
        group.MapPost("/", CreateLogicPoint)
            .WithName("CreateLogicPoint")
            .WithSummary("Create a new logic point")
            .WithDescription("Creates a new logic point with its own dedicated engine instance")
            .Accepts<CreateLogicPointRequest>("application/json")
            .Produces<ApiResponse<LogicPointStatus>>(StatusCodes.Status201Created)
            .Produces<ApiResponse<string>>(StatusCodes.Status400BadRequest);

        // Update logic point input
        group.MapPut("/{pointId}/input", UpdateLogicPointInput)
            .WithName("UpdateLogicPointInput")
            .WithSummary("Update logic point input value")
            .WithDescription("Updates the input value for a specific logic point, triggering calculation")
            .Accepts<object>("application/json")
            .Produces<ApiResponse<object>>(StatusCodes.Status200OK)
            .Produces<ApiResponse<string>>(StatusCodes.Status404NotFound);

        // Get logic point output
        group.MapGet("/{pointId}/output", GetLogicPointOutput)
            .WithName("GetLogicPointOutput")
            .WithSummary("Get logic point output value")
            .WithDescription("Gets the current calculated output value for a specific logic point")
            .Produces<ApiResponse<object>>(StatusCodes.Status200OK)
            .Produces<ApiResponse<string>>(StatusCodes.Status404NotFound);

        // Get logic point status
        group.MapGet("/{pointId}/status", GetLogicPointStatus)
            .WithName("GetLogicPointStatus")
            .WithSummary("Get logic point status")
            .WithDescription("Gets detailed status information for a specific logic point")
            .Produces<ApiResponse<LogicPointStatus>>(StatusCodes.Status200OK)
            .Produces<ApiResponse<string>>(StatusCodes.Status404NotFound);

        // List all logic points
        group.MapGet("/", GetAllLogicPoints)
            .WithName("GetAllLogicPoints")
            .WithSummary("Get all logic points")
            .WithDescription("Gets status information for all logic points")
            .Produces<ApiResponse<Dictionary<string, LogicPointStatus>>>(StatusCodes.Status200OK);

        // Delete logic point
        group.MapDelete("/{pointId}", DeleteLogicPoint)
            .WithName("DeleteLogicPoint")
            .WithSummary("Delete a logic point")
            .WithDescription("Removes a logic point and disposes its engine instance")
            .Produces<ApiResponse<bool>>(StatusCodes.Status200OK)
            .Produces<ApiResponse<string>>(StatusCodes.Status404NotFound);

        // Batch create logic points
        group.MapPost("/batch", BatchCreateLogicPoints)
            .WithName("BatchCreateLogicPoints")
            .WithSummary("Batch create logic points")
            .WithDescription("Creates multiple logic points in a single request")
            .Accepts<BatchCreateLogicPointsRequest>("application/json")
            .Produces<ApiResponse<BatchCreateLogicPointsResponse>>(StatusCodes.Status200OK);

        // Batch update inputs
        group.MapPut("/batch/inputs", BatchUpdateInputs)
            .WithName("BatchUpdateLogicPointInputs")
            .WithSummary("Batch update logic point inputs")
            .WithDescription("Updates multiple logic point inputs in a single request")
            .Accepts<Dictionary<string, object>>("application/json")
            .Produces<ApiResponse<Dictionary<string, bool>>>(StatusCodes.Status200OK);

        return app;
    }

    /// <summary>
    /// Creates a new logic point
    /// </summary>
    private static async Task<IResult> CreateLogicPoint(
        [FromBody] CreateLogicPointRequest request,
        LogicPointManager logicPointManager,
        ILogger<LogicPointManager> logger)
    {
        try
        {
            var config = new LogicPointConfig
            {
                InputId = request.InputId,
                OutputId = request.OutputId,
                Formula = request.Formula,
                InputDataType = request.InputDataType,
                InitialValue = request.InitialValue,
                InputDescription = request.InputDescription,
                OutputDescription = request.OutputDescription
            };

            var success = logicPointManager.CreateLogicPoint(request.PointId, config);

            if (success)
            {
                var statuses = logicPointManager.GetLogicPointStatuses();
                var status = statuses[request.PointId];
                
                return Results.Created($"/api/logic-points/{request.PointId}", 
                    ApiResponse<LogicPointStatus>.CreateSuccess(status, "Logic point created successfully"));
            }
            else
            {
                return Results.BadRequest(ApiResponse<string>.CreateError("Failed to create logic point"));
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error creating logic point {PointId}", request.PointId);
            return Results.BadRequest(ApiResponse<string>.CreateError($"Error creating logic point: {ex.Message}"));
        }
    }

    /// <summary>
    /// Updates logic point input value
    /// </summary>
    private static async Task<IResult> UpdateLogicPointInput(
        string pointId,
        [FromBody] object value,
        LogicPointManager logicPointManager,
        ILogger<LogicPointManager> logger)
    {
        try
        {
            var success = logicPointManager.UpdateLogicPointInput(pointId, value);

            if (success)
            {
                var output = logicPointManager.GetLogicPointOutput(pointId);
                return Results.Ok(ApiResponse<object>.CreateSuccess(output, "Input updated successfully"));
            }
            else
            {
                return Results.NotFound(ApiResponse<string>.CreateError($"Logic point not found: {pointId}"));
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error updating logic point input {PointId}", pointId);
            return Results.BadRequest(ApiResponse<string>.CreateError($"Error updating input: {ex.Message}"));
        }
    }

    /// <summary>
    /// Gets logic point output value
    /// </summary>
    private static async Task<IResult> GetLogicPointOutput(
        string pointId,
        LogicPointManager logicPointManager,
        ILogger<LogicPointManager> logger)
    {
        try
        {
            var output = logicPointManager.GetLogicPointOutput(pointId);

            if (output != null)
            {
                return Results.Ok(ApiResponse<object>.CreateSuccess(output));
            }
            else
            {
                return Results.NotFound(ApiResponse<string>.CreateError($"Logic point not found: {pointId}"));
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting logic point output {PointId}", pointId);
            return Results.BadRequest(ApiResponse<string>.CreateError($"Error getting output: {ex.Message}"));
        }
    }

    /// <summary>
    /// Gets logic point status
    /// </summary>
    private static async Task<IResult> GetLogicPointStatus(
        string pointId,
        LogicPointManager logicPointManager,
        ILogger<LogicPointManager> logger)
    {
        try
        {
            var statuses = logicPointManager.GetLogicPointStatuses();

            if (statuses.TryGetValue(pointId, out var status))
            {
                return Results.Ok(ApiResponse<LogicPointStatus>.CreateSuccess(status));
            }
            else
            {
                return Results.NotFound(ApiResponse<string>.CreateError($"Logic point not found: {pointId}"));
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting logic point status {PointId}", pointId);
            return Results.BadRequest(ApiResponse<string>.CreateError($"Error getting status: {ex.Message}"));
        }
    }

    /// <summary>
    /// Gets all logic points
    /// </summary>
    private static async Task<IResult> GetAllLogicPoints(
        LogicPointManager logicPointManager,
        ILogger<LogicPointManager> logger)
    {
        try
        {
            var statuses = logicPointManager.GetLogicPointStatuses();
            return Results.Ok(ApiResponse<Dictionary<string, LogicPointStatus>>.CreateSuccess(statuses));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting all logic points");
            return Results.BadRequest(ApiResponse<string>.CreateError($"Error getting logic points: {ex.Message}"));
        }
    }

    /// <summary>
    /// Deletes a logic point
    /// </summary>
    private static async Task<IResult> DeleteLogicPoint(
        string pointId,
        LogicPointManager logicPointManager,
        ILogger<LogicPointManager> logger)
    {
        try
        {
            var success = logicPointManager.RemoveLogicPoint(pointId);

            if (success)
            {
                return Results.Ok(ApiResponse<bool>.CreateSuccess(true, "Logic point deleted successfully"));
            }
            else
            {
                return Results.NotFound(ApiResponse<string>.CreateError($"Logic point not found: {pointId}"));
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error deleting logic point {PointId}", pointId);
            return Results.BadRequest(ApiResponse<string>.CreateError($"Error deleting logic point: {ex.Message}"));
        }
    }

    /// <summary>
    /// Batch creates logic points
    /// </summary>
    private static async Task<IResult> BatchCreateLogicPoints(
        [FromBody] BatchCreateLogicPointsRequest request,
        LogicPointManager logicPointManager,
        ILogger<LogicPointManager> logger)
    {
        try
        {
            var results = new Dictionary<string, bool>();
            var errors = new List<string>();

            foreach (var pointRequest in request.LogicPoints)
            {
                try
                {
                    var config = new LogicPointConfig
                    {
                        InputId = pointRequest.InputId,
                        OutputId = pointRequest.OutputId,
                        Formula = pointRequest.Formula,
                        InputDataType = pointRequest.InputDataType,
                        InitialValue = pointRequest.InitialValue,
                        InputDescription = pointRequest.InputDescription,
                        OutputDescription = pointRequest.OutputDescription
                    };

                    var success = logicPointManager.CreateLogicPoint(pointRequest.PointId, config);
                    results[pointRequest.PointId] = success;

                    if (!success)
                    {
                        errors.Add($"Failed to create logic point: {pointRequest.PointId}");
                    }
                }
                catch (Exception ex)
                {
                    results[pointRequest.PointId] = false;
                    errors.Add($"Error creating {pointRequest.PointId}: {ex.Message}");
                }
            }

            var response = new BatchCreateLogicPointsResponse
            {
                Results = results,
                Errors = errors,
                SuccessCount = results.Values.Count(r => r),
                TotalCount = results.Count
            };

            return Results.Ok(ApiResponse<BatchCreateLogicPointsResponse>.CreateSuccess(response));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in batch create logic points");
            return Results.BadRequest(ApiResponse<string>.CreateError($"Error in batch create: {ex.Message}"));
        }
    }

    /// <summary>
    /// Batch updates logic point inputs
    /// </summary>
    private static async Task<IResult> BatchUpdateInputs(
        [FromBody] Dictionary<string, object> updates,
        LogicPointManager logicPointManager,
        ILogger<LogicPointManager> logger)
    {
        try
        {
            var results = new Dictionary<string, bool>();

            foreach (var update in updates)
            {
                try
                {
                    var success = logicPointManager.UpdateLogicPointInput(update.Key, update.Value);
                    results[update.Key] = success;
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error updating logic point {PointId}", update.Key);
                    results[update.Key] = false;
                }
            }

            return Results.Ok(ApiResponse<Dictionary<string, bool>>.CreateSuccess(results));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in batch update inputs");
            return Results.BadRequest(ApiResponse<string>.CreateError($"Error in batch update: {ex.Message}"));
        }
    }
}
