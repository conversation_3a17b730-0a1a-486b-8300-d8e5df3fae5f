using Microsoft.AspNetCore.Mvc;
using Ngp.Services;
using Ngp.Shared.Models;

namespace Ngp.Endpoints;

/// <summary>
/// Minimal API endpoints for Soyal access control proxy
/// </summary>
public static class SoyalProxyEndpoints
{
    /// <summary>
    /// Map Soyal proxy endpoints
    /// </summary>
    /// <param name="app">Web application instance</param>
    /// <returns>Web application instance for method chaining</returns>
    public static WebApplication MapSoyalProxyEndpoints(this WebApplication app)
    {
        var group = app.MapGroup("/api/soyal")
            .WithTags("Soyal Access Control")
            .WithOpenApi();

        // Get all door statuses
        group.MapGet("/doors", async ([FromServices] SoyalProxyService service) =>
        {
            try
            {
                var soyalProxy = service.GetSoyalProxyService();
                var doors = await soyalProxy.GetAllDoorStatusesAsync();
                return Results.Ok(doors);
            }
            catch (Exception ex)
            {
                return Results.Problem($"Failed to get door statuses: {ex.Message}");
            }
        })
        .WithName("GetAllDoorStatuses")
        .WithSummary("Get all door statuses")
        .WithDescription("Retrieves the current status of all monitored doors")
        .Produces<IEnumerable<DoorStatus>>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status500InternalServerError);

        // Get specific door status
        group.MapGet("/doors/{area:int}/{nodeId:int}", async (
            [FromRoute] int area,
            [FromRoute] int nodeId,
            [FromServices] SoyalProxyService service) =>
        {
            try
            {
                if (area < 0 || area > 255)
                    return Results.BadRequest("Area must be between 0 and 255");

                if (nodeId < 1 || nodeId > 254)
                    return Results.BadRequest("Node ID must be between 1 and 254");

                var soyalProxy = service.GetSoyalProxyService();
                var door = await soyalProxy.GetDoorStatusAsync((byte)area, (byte)nodeId);
                
                if (door == null)
                    return Results.NotFound($"Door not found for Area {area}, Node {nodeId}");

                return Results.Ok(door);
            }
            catch (Exception ex)
            {
                return Results.Problem($"Failed to get door status: {ex.Message}");
            }
        })
        .WithName("GetDoorStatus")
        .WithSummary("Get specific door status")
        .WithDescription("Retrieves the current status of a specific door by area and node ID")
        .Produces<DoorStatus>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status400BadRequest)
        .Produces(StatusCodes.Status404NotFound)
        .Produces(StatusCodes.Status500InternalServerError);

        // Unlock door
        group.MapPost("/doors/{area:int}/{nodeId:int}/unlock", async (
            [FromRoute] int area,
            [FromRoute] int nodeId,
            [FromServices] SoyalProxyService service) =>
        {
            try
            {
                if (area < 0 || area > 255)
                    return Results.BadRequest("Area must be between 0 and 255");

                if (nodeId < 1 || nodeId > 254)
                    return Results.BadRequest("Node ID must be between 1 and 254");

                var command = new AccessControlCommand
                {
                    Area = (byte)area,
                    NodeId = (byte)nodeId,
                    CommandType = AccessControlCommandType.Unlock
                };

                var soyalProxy = service.GetSoyalProxyService();
                await soyalProxy.ExecuteCommandAsync(command);

                return Results.Ok(new { Message = $"Unlock command sent to Area {area}, Node {nodeId}" });
            }
            catch (Exception ex)
            {
                return Results.Problem($"Failed to unlock door: {ex.Message}");
            }
        })
        .WithName("UnlockDoor")
        .WithSummary("Unlock door")
        .WithDescription("Sends an unlock command to the specified door")
        .Produces<object>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status400BadRequest)
        .Produces(StatusCodes.Status500InternalServerError);

        // Lock door
        group.MapPost("/doors/{area:int}/{nodeId:int}/lock", async (
            [FromRoute] int area,
            [FromRoute] int nodeId,
            [FromServices] SoyalProxyService service) =>
        {
            try
            {
                if (area < 0 || area > 255)
                    return Results.BadRequest("Area must be between 0 and 255");

                if (nodeId < 1 || nodeId > 254)
                    return Results.BadRequest("Node ID must be between 1 and 254");

                var command = new AccessControlCommand
                {
                    Area = (byte)area,
                    NodeId = (byte)nodeId,
                    CommandType = AccessControlCommandType.Lock
                };

                var soyalProxy = service.GetSoyalProxyService();
                await soyalProxy.ExecuteCommandAsync(command);

                return Results.Ok(new { Message = $"Lock command sent to Area {area}, Node {nodeId}" });
            }
            catch (Exception ex)
            {
                return Results.Problem($"Failed to lock door: {ex.Message}");
            }
        })
        .WithName("LockDoor")
        .WithSummary("Lock door")
        .WithDescription("Sends a lock command to the specified door")
        .Produces<object>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status400BadRequest)
        .Produces(StatusCodes.Status500InternalServerError);

        // Query door status
        group.MapPost("/doors/{area:int}/{nodeId:int}/query", async (
            [FromRoute] int area,
            [FromRoute] int nodeId,
            [FromServices] SoyalProxyService service) =>
        {
            try
            {
                if (area < 0 || area > 255)
                    return Results.BadRequest("Area must be between 0 and 255");

                if (nodeId < 1 || nodeId > 254)
                    return Results.BadRequest("Node ID must be between 1 and 254");

                var command = new AccessControlCommand
                {
                    Area = (byte)area,
                    NodeId = (byte)nodeId,
                    CommandType = AccessControlCommandType.QueryStatus
                };

                var soyalProxy = service.GetSoyalProxyService();
                await soyalProxy.ExecuteCommandAsync(command);

                return Results.Ok(new { Message = $"Query status command sent to Area {area}, Node {nodeId}" });
            }
            catch (Exception ex)
            {
                return Results.Problem($"Failed to query door status: {ex.Message}");
            }
        })
        .WithName("QueryDoorStatus")
        .WithSummary("Query door status")
        .WithDescription("Sends a query status command to the specified door")
        .Produces<object>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status400BadRequest)
        .Produces(StatusCodes.Status500InternalServerError);

        // Batch operations
        group.MapPost("/doors/batch", async (
            [FromBody] BatchDoorCommand[] commands,
            [FromServices] SoyalProxyService service) =>
        {
            try
            {
                if (commands == null || commands.Length == 0)
                    return Results.BadRequest("Commands array cannot be null or empty");

                var results = new List<BatchResult>();
                var soyalProxy = service.GetSoyalProxyService();

                foreach (var cmd in commands)
                {
                    try
                    {
                        if (cmd.Area < 0 || cmd.Area > 255)
                        {
                            results.Add(new BatchResult
                            {
                                Area = cmd.Area,
                                NodeId = cmd.NodeId,
                                Success = false,
                                Message = "Area must be between 0 and 255"
                            });
                            continue;
                        }

                        if (cmd.NodeId < 1 || cmd.NodeId > 254)
                        {
                            results.Add(new BatchResult
                            {
                                Area = cmd.Area,
                                NodeId = cmd.NodeId,
                                Success = false,
                                Message = "Node ID must be between 1 and 254"
                            });
                            continue;
                        }

                        var command = new AccessControlCommand
                        {
                            Area = (byte)cmd.Area,
                            NodeId = (byte)cmd.NodeId,
                            CommandType = cmd.CommandType
                        };

                        await soyalProxy.ExecuteCommandAsync(command);

                        results.Add(new BatchResult
                        {
                            Area = cmd.Area,
                            NodeId = cmd.NodeId,
                            Success = true,
                            Message = $"{cmd.CommandType} command sent successfully"
                        });
                    }
                    catch (Exception ex)
                    {
                        results.Add(new BatchResult
                        {
                            Area = cmd.Area,
                            NodeId = cmd.NodeId,
                            Success = false,
                            Message = ex.Message
                        });
                    }
                }

                return Results.Ok(results);
            }
            catch (Exception ex)
            {
                return Results.Problem($"Failed to execute batch commands: {ex.Message}");
            }
        })
        .WithName("BatchDoorCommands")
        .WithSummary("Execute batch door commands")
        .WithDescription("Executes multiple door commands in a single request")
        .Produces<IEnumerable<BatchResult>>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status400BadRequest)
        .Produces(StatusCodes.Status500InternalServerError);

        return app;
    }
}

/// <summary>
/// Batch door command request
/// </summary>
public record BatchDoorCommand(int Area, int NodeId, AccessControlCommandType CommandType);

/// <summary>
/// Batch operation result
/// </summary>
public record BatchResult
{
    public int Area { get; init; }
    public int NodeId { get; init; }
    public bool Success { get; init; }
    public string Message { get; init; } = string.Empty;
}