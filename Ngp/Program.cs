using Ngp.Endpoints;
using Ngp.Services;
using Ngp.Shared.Interfaces;

var builder = WebApplication.CreateBuilder(args);

builder.AddServiceDefaults();

// Add services to the container.
// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
builder.Services.AddOpenApi();

// Add ModbusTCP service
// builder.Services.AddSingleton<ModbusTcpService>();
// builder.Services.AddHostedService<ModbusTcpService>(provider => provider.GetRequiredService<ModbusTcpService>());

// Add 24Dio service
//builder.Services.AddSingleton<TwentyFourDioService>();
//builder.Services.AddHostedService<TwentyFourDioService>(provider => provider.GetRequiredService<TwentyFourDioService>());

// Add AsteriskProxy service
// builder.Services.AddSingleton<AsteriskProxyService>();
// builder.Services.AddHostedService<AsteriskProxyService>(provider => provider.GetRequiredService<AsteriskProxyService>());

// Add IP Monitor service
// builder.Services.AddSingleton<IpMonitorService>();
// builder.Services.AddHostedService<IpMonitorService>(provider => provider.GetRequiredService<IpMonitorService>());

// Add Logic Engine service (legacy - multiple formulas per engine)
// NOTE: This approach is NOT recommended for independent calculations
// builder.Services.AddSingleton<LogicEngineServiceSimple>();
// builder.Services.AddHostedService<LogicEngineServiceSimple>(provider => provider.GetRequiredService<LogicEngineServiceSimple>());

// Add Logic Point Manager (RECOMMENDED - one engine per logic point)
// builder.Services.AddSingleton<LogicPointManager>();

// Add Logic Point Demo Service (demonstrates best practices)
// builder.Services.AddSingleton<LogicPointDemoService>();
// builder.Services.AddHostedService<LogicPointDemoService>(provider => provider.GetRequiredService<LogicPointDemoService>());

// Add SIP Message Proxy service
//builder.Services.AddSingleton<SipMessageProxyService>();
//builder.Services.AddHostedService<SipMessageProxyService>(provider => provider.GetRequiredService<SipMessageProxyService>());

// Add SIP Message Service Adapter for NotifyEngine integration
//builder.Services.AddSingleton<SipMessageServiceAdapter>();
//builder.Services.AddSingleton<Ngp.Shared.Interfaces.ISipMessageService>(provider =>
//    provider.GetRequiredService<SipMessageServiceAdapter>());

// Add NotifyEngine service
// builder.Services.AddSingleton<NotifyEngineService>();
// builder.Services.AddHostedService<NotifyEngineService>(provider => provider.GetRequiredService<NotifyEngineService>());

// Add ModbusTcp master factory for SoyalProxy integration
builder.Services.AddSingleton<Ngp.Shared.Factories.IModbusTcpMasterFactory, ModbusTcpMasterFactoryImplementation>();

// Add SoyalProxy service
builder.Services.AddSingleton<SoyalProxyService>();
builder.Services.AddHostedService<SoyalProxyService>(provider => 
    provider.GetRequiredService<SoyalProxyService>());



// Add Swagger services
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo
    {
        Title = "NGP API",
        Version = "v1",
        Description = "Next Generation Platform API Documentation"
    });
});

var app = builder.Build();

app.MapDefaultEndpoints();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();

    // Enable Swagger UI
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "NGP API v1");
        c.RoutePrefix = "swagger"; // Set Swagger UI at /swagger
    });
}

app.UseHttpsRedirection();

// Map Modbus endpoints
// app.MapModbusEndpoints();

// Map 24Dio endpoints
//app.MapTwentyFourDioEndpoints();

// Map AsteriskProxy endpoints
// app.MapAsteriskProxyEndpoints();

// Map IP Monitor endpoints
// app.MapIpMonitorEndpoints();

// Map Logic Engine endpoints (legacy - multiple formulas per engine)
// NOTE: This approach is NOT recommended for independent calculations
// app.MapLogicEngineEndpoints();

// Map Logic Point endpoints (RECOMMENDED - one engine per logic point)
// app.MapLogicPointEndpoints();

// Map SIP Message Proxy endpoints
// app.MapSipMessageProxyEndpoints();

// Map NotifyEngine endpoints
// app.MapNotifyEndpoints();

// Map SoyalProxy endpoints
app.MapSoyalProxyEndpoints();


var summaries = new[]
{
    "Freezing", "Bracing", "Chilly", "Cool", "Mild", "Warm", "Balmy", "Hot", "Sweltering", "Scorching"
};

app.MapGet("/weatherforecast", () =>
{
    var forecast =  Enumerable.Range(1, 5).Select(index =>
        new WeatherForecast
        (
            DateOnly.FromDateTime(DateTime.Now.AddDays(index)),
            Random.Shared.Next(-20, 55),
            summaries[Random.Shared.Next(summaries.Length)]
        ))
        .ToArray();
    return forecast;
})
.WithName("GetWeatherForecast")
.WithSummary("Get weather forecast")
.WithDescription("Returns a 5-day weather forecast with random temperature and weather conditions")
.WithTags("Weather")
.Produces<WeatherForecast[]>(StatusCodes.Status200OK);

app.Run();

record WeatherForecast(DateOnly Date, int TemperatureC, string? Summary)
{
    public int TemperatureF => 32 + (int)(TemperatureC / 0.5556);
}
