using System.ComponentModel.DataAnnotations;
using Ngp.Entities.Enums;

namespace Ngp.Entities;

/// <summary>
/// Represents a node in the system with its configuration and current state
/// </summary>
public class Node
{
    /// <summary>
    /// Primary key for the node
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// Unique identifier for the node within the system
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string NodeId { get; set; } = string.Empty;
    
    /// <summary>
    /// Display name for the node
    /// </summary>
    [Required]
    [MaxLength(200)]
    public string Name { get; set; } = string.Empty;
    
    /// <summary>
    /// Description of the node
    /// </summary>
    [MaxLength(500)]
    public string Description { get; set; } = string.Empty;
    
    /// <summary>
    /// Type of the node (Boolean, Analog, String, Custom)
    /// </summary>
    public NodeType NodeType { get; set; }
    
    /// <summary>
    /// Whether the node is enabled for processing
    /// </summary>
    public bool IsEnabled { get; set; } = true;
    
    /// <summary>
    /// Whether logging is enabled for this node
    /// </summary>
    public bool IsLogEnabled { get; set; } = true;
    
    /// <summary>
    /// Whether this node represents a failure condition
    /// </summary>
    public bool IsFailureNode { get; set; } = false;
    
    /// <summary>
    /// Whether to display runtime information
    /// </summary>
    public bool DisplayRuntime { get; set; } = true;
    
    /// <summary>
    /// Number of decimal places for numeric display
    /// </summary>
    public byte DecimalPlaces { get; set; } = 2;
    
    /// <summary>
    /// Unit of measurement for the node value
    /// </summary>
    [MaxLength(50)]
    public string Unit { get; set; } = string.Empty;
    
    /// <summary>
    /// Scale factor for value conversion
    /// </summary>
    public double Scale { get; set; } = 1.0;
    
    /// <summary>
    /// Current value of the node (stored as string for flexibility)
    /// </summary>
    [MaxLength(1000)]
    public string CurrentValue { get; set; } = string.Empty;
    
    /// <summary>
    /// Timestamp when the value was last updated
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// Quality indicator for the current value
    /// </summary>
    public bool IsGoodQuality { get; set; } = true;
    
    /// <summary>
    /// Data path ID that this node belongs to
    /// </summary>
    public int? DataPathId { get; set; }
    
    /// <summary>
    /// Navigation property to the data path
    /// </summary>
    public virtual DataPath? DataPath { get; set; }
    
    /// <summary>
    /// Custom node template ID if this is a custom node
    /// </summary>
    public int? CustomNodeTemplateId { get; set; }
    
    /// <summary>
    /// Navigation property to the custom node template
    /// </summary>
    public virtual CustomNodeTemplate? CustomNodeTemplate { get; set; }
    
    /// <summary>
    /// Lookup table ID for value mapping
    /// </summary>
    public int? LookupTableId { get; set; }
    
    /// <summary>
    /// Navigation property to the lookup table
    /// </summary>
    public virtual LookupTable? LookupTable { get; set; }
    
    /// <summary>
    /// History recording mode for this node
    /// </summary>
    public HistoryRecordMode? HistoryRecordMode { get; set; }
    
    /// <summary>
    /// Threshold for instantaneous recording mode
    /// </summary>
    public double? ChangeThreshold { get; set; }
    
    /// <summary>
    /// Recording interval in seconds for instantaneous mode
    /// </summary>
    public int? RecordingIntervalSeconds { get; set; }
    
    /// <summary>
    /// Whether this node should be recorded in history
    /// </summary>
    public bool EnableHistoryRecording { get; set; } = false;
    
    /// <summary>
    /// Creation timestamp
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// Last modification timestamp
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
}
