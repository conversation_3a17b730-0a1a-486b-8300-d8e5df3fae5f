namespace Ngp.Entities.Enums;

/// <summary>
/// Defines the type of node in the system
/// </summary>
public enum NodeType
{
    /// <summary>
    /// Boolean node type for digital inputs/outputs
    /// </summary>
    Boolean = 1,
    
    /// <summary>
    /// Analog node type for numeric values
    /// </summary>
    Analog = 2,
    
    /// <summary>
    /// String node type for text values
    /// </summary>
    String = 3,
    
    /// <summary>
    /// Custom advanced node type with multiple values
    /// </summary>
    Custom = 4
}
