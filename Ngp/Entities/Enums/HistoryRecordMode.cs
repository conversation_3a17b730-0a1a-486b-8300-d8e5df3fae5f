namespace Ngp.Entities.Enums;

/// <summary>
/// Defines the recording mode for history records
/// </summary>
public enum HistoryRecordMode
{
    /// <summary>
    /// Cumulative usage recording (e.g., meter readings)
    /// Records at 0, 15, 30, 45 minutes of each hour
    /// </summary>
    Cumulative = 1,
    
    /// <summary>
    /// Instantaneous value recording (e.g., air quality sensors)
    /// Records based on time intervals and threshold changes
    /// </summary>
    Instantaneous = 2,
    
    /// <summary>
    /// Boolean state change recording
    /// Records only when state changes
    /// </summary>
    StateChange = 3
}
