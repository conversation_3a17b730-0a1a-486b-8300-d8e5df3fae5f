using System.ComponentModel.DataAnnotations;
using System.Text.Json;

namespace Ngp.Entities;

/// <summary>
/// Represents a template for custom advanced node types
/// Supports flexible node definitions like three-phase meters, water meters, air quality sensors
/// </summary>
public class CustomNodeTemplate
{
    /// <summary>
    /// Primary key for the template
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// Unique identifier for the template
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string TemplateId { get; set; } = string.Empty;
    
    /// <summary>
    /// Display name of the template
    /// </summary>
    [Required]
    [MaxLength(200)]
    public string Name { get; set; } = string.Empty;
    
    /// <summary>
    /// Description of the template
    /// </summary>
    [MaxLength(500)]
    public string Description { get; set; } = string.Empty;
    
    /// <summary>
    /// Category of the template (e.g., "Electrical", "Water", "Environmental")
    /// </summary>
    [MaxLength(100)]
    public string Category { get; set; } = string.Empty;
    
    /// <summary>
    /// Template definition stored as JSON
    /// Contains field definitions, data types, units, etc.
    /// </summary>
    public JsonDocument TemplateDefinition { get; set; } = null!;
    
    /// <summary>
    /// JSON configuration for which fields should be recorded in history
    /// </summary>
    public JsonDocument? HistoryFieldsConfig { get; set; }
    
    /// <summary>
    /// Whether this template is active and available for use
    /// </summary>
    public bool IsActive { get; set; } = true;
    
    /// <summary>
    /// Version of the template for tracking changes
    /// </summary>
    [MaxLength(20)]
    public string Version { get; set; } = "1.0";
    
    /// <summary>
    /// Navigation property to nodes using this template
    /// </summary>
    public virtual ICollection<Node> Nodes { get; set; } = null!;
    
    /// <summary>
    /// Creation timestamp
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// Last modification timestamp
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// User who created this template
    /// </summary>
    [MaxLength(100)]
    public string CreatedBy { get; set; } = string.Empty;
}
