using System.ComponentModel.DataAnnotations;
using System.Text.Json;

namespace Ngp.Entities;

/// <summary>
/// Represents a lookup table for mapping node values to corresponding strings
/// </summary>
public class LookupTable
{
    /// <summary>
    /// Primary key for the lookup table
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// Unique identifier for the lookup table
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string TableId { get; set; } = string.Empty;
    
    /// <summary>
    /// Display name of the lookup table
    /// </summary>
    [Required]
    [MaxLength(200)]
    public string Name { get; set; } = string.Empty;
    
    /// <summary>
    /// Description of the lookup table
    /// </summary>
    [MaxLength(500)]
    public string Description { get; set; } = string.Empty;
    
    /// <summary>
    /// Lookup mappings stored as JSON in jsonb format
    /// Structure: { "mappings": [{ "value": "input_value", "text": "output_text" }] }
    /// </summary>
    public JsonDocument LookupMappings { get; set; } = null!;
    
    /// <summary>
    /// Default text to return when no mapping is found
    /// </summary>
    [MaxLength(200)]
    public string DefaultText { get; set; } = string.Empty;
    
    /// <summary>
    /// Whether this lookup table is active
    /// </summary>
    public bool IsActive { get; set; } = true;
    
    /// <summary>
    /// Navigation property to nodes using this lookup table
    /// </summary>
    public virtual ICollection<Node> Nodes { get; set; } = null!;
    
    /// <summary>
    /// Creation timestamp
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// Last modification timestamp
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
}
