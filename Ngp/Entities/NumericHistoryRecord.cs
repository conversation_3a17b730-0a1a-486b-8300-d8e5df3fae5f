using System.ComponentModel.DataAnnotations;
using Ngp.Entities.Enums;

namespace Ngp.Entities;

/// <summary>
/// Represents numeric history records for nodes
/// Uses NodeId and Timestamp as composite primary key
/// </summary>
public class NumericHistoryRecord
{
    /// <summary>
    /// Node ID (part of composite primary key)
    /// </summary>
    public int NodeId { get; set; }
    
    /// <summary>
    /// Timestamp of the record (part of composite primary key)
    /// </summary>
    public DateTime Timestamp { get; set; }
    
    /// <summary>
    /// Numeric value recorded
    /// </summary>
    public double Value { get; set; }
    
    /// <summary>
    /// Recording mode used for this record
    /// </summary>
    public HistoryRecordMode RecordMode { get; set; }
    
    /// <summary>
    /// Navigation property to the node
    /// </summary>
    public virtual Node Node { get; set; } = null!;
}
