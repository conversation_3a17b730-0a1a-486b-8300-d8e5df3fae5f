using System.ComponentModel.DataAnnotations;

namespace Ngp.Entities;

/// <summary>
/// Represents a hierarchical data path in the system
/// Similar to "\\SiteA\BuildingB\FloorC\MeterA\ReadingC" concept
/// </summary>
public class DataPath
{
    /// <summary>
    /// Primary key for the data path
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// Name of this path segment (supports Chinese characters)
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;
    
    /// <summary>
    /// Full path from root to this segment (e.g., "SiteA\\BuildingB\\FloorC")
    /// </summary>
    [Required]
    [MaxLength(1000)]
    public string FullPath { get; set; } = string.Empty;
    
    /// <summary>
    /// Level in the hierarchy (0 = root level)
    /// </summary>
    public int Level { get; set; }
    
    /// <summary>
    /// Parent data path ID (null for root level)
    /// </summary>
    public int? ParentId { get; set; }
    
    /// <summary>
    /// Navigation property to parent data path
    /// </summary>
    public virtual DataPath? Parent { get; set; }
    
    /// <summary>
    /// Navigation property to child data paths
    /// </summary>
    public virtual ICollection<DataPath> Children { get; set; } = null!;
    
    /// <summary>
    /// Navigation property to nodes in this data path
    /// </summary>
    public virtual ICollection<Node> Nodes { get; set; } = null!;
    
    /// <summary>
    /// Description of this data path segment
    /// </summary>
    [MaxLength(500)]
    public string Description { get; set; } = string.Empty;
    
    /// <summary>
    /// Whether this data path is active
    /// </summary>
    public bool IsActive { get; set; } = true;
    
    /// <summary>
    /// Display order within the same level
    /// </summary>
    public int DisplayOrder { get; set; } = 0;
    
    /// <summary>
    /// Creation timestamp
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// Last modification timestamp
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
}
