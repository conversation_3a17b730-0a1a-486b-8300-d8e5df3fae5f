using System.ComponentModel.DataAnnotations;

namespace Ngp.Entities;

/// <summary>
/// Represents boolean history records for nodes
/// Uses NodeId and Timestamp as composite primary key
/// </summary>
public class BooleanHistoryRecord
{
    /// <summary>
    /// Node ID (part of composite primary key)
    /// </summary>
    public int NodeId { get; set; }
    
    /// <summary>
    /// Timestamp of the record (part of composite primary key)
    /// </summary>
    public DateTime Timestamp { get; set; }
    
    /// <summary>
    /// Boolean value recorded
    /// </summary>
    public bool Value { get; set; }
    
    /// <summary>
    /// Navigation property to the node
    /// </summary>
    public virtual Node Node { get; set; } = null!;
}
