<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>4610a975-a092-4e9f-80c9-fd3ec77e9a20</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Mediator.SourceGenerator" Version="2.1.7">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.6">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.FASTER.Core" Version="2.3.2" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.22.1-Preview.1" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.10" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Ngp.ServiceDefaults\Ngp.ServiceDefaults.csproj" />
    <ProjectReference Include="..\Ngp.Communication.ModbusTcpMaster\Ngp.Communication.ModbusTcpMaster.csproj" />
    <ProjectReference Include="..\Ngp.Communication.TwentyFourDioPoller\Ngp.Communication.TwentyFourDioPoller.csproj" />
    <ProjectReference Include="..\Ngp.Communication.IpMonitor\Ngp.Communication.IpMonitor.csproj" />
    <ProjectReference Include="..\Ngp.Communication.AsteriskProxy\Ngp.Communication.AsteriskProxy.csproj" />
    <ProjectReference Include="..\Ngp.Calculation.LogicEngine\Ngp.Calculation.LogicEngine.csproj" />
    <ProjectReference Include="..\Ngp.Communication.SipMessageProxy\Ngp.Communication.SipMessageProxy.csproj" />
    <ProjectReference Include="..\Ngp.Shared\Ngp.Shared.csproj" />
    <ProjectReference Include="..\Ngp.Communication.SoyalProxy\Ngp.Communication.SoyalProxy.csproj" />
    <ProjectReference Include="..\Ngp.Communication.NotifyEngine\Ngp.Communication.NotifyEngine.csproj" />
  </ItemGroup>

</Project>
