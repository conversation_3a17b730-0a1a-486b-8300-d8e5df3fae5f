### Soyal Proxy API Tests
### Server: ************, Modbus Port: 502, JSON Port: 1001 (default)
### Areas: 0 (Nodes 1-5), 1 (Nodes 1-5)

@baseUrl = https://localhost:7001
@proxyId = TestSoyalProxy

### 1. Get all Soyal proxies
GET {{baseUrl}}/api/soyal/proxies
Accept: application/json

### 2. Get test proxy status
GET {{baseUrl}}/api/soyal/proxies/{{proxyId}}/status
Accept: application/json

### 3. Get all doors for test proxy
GET {{baseUrl}}/api/soyal/proxies/{{proxyId}}/doors
Accept: application/json

### 4. Get specific door status - Area 0, Node 1
GET {{baseUrl}}/api/soyal/proxies/{{proxyId}}/doors/0/1
Accept: application/json

### 5. Get specific door status - Area 0, Node 5
GET {{baseUrl}}/api/soyal/proxies/{{proxyId}}/doors/0/5
Accept: application/json

### 6. Get specific door status - Area 1, Node 1
GET {{baseUrl}}/api/soyal/proxies/{{proxyId}}/doors/1/1
Accept: application/json

### 7. Get specific door status - Area 1, Node 5
GET {{baseUrl}}/api/soyal/proxies/{{proxyId}}/doors/1/5
Accept: application/json

### 8. Unlock door - Area 0, Node 1
POST {{baseUrl}}/api/soyal/proxies/{{proxyId}}/doors/0/1/unlock
Accept: application/json

### 9. Unlock door - Area 0, Node 3
POST {{baseUrl}}/api/soyal/proxies/{{proxyId}}/doors/0/3/unlock
Accept: application/json

### 10. Unlock door - Area 1, Node 2
POST {{baseUrl}}/api/soyal/proxies/{{proxyId}}/doors/1/2/unlock
Accept: application/json

### 11. Unlock door - Area 1, Node 4
POST {{baseUrl}}/api/soyal/proxies/{{proxyId}}/doors/1/4/unlock
Accept: application/json

### 12. Add a new proxy (example)
POST {{baseUrl}}/api/soyal/proxies
Content-Type: application/json

{
  "proxyId": "CustomSoyalProxy",
  "serverIp": "************",
  "modbusTcpPort": 502,
  "jsonCommandPort": 1001,
  "areas": [
    {
      "areaId": 2,
      "startNode": 1,
      "endNode": 3
    },
    {
      "areaId": 3,
      "startNode": 10,
      "endNode": 15
    }
  ]
}

### 13. Get custom proxy status
GET {{baseUrl}}/api/soyal/proxies/CustomSoyalProxy/status
Accept: application/json

### 14. Remove custom proxy
DELETE {{baseUrl}}/api/soyal/proxies/CustomSoyalProxy

### 15. Test error case - Get non-existent proxy
GET {{baseUrl}}/api/soyal/proxies/NonExistentProxy/status
Accept: application/json

### 16. Test error case - Get non-existent door
GET {{baseUrl}}/api/soyal/proxies/{{proxyId}}/doors/99/99
Accept: application/json

### 17. Test error case - Unlock non-existent door
POST {{baseUrl}}/api/soyal/proxies/{{proxyId}}/doors/99/99/unlock
Accept: application/json

### 18. Batch test - Check all doors in Area 0
GET {{baseUrl}}/api/soyal/proxies/{{proxyId}}/doors/0/1
Accept: application/json

###
GET {{baseUrl}}/api/soyal/proxies/{{proxyId}}/doors/0/2
Accept: application/json

###
GET {{baseUrl}}/api/soyal/proxies/{{proxyId}}/doors/0/3
Accept: application/json

###
GET {{baseUrl}}/api/soyal/proxies/{{proxyId}}/doors/0/4
Accept: application/json

###
GET {{baseUrl}}/api/soyal/proxies/{{proxyId}}/doors/0/5
Accept: application/json

### 19. Batch test - Check all doors in Area 1
GET {{baseUrl}}/api/soyal/proxies/{{proxyId}}/doors/1/1
Accept: application/json

###
GET {{baseUrl}}/api/soyal/proxies/{{proxyId}}/doors/1/2
Accept: application/json

###
GET {{baseUrl}}/api/soyal/proxies/{{proxyId}}/doors/1/3
Accept: application/json

###
GET {{baseUrl}}/api/soyal/proxies/{{proxyId}}/doors/1/4
Accept: application/json

###
GET {{baseUrl}}/api/soyal/proxies/{{proxyId}}/doors/1/5
Accept: application/json

### 20. Batch unlock test - Unlock all doors in Area 0
POST {{baseUrl}}/api/soyal/proxies/{{proxyId}}/doors/0/1/unlock
Accept: application/json

###
POST {{baseUrl}}/api/soyal/proxies/{{proxyId}}/doors/0/2/unlock
Accept: application/json

###
POST {{baseUrl}}/api/soyal/proxies/{{proxyId}}/doors/0/3/unlock
Accept: application/json

###
POST {{baseUrl}}/api/soyal/proxies/{{proxyId}}/doors/0/4/unlock
Accept: application/json

###
POST {{baseUrl}}/api/soyal/proxies/{{proxyId}}/doors/0/5/unlock
Accept: application/json

### 21. Batch unlock test - Unlock all doors in Area 1
POST {{baseUrl}}/api/soyal/proxies/{{proxyId}}/doors/1/1/unlock
Accept: application/json

###
POST {{baseUrl}}/api/soyal/proxies/{{proxyId}}/doors/1/2/unlock
Accept: application/json

###
POST {{baseUrl}}/api/soyal/proxies/{{proxyId}}/doors/1/3/unlock
Accept: application/json

###
POST {{baseUrl}}/api/soyal/proxies/{{proxyId}}/doors/1/4/unlock
Accept: application/json

###
POST {{baseUrl}}/api/soyal/proxies/{{proxyId}}/doors/1/5/unlock
Accept: application/json
