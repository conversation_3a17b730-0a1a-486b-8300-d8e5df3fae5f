using Microsoft.EntityFrameworkCore;
using Ngp.Entities;
using Ngp.Entities.Enums;

namespace Ngp.Data;

/// <summary>
/// Application database context for the node system
/// </summary>
public class AppDbContext : DbContext
{
    public AppDbContext(DbContextOptions<AppDbContext> options) : base(options)
    {
    }
    
    /// <summary>
    /// Nodes in the system
    /// </summary>
    public DbSet<Node> Nodes { get; set; } = null!;
    
    /// <summary>
    /// Hierarchical data paths
    /// </summary>
    public DbSet<DataPath> DataPaths { get; set; } = null!;
    
    /// <summary>
    /// Custom node templates
    /// </summary>
    public DbSet<CustomNodeTemplate> CustomNodeTemplates { get; set; } = null!;
    
    /// <summary>
    /// Lookup tables for value mapping
    /// </summary>
    public DbSet<LookupTable> LookupTables { get; set; } = null!;
    
    /// <summary>
    /// Numeric history records
    /// </summary>
    public DbSet<NumericHistoryRecord> NumericHistoryRecords { get; set; } = null!;
    
    /// <summary>
    /// Boolean history records
    /// </summary>
    public DbSet<BooleanHistoryRecord> BooleanHistoryRecords { get; set; } = null!;
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        
        // Configure PostgreSQL enums
        modelBuilder.HasPostgresEnum<NodeType>();
        modelBuilder.HasPostgresEnum<HistoryRecordMode>();
        
        ConfigureNodeEntity(modelBuilder);
        ConfigureDataPathEntity(modelBuilder);
        ConfigureCustomNodeTemplateEntity(modelBuilder);
        ConfigureLookupTableEntity(modelBuilder);
        ConfigureHistoryRecordEntities(modelBuilder);
    }
    
    private static void ConfigureNodeEntity(ModelBuilder modelBuilder)
    {
        var entity = modelBuilder.Entity<Node>();
        
        // Primary key
        entity.HasKey(e => e.Id);
        
        // Indexes
        entity.HasIndex(e => e.NodeId).IsUnique();
        entity.HasIndex(e => e.LastUpdated);
        entity.HasIndex(e => new { e.NodeType, e.IsEnabled });
        
        // Properties
        entity.Property(e => e.NodeId).HasMaxLength(100).IsRequired();
        entity.Property(e => e.Name).HasMaxLength(200).IsRequired();
        entity.Property(e => e.Description).HasMaxLength(500).HasDefaultValue(string.Empty);
        entity.Property(e => e.Unit).HasMaxLength(50).HasDefaultValue(string.Empty);
        entity.Property(e => e.CurrentValue).HasMaxLength(1000).HasDefaultValue(string.Empty);
        entity.Property(e => e.Scale).HasDefaultValue(1.0);
        entity.Property(e => e.DecimalPlaces).HasDefaultValue((byte)2);
        entity.Property(e => e.IsEnabled).HasDefaultValue(true);
        entity.Property(e => e.IsLogEnabled).HasDefaultValue(true);
        entity.Property(e => e.IsFailureNode).HasDefaultValue(false);
        entity.Property(e => e.DisplayRuntime).HasDefaultValue(true);
        entity.Property(e => e.IsGoodQuality).HasDefaultValue(true);
        entity.Property(e => e.EnableHistoryRecording).HasDefaultValue(false);
        entity.Property(e => e.CreatedAt).HasDefaultValueSql("NOW()");
        entity.Property(e => e.UpdatedAt).HasDefaultValueSql("NOW()");
        
        // Relationships
        entity.HasOne(e => e.DataPath)
              .WithMany(d => d.Nodes)
              .HasForeignKey(e => e.DataPathId)
              .OnDelete(DeleteBehavior.SetNull);
              
        entity.HasOne(e => e.CustomNodeTemplate)
              .WithMany(t => t.Nodes)
              .HasForeignKey(e => e.CustomNodeTemplateId)
              .OnDelete(DeleteBehavior.SetNull);
              
        entity.HasOne(e => e.LookupTable)
              .WithMany(l => l.Nodes)
              .HasForeignKey(e => e.LookupTableId)
              .OnDelete(DeleteBehavior.SetNull);
    }

    private static void ConfigureDataPathEntity(ModelBuilder modelBuilder)
    {
        var entity = modelBuilder.Entity<DataPath>();

        // Primary key
        entity.HasKey(e => e.Id);

        // Indexes
        entity.HasIndex(e => e.FullPath).IsUnique();
        entity.HasIndex(e => new { e.ParentId, e.Level });
        entity.HasIndex(e => new { e.Level, e.DisplayOrder });

        // Properties
        entity.Property(e => e.Name).HasMaxLength(100).IsRequired();
        entity.Property(e => e.FullPath).HasMaxLength(1000).IsRequired();
        entity.Property(e => e.Description).HasMaxLength(500).HasDefaultValue(string.Empty);
        entity.Property(e => e.IsActive).HasDefaultValue(true);
        entity.Property(e => e.DisplayOrder).HasDefaultValue(0);
        entity.Property(e => e.CreatedAt).HasDefaultValueSql("NOW()");
        entity.Property(e => e.UpdatedAt).HasDefaultValueSql("NOW()");

        // Self-referencing relationship
        entity.HasOne(e => e.Parent)
              .WithMany(e => e.Children)
              .HasForeignKey(e => e.ParentId)
              .OnDelete(DeleteBehavior.Restrict);
    }

    private static void ConfigureCustomNodeTemplateEntity(ModelBuilder modelBuilder)
    {
        var entity = modelBuilder.Entity<CustomNodeTemplate>();

        // Primary key
        entity.HasKey(e => e.Id);

        // Indexes
        entity.HasIndex(e => e.TemplateId).IsUnique();
        entity.HasIndex(e => new { e.Category, e.IsActive });

        // Properties
        entity.Property(e => e.TemplateId).HasMaxLength(100).IsRequired();
        entity.Property(e => e.Name).HasMaxLength(200).IsRequired();
        entity.Property(e => e.Description).HasMaxLength(500).HasDefaultValue(string.Empty);
        entity.Property(e => e.Category).HasMaxLength(100).HasDefaultValue(string.Empty);
        entity.Property(e => e.Version).HasMaxLength(20).HasDefaultValue("1.0");
        entity.Property(e => e.CreatedBy).HasMaxLength(100).HasDefaultValue(string.Empty);
        entity.Property(e => e.IsActive).HasDefaultValue(true);
        entity.Property(e => e.CreatedAt).HasDefaultValueSql("NOW()");
        entity.Property(e => e.UpdatedAt).HasDefaultValueSql("NOW()");

        // JSON columns
        entity.Property(e => e.TemplateDefinition)
              .HasColumnType("jsonb")
              .IsRequired();

        entity.Property(e => e.HistoryFieldsConfig)
              .HasColumnType("jsonb");
    }

    private static void ConfigureLookupTableEntity(ModelBuilder modelBuilder)
    {
        var entity = modelBuilder.Entity<LookupTable>();

        // Primary key
        entity.HasKey(e => e.Id);

        // Indexes
        entity.HasIndex(e => e.TableId).IsUnique();
        entity.HasIndex(e => e.IsActive);

        // Properties
        entity.Property(e => e.TableId).HasMaxLength(100).IsRequired();
        entity.Property(e => e.Name).HasMaxLength(200).IsRequired();
        entity.Property(e => e.Description).HasMaxLength(500).HasDefaultValue(string.Empty);
        entity.Property(e => e.DefaultText).HasMaxLength(200).HasDefaultValue(string.Empty);
        entity.Property(e => e.IsActive).HasDefaultValue(true);
        entity.Property(e => e.CreatedAt).HasDefaultValueSql("NOW()");
        entity.Property(e => e.UpdatedAt).HasDefaultValueSql("NOW()");

        // JSON column
        entity.Property(e => e.LookupMappings)
              .HasColumnType("jsonb")
              .IsRequired();
    }

    private static void ConfigureHistoryRecordEntities(ModelBuilder modelBuilder)
    {
        // Configure NumericHistoryRecord
        var numericEntity = modelBuilder.Entity<NumericHistoryRecord>();
        numericEntity.HasKey(e => new { e.NodeId, e.Timestamp });
        numericEntity.HasIndex(e => e.Timestamp);
        numericEntity.HasIndex(e => new { e.NodeId, e.Timestamp });

        numericEntity.HasOne(e => e.Node)
                     .WithMany()
                     .HasForeignKey(e => e.NodeId)
                     .OnDelete(DeleteBehavior.Cascade);

        // Configure BooleanHistoryRecord
        var booleanEntity = modelBuilder.Entity<BooleanHistoryRecord>();
        booleanEntity.HasKey(e => new { e.NodeId, e.Timestamp });
        booleanEntity.HasIndex(e => e.Timestamp);
        booleanEntity.HasIndex(e => new { e.NodeId, e.Timestamp });

        booleanEntity.HasOne(e => e.Node)
                     .WithMany()
                     .HasForeignKey(e => e.NodeId)
                     .OnDelete(DeleteBehavior.Cascade);

        // Configure TimescaleDB hypertables (will be done in migration)
        numericEntity.ToTable("numeric_history_records");
        booleanEntity.ToTable("boolean_history_records");
    }
}
