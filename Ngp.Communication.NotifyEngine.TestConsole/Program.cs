using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Ngp.Communication.NotifyEngine;
using Ngp.Communication.NotifyEngine.Enums;
using Ngp.Communication.NotifyEngine.Events;
using Ngp.Communication.NotifyEngine.Interfaces;
using Ngp.Shared.Interfaces;

namespace Ngp.Communication.NotifyEngine.TestConsole;

/// <summary>
/// Test console application for NotifyEngine
/// </summary>
class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("=== NotifyEngine Test Console ===");
        Console.WriteLine();

        // Create host builder with logging
        var hostBuilder = Host.CreateDefaultBuilder(args)
            .ConfigureServices((context, services) =>
            {
                // Add mock SIP message service
                services.AddSingleton<ISipMessageService, MockSipMessageService>();
            })
            .ConfigureLogging(logging =>
            {
                logging.ClearProviders();
                logging.AddConsole();
                logging.SetMinimumLevel(LogLevel.Information);
            });

        using var host = hostBuilder.Build();
        var loggerFactory = host.Services.GetRequiredService<ILoggerFactory>();
        var logger = loggerFactory.CreateLogger<Program>();
        var sipService = host.Services.GetRequiredService<ISipMessageService>();

        try
        {
            await host.StartAsync();

            // Run tests
            await RunNotifyEngineTestsAsync(loggerFactory, sipService, logger);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error running tests");
        }
        finally
        {
            await host.StopAsync();
        }

        Console.WriteLine("\nPress any key to exit...");
        Console.ReadKey();
    }

    /// <summary>
    /// Runs comprehensive NotifyEngine tests
    /// </summary>
    /// <param name="loggerFactory">Logger factory</param>
    /// <param name="sipService">SIP message service</param>
    /// <param name="logger">Logger</param>
    /// <returns>Task representing the async operation</returns>
    static async Task RunNotifyEngineTestsAsync(
        ILoggerFactory loggerFactory, 
        ISipMessageService sipService, 
        ILogger logger)
    {
        logger.LogInformation("Starting NotifyEngine tests");

        // Test 1: Email Provider Test
        await TestEmailProviderAsync(loggerFactory, logger);

        // Test 2: SMS Provider Test
        await TestSmsProviderAsync(loggerFactory, logger);

        // Test 3: LINE Provider Test
        await TestLineProviderAsync(loggerFactory, logger);

        // Test 4: SIP Message Provider Test
        await TestSipMessageProviderAsync(loggerFactory, sipService, logger);

        // Test 5: Multi-Channel Test
        await TestMultiChannelNotificationAsync(loggerFactory, sipService, logger);

        // Test 6: Statistics Test
        await TestStatisticsAsync(loggerFactory, sipService, logger);

        logger.LogInformation("All NotifyEngine tests completed");
    }

    /// <summary>
    /// Tests email provider functionality
    /// </summary>
    static async Task TestEmailProviderAsync(ILoggerFactory loggerFactory, ILogger logger)
    {
        logger.LogInformation("=== Testing Email Provider ===");

        try
        {
            var engine = NotifyEngineFactory.Create(loggerFactory)
                .WithEmail(
                    smtpHost: "smtp.gmail.com",
                    smtpPort: 587,
                    username: "<EMAIL>",
                    password: "test-password",
                    fromEmail: "<EMAIL>",
                    fromName: "Test System")
                .WithTimeout(5000) // Short timeout for testing
                .Build();

            await engine.StartAsync();

            // Test sending email (will fail due to mock credentials, but tests the flow)
            try
            {
                var messageId = await engine.SendNotificationAsync(
                    NotificationChannel.Email,
                    "This is a test email from NotifyEngine",
                    new[] { "<EMAIL>" },
                    "Test Email Subject");

                logger.LogInformation("Email test message queued with ID: {MessageId}", messageId);
            }
            catch (Exception ex)
            {
                logger.LogWarning("Email test failed as expected (mock credentials): {Message}", ex.Message);
            }

            await engine.StopAsync();
            engine.Dispose();

            logger.LogInformation("Email provider test completed");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Email provider test failed");
        }
    }

    /// <summary>
    /// Tests SMS provider functionality
    /// </summary>
    static async Task TestSmsProviderAsync(ILoggerFactory loggerFactory, ILogger logger)
    {
        logger.LogInformation("=== Testing SMS Provider ===");

        try
        {
            var engine = NotifyEngineFactory.Create(loggerFactory)
                .WithSms(
                    apiUrl: "https://api.example-sms.com/send",
                    username: "test-user",
                    password: "test-password",
                    senderId: "TestSender")
                .WithTimeout(5000)
                .Build();

            await engine.StartAsync();

            // Test sending SMS (will fail due to mock API, but tests the flow)
            try
            {
                var messageId = await engine.SendNotificationAsync(
                    NotificationChannel.Sms,
                    "This is a test SMS from NotifyEngine",
                    new[] { "**********" });

                logger.LogInformation("SMS test message queued with ID: {MessageId}", messageId);
            }
            catch (Exception ex)
            {
                logger.LogWarning("SMS test failed as expected (mock API): {Message}", ex.Message);
            }

            await engine.StopAsync();
            engine.Dispose();

            logger.LogInformation("SMS provider test completed");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "SMS provider test failed");
        }
    }

    /// <summary>
    /// Tests LINE provider functionality
    /// </summary>
    static async Task TestLineProviderAsync(ILoggerFactory loggerFactory, ILogger logger)
    {
        logger.LogInformation("=== Testing LINE Provider ===");

        try
        {
            var engine = NotifyEngineFactory.Create(loggerFactory)
                .WithLine("test-channel-access-token")
                .WithTimeout(5000)
                .Build();

            await engine.StartAsync();

            // Test sending LINE message (will fail due to mock token, but tests the flow)
            try
            {
                var messageId = await engine.SendNotificationAsync(
                    NotificationChannel.Line,
                    "This is a test LINE message from NotifyEngine",
                    new[] { "U1234567890abcdef1234567890abcdef" });

                logger.LogInformation("LINE test message queued with ID: {MessageId}", messageId);
            }
            catch (Exception ex)
            {
                logger.LogWarning("LINE test failed as expected (mock token): {Message}", ex.Message);
            }

            await engine.StopAsync();
            engine.Dispose();

            logger.LogInformation("LINE provider test completed");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "LINE provider test failed");
        }
    }

    /// <summary>
    /// Tests SIP message provider functionality
    /// </summary>
    static async Task TestSipMessageProviderAsync(ILoggerFactory loggerFactory, ISipMessageService sipService, ILogger logger)
    {
        logger.LogInformation("=== Testing SIP Message Provider ===");

        try
        {
            var engine = NotifyEngineFactory.Create(loggerFactory)
                .WithSipMessage(sipService)
                .WithTimeout(5000)
                .Build();

            await engine.StartAsync();

            // Test sending SIP message
            var messageId = await engine.SendNotificationAsync(
                NotificationChannel.SipMessage,
                "This is a test SIP message from NotifyEngine",
                new[] { "1001" });

            logger.LogInformation("SIP message test queued with ID: {MessageId}", messageId);

            await engine.StopAsync();
            engine.Dispose();

            logger.LogInformation("SIP message provider test completed");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "SIP message provider test failed");
        }
    }

    /// <summary>
    /// Tests multi-channel notification functionality
    /// </summary>
    static async Task TestMultiChannelNotificationAsync(ILoggerFactory loggerFactory, ISipMessageService sipService, ILogger logger)
    {
        logger.LogInformation("=== Testing Multi-Channel Notification ===");

        try
        {
            var engine = NotifyEngineFactory.Create(loggerFactory)
                .WithEmail("smtp.gmail.com", 587, "<EMAIL>", "test-password", "<EMAIL>", "Test System")
                .WithSms("https://api.example-sms.com/send", "test-user", "test-password", "TestSender")
                .WithLine("test-channel-access-token")
                .WithSipMessage(sipService)
                .WithTimeout(5000)
                .Build();

            await engine.StartAsync();

            // Test sending to multiple channels
            var recipients = new Dictionary<NotificationChannel, List<string>>
            {
                { NotificationChannel.Email, new List<string> { "<EMAIL>" } },
                { NotificationChannel.Sms, new List<string> { "**********" } },
                { NotificationChannel.SipMessage, new List<string> { "1001" } }
            };

            try
            {
                var messageIds = await engine.SendNotificationAsync(
                    "This is a multi-channel test notification",
                    recipients,
                    "Multi-Channel Test");

                logger.LogInformation("Multi-channel test queued {MessageCount} messages: {MessageIds}",
                    messageIds.Count, string.Join(", ", messageIds));
            }
            catch (Exception ex)
            {
                logger.LogWarning("Multi-channel test partially failed (expected with mock services): {Message}", ex.Message);
            }

            await engine.StopAsync();
            engine.Dispose();

            logger.LogInformation("Multi-channel notification test completed");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Multi-channel notification test failed");
        }
    }

    /// <summary>
    /// Tests statistics functionality
    /// </summary>
    static async Task TestStatisticsAsync(ILoggerFactory loggerFactory, ISipMessageService sipService, ILogger logger)
    {
        logger.LogInformation("=== Testing Statistics ===");

        try
        {
            var engine = NotifyEngineFactory.Create(loggerFactory)
                .WithSipMessage(sipService)
                .WithTimeout(5000)
                .Build();

            await engine.StartAsync();

            // Send a test message
            await engine.SendNotificationAsync(
                NotificationChannel.SipMessage,
                "Statistics test message",
                new[] { "1001" });

            // Get statistics
            var stats = engine.GetStatistics();
            logger.LogInformation("Statistics - Total: {Total}, Delivered: {Delivered}, Failed: {Failed}, Pending: {Pending}",
                stats.TotalMessages, stats.DeliveredMessages, stats.FailedMessages, stats.PendingMessages);

            // Get messages
            var messages = engine.GetMessages();
            logger.LogInformation("Found {MessageCount} messages in history", messages.Count);

            await engine.StopAsync();
            engine.Dispose();

            logger.LogInformation("Statistics test completed");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Statistics test failed");
        }
    }
}

/// <summary>
/// Mock SIP message service for testing
/// </summary>
public class MockSipMessageService : ISipMessageService
{
    private readonly ILogger<MockSipMessageService> _logger;

    public bool IsAvailable => true;

    public event EventHandler<SipServiceAvailabilityChangedEventArgs>? AvailabilityChanged;

    public MockSipMessageService()
    {
        _logger = Microsoft.Extensions.Logging.Abstractions.NullLogger<MockSipMessageService>.Instance;
    }

    public Task<string> SendMessageAsync(string toExtension, string content, string contentType = "text/plain", CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Mock SIP: Sending message to {Extension}: {Content}", toExtension, content);
        
        // Simulate successful send
        var messageId = Guid.NewGuid().ToString();
        return Task.FromResult(messageId);
    }
}
