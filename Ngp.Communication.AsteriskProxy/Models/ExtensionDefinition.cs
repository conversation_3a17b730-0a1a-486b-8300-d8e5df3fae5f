namespace Ngp.Communication.AsteriskProxy.Models;

/// <summary>
/// Definition of an extension to monitor
/// </summary>
public class ExtensionDefinition
{
    /// <summary>
    /// Gets or sets the extension number
    /// </summary>
    public string Extension { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the context for the extension
    /// </summary>
    public string Context { get; set; } = "default";

    /// <summary>
    /// Gets or sets a custom identifier for this extension
    /// </summary>
    public string? Tag { get; set; }

    /// <summary>
    /// Gets or sets a description for this extension
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Validates the extension definition
    /// </summary>
    /// <returns>True if valid, false otherwise</returns>
    public bool IsValid()
    {
        if (string.IsNullOrWhiteSpace(Extension))
            return false;

        if (string.IsNullOrWhiteSpace(Context))
            return false;

        return true;
    }

    /// <summary>
    /// Gets the unique key for this extension
    /// </summary>
    /// <returns>Unique key combining context and extension</returns>
    public string GetKey()
    {
        return $"{Context}:{Extension}";
    }

    /// <summary>
    /// Returns a string representation of the extension definition
    /// </summary>
    /// <returns>String representation</returns>
    public override string ToString()
    {
        return $"{Extension}@{Context}";
    }
}
