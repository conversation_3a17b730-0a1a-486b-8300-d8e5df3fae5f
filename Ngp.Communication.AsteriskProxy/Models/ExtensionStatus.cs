using Ngp.Communication.AsteriskProxy.Enums;

namespace Ngp.Communication.AsteriskProxy.Models;

/// <summary>
/// Current status of an extension
/// </summary>
public class ExtensionStatus
{
    /// <summary>
    /// Gets or sets the extension number
    /// </summary>
    public string Extension { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the context for the extension
    /// </summary>
    public string Context { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the current extension state
    /// </summary>
    public ExtensionState State { get; set; } = ExtensionState.Unknown;

    /// <summary>
    /// Gets or sets the hint associated with the extension
    /// </summary>
    public string? Hint { get; set; }

    /// <summary>
    /// Gets or sets the status text description
    /// </summary>
    public string? StatusText { get; set; }

    /// <summary>
    /// Gets or sets the timestamp when this status was last updated
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Gets or sets whether the extension is currently connected
    /// </summary>
    public bool IsConnected { get; set; } = false;

    /// <summary>
    /// Gets or sets whether the extension is currently in a call
    /// </summary>
    public bool IsInCall { get; set; } = false;

    /// <summary>
    /// Gets or sets the number of active channels for this extension
    /// </summary>
    public int ActiveChannels { get; set; } = 0;

    /// <summary>
    /// Gets the unique key for this extension
    /// </summary>
    /// <returns>Unique key combining context and extension</returns>
    public string GetKey()
    {
        return $"{Context}:{Extension}";
    }

    /// <summary>
    /// Creates a copy of this extension status
    /// </summary>
    /// <returns>A new ExtensionStatus instance with the same values</returns>
    public ExtensionStatus Clone()
    {
        return new ExtensionStatus
        {
            Extension = Extension,
            Context = Context,
            State = State,
            Hint = Hint,
            StatusText = StatusText,
            LastUpdated = LastUpdated,
            IsConnected = IsConnected,
            IsInCall = IsInCall,
            ActiveChannels = ActiveChannels
        };
    }

    /// <summary>
    /// Returns a string representation of the extension status
    /// </summary>
    /// <returns>String representation</returns>
    public override string ToString()
    {
        return $"{Extension}@{Context}: {State} (Connected: {IsConnected}, InCall: {IsInCall}, Channels: {ActiveChannels})";
    }
}
