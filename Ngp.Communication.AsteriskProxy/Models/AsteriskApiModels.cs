using System.Text.Json.Serialization;
using System.Text.Json;
using System.Globalization;
using System.Text.RegularExpressions;

namespace Ngp.Communication.AsteriskProxy.Models;



/// <summary>
/// Asterisk endpoint response from REST API
/// </summary>
public class AsteriskEndpointResponse
{
    [JsonPropertyName("technology")]
    public string Technology { get; set; } = string.Empty;

    [JsonPropertyName("resource")]
    public string Resource { get; set; } = string.Empty;

    [JsonPropertyName("state")]
    public string State { get; set; } = string.Empty;

    [JsonPropertyName("channel_ids")]
    public string[] ChannelIds { get; set; } = Array.Empty<string>();

    /// <summary>
    /// Gets extension number from Resource
    /// </summary>
    /// <returns>Extension number or null if not found</returns>
    public string? GetExtensionNumber()
    {
        // Try to extract from Resource (e.g., "PJSIP/1001")
        if (!string.IsNullOrEmpty(Resource))
        {
            var match = Regex.Match(Resource, @"(\w+)/(\d+)");
            if (match.Success && match.Groups.Count > 2)
            {
                return match.Groups[2].Value;
            }
        }

        // Try to extract from Technology if it contains extension info
        if (!string.IsNullOrEmpty(Technology))
        {
            var match = Regex.Match(Technology, @"(\d{3,})");
            if (match.Success)
            {
                return match.Value;
            }
        }

        return null;
    }
}

/// <summary>
/// Asterisk channel response from REST API
/// </summary>
public class AsteriskChannelResponse
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;

    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;

    [JsonPropertyName("state")]
    public string State { get; set; } = string.Empty;

    [JsonPropertyName("caller")]
    public AsteriskCallerInfo? Caller { get; set; }

    [JsonPropertyName("connected")]
    public AsteriskCallerInfo? Connected { get; set; }

    [JsonPropertyName("accountcode")]
    public string? AccountCode { get; set; }

    [JsonPropertyName("dialplan")]
    public AsteriskDialplanInfo? Dialplan { get; set; }

    [JsonPropertyName("creationtime")]
    [JsonConverter(typeof(AsteriskNullableDateTimeConverter))]
    public DateTime? CreationTime { get; set; }

    /// <summary>
    /// Gets extension number from channel name
    /// </summary>
    /// <returns>Extension number or null if not found</returns>
    public string? GetExtensionFromName()
    {
        if (string.IsNullOrEmpty(Name))
            return null;

        // Common channel name patterns like "PJSIP/1001-********"
        var match = Regex.Match(Name, @"(\w+)/(\d+)-");
        if (match.Success && match.Groups.Count > 2)
        {
            return match.Groups[2].Value;
        }

        // Try other possible patterns like "SIP/1001"
        match = Regex.Match(Name, @"(\w+)/(\d+)");
        if (match.Success && match.Groups.Count > 2)
        {
            return match.Groups[2].Value;
        }

        return null;
    }

    /// <summary>
    /// Checks if channel is related to specified extension
    /// </summary>
    /// <param name="extension">Extension number to check</param>
    /// <returns>True if channel is related to extension</returns>
    public bool IsRelatedToExtension(string extension)
    {
        // Check channel name
        if (!string.IsNullOrEmpty(Name) && Name.Contains(extension))
            return true;

        // Check caller number
        if (Caller != null && !string.IsNullOrEmpty(Caller.Number) && Caller.Number == extension)
            return true;

        // Check connected number
        if (Connected != null && !string.IsNullOrEmpty(Connected.Number) && Connected.Number == extension)
            return true;

        // Use regex to extract extension from channel name
        var extractedExtension = GetExtensionFromName();
        if (!string.IsNullOrEmpty(extractedExtension) && extractedExtension == extension)
            return true;

        return false;
    }
}

/// <summary>
/// Asterisk caller information
/// </summary>
public class AsteriskCallerInfo
{
    [JsonPropertyName("name")]
    public string? Name { get; set; }

    [JsonPropertyName("number")]
    public string? Number { get; set; }
}

/// <summary>
/// Asterisk dialplan information
/// </summary>
public class AsteriskDialplanInfo
{
    [JsonPropertyName("context")]
    public string Context { get; set; } = string.Empty;

    [JsonPropertyName("exten")]
    public string Extension { get; set; } = string.Empty;

    [JsonPropertyName("priority")]
    public int Priority { get; set; }

    [JsonPropertyName("app_name")]
    public string? AppName { get; set; }

    [JsonPropertyName("app_data")]
    public string? AppData { get; set; }
}

/// <summary>
/// Asterisk system info response
/// </summary>
public class AsteriskSystemInfoResponse
{
    [JsonPropertyName("version")]
    public string Version { get; set; } = string.Empty;

    [JsonPropertyName("entity_id")]
    public string EntityId { get; set; } = string.Empty;

    [JsonPropertyName("system_name")]
    public string? SystemName { get; set; }
}

/// <summary>
/// Asterisk error response
/// </summary>
public class AsteriskErrorResponse
{
    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;

    [JsonPropertyName("error")]
    public string? Error { get; set; }

    [JsonPropertyName("error_type")]
    public string? ErrorType { get; set; }
}

/// <summary>
/// Asterisk configuration attribute
/// </summary>
public class AsteriskConfigAttribute
{
    [JsonPropertyName("attribute")]
    public string Attribute { get; set; } = string.Empty;

    [JsonPropertyName("value")]
    public string Value { get; set; } = string.Empty;
}

/// <summary>
/// Custom JSON converter for Asterisk DateTime format
/// </summary>
public class AsteriskDateTimeConverter : JsonConverter<DateTime>
{
    private static readonly string[] DateTimeFormats = new[]
    {
        "yyyy-MM-ddTHH:mm:ss.fffZ",     // ISO 8601 with milliseconds
        "yyyy-MM-ddTHH:mm:ssZ",         // ISO 8601 without milliseconds
        "yyyy-MM-ddTHH:mm:ss.fff",      // Without timezone
        "yyyy-MM-ddTHH:mm:ss",          // Basic format
        "yyyy-MM-dd HH:mm:ss.fff",      // Space separated with milliseconds
        "yyyy-MM-dd HH:mm:ss",          // Space separated
        "MM/dd/yyyy HH:mm:ss",          // US format
        "dd/MM/yyyy HH:mm:ss",          // European format
        "ddd MMM dd yyyy HH:mm:ss",     // Asterisk specific format
        "ddd MMM dd HH:mm:ss yyyy",     // Alternative Asterisk format
        "yyyy-MM-ddTHH:mm:sszzz"        // With timezone offset
    };

    public override DateTime Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        var value = reader.GetString();
        if (string.IsNullOrEmpty(value))
        {
            return DateTime.MinValue;
        }

        // Try to parse as Unix timestamp first (common in Asterisk)
        if (double.TryParse(value, NumberStyles.Any, CultureInfo.InvariantCulture, out var unixTimestamp))
        {
            try
            {
                // Handle both seconds and milliseconds timestamps
                if (unixTimestamp > 1_000_000_000_000) // Milliseconds timestamp
                {
                    return DateTimeOffset.FromUnixTimeMilliseconds((long)unixTimestamp).DateTime;
                }
                else // Seconds timestamp
                {
                    return DateTimeOffset.FromUnixTimeSeconds((long)unixTimestamp).DateTime;
                }
            }
            catch
            {
                // If Unix timestamp parsing fails, continue with string parsing
            }
        }

        // Try various DateTime formats
        foreach (var format in DateTimeFormats)
        {
            if (DateTime.TryParseExact(value, format, CultureInfo.InvariantCulture, DateTimeStyles.None, out var result))
            {
                return result;
            }
        }

        // Try standard DateTime parsing as fallback with different culture settings
        if (DateTime.TryParse(value, CultureInfo.InvariantCulture, DateTimeStyles.AdjustToUniversal, out var fallbackResult))
        {
            return fallbackResult;
        }

        // Final fallback - try with current culture
        if (DateTime.TryParse(value, out var cultureResult))
        {
            return cultureResult;
        }

        // If all parsing attempts fail, return current time
        return DateTime.UtcNow;
    }

    public override void Write(Utf8JsonWriter writer, DateTime value, JsonSerializerOptions options)
    {
        writer.WriteStringValue(value.ToString("yyyy-MM-ddTHH:mm:ss.fffZ", CultureInfo.InvariantCulture));
    }
}

/// <summary>
/// Custom JSON converter for nullable Asterisk DateTime format
/// </summary>
public class AsteriskNullableDateTimeConverter : JsonConverter<DateTime?>
{
    private readonly AsteriskDateTimeConverter _innerConverter = new();

    public override DateTime? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        if (reader.TokenType == JsonTokenType.Null)
        {
            return null;
        }

        try
        {
            return _innerConverter.Read(ref reader, typeof(DateTime), options);
        }
        catch
        {
            return null;
        }
    }

    public override void Write(Utf8JsonWriter writer, DateTime? value, JsonSerializerOptions options)
    {
        if (value.HasValue)
        {
            _innerConverter.Write(writer, value.Value, options);
        }
        else
        {
            writer.WriteNullValue();
        }
    }
}
