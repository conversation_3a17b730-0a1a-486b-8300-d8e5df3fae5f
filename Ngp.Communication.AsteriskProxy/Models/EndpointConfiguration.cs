using Ngp.Communication.AsteriskProxy.Enums;

namespace Ngp.Communication.AsteriskProxy.Models;

/// <summary>
/// Configuration for an Asterisk REST API endpoint
/// </summary>
public class EndpointConfiguration
{
    /// <summary>
    /// Gets or sets the endpoint identifier
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the base URL for the Asterisk REST API
    /// </summary>
    public string BaseUrl { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the username for authentication
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the password for authentication
    /// </summary>
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets whether to verify SSL certificates
    /// </summary>
    public bool VerifySslCertificate { get; set; } = true;

    /// <summary>
    /// Gets or sets the API version to use
    /// </summary>
    public AsteriskApiVersion ApiVersion { get; set; } = AsteriskApiVersion.Version22;

    /// <summary>
    /// Gets or sets the connection timeout in milliseconds
    /// </summary>
    public int ConnectionTimeoutMs { get; set; } = 5000;

    /// <summary>
    /// Gets or sets the request timeout in milliseconds
    /// </summary>
    public int RequestTimeoutMs { get; set; } = 10000;

    /// <summary>
    /// Gets or sets the polling interval in milliseconds
    /// </summary>
    public int PollingIntervalMs { get; set; } = 1000;

    /// <summary>
    /// Gets or sets the maximum retry attempts
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// Gets or sets the retry delay in milliseconds
    /// </summary>
    public int RetryDelayMs { get; set; } = 1000;



    /// <summary>
    /// Validates the endpoint configuration
    /// </summary>
    /// <returns>True if valid, false otherwise</returns>
    public bool IsValid()
    {
        if (string.IsNullOrWhiteSpace(Id))
            return false;

        if (string.IsNullOrWhiteSpace(BaseUrl))
            return false;

        if (string.IsNullOrWhiteSpace(Username))
            return false;

        if (string.IsNullOrWhiteSpace(Password))
            return false;

        if (ConnectionTimeoutMs <= 0)
            return false;

        if (RequestTimeoutMs <= 0)
            return false;

        if (PollingIntervalMs <= 0)
            return false;

        if (MaxRetryAttempts < 0)
            return false;

        if (RetryDelayMs < 0)
            return false;

        return true;
    }
}
