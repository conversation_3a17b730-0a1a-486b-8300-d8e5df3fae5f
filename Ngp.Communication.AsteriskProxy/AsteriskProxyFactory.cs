using Microsoft.Extensions.Logging;
using Ngp.Communication.AsteriskProxy.Builders;
using Ngp.Communication.AsteriskProxy.Interfaces;

namespace Ngp.Communication.AsteriskProxy;

/// <summary>
/// Factory for creating Asterisk proxy instances
/// </summary>
public static class AsteriskProxyFactory
{
    /// <summary>
    /// Creates a new Asterisk proxy builder for the specified endpoint
    /// </summary>
    /// <param name="endpointId">Endpoint identifier</param>
    /// <param name="baseUrl">Base URL for the Asterisk REST API</param>
    /// <param name="username">Username for authentication</param>
    /// <param name="password">Password for authentication</param>
    /// <param name="loggerFactory">Optional logger factory</param>
    /// <returns>Configured Asterisk proxy builder</returns>
    public static IAsteriskProxyBuilder CreateForEndpoint(
        string endpointId,
        string baseUrl,
        string username,
        string password,
        ILoggerFactory? loggerFactory = null)
    {
        var builder = new AsteriskProxyBuilder()
            .WithId(endpointId)
            .WithEndpoint(baseUrl, username, password);

        if (loggerFactory != null)
        {
            builder.WithLogger(loggerFactory);
        }

        return builder;
    }

    /// <summary>
    /// Creates a new Asterisk proxy builder with default settings
    /// </summary>
    /// <param name="loggerFactory">Optional logger factory</param>
    /// <returns>New Asterisk proxy builder</returns>
    public static IAsteriskProxyBuilder Create(ILoggerFactory? loggerFactory = null)
    {
        var builder = new AsteriskProxyBuilder();

        if (loggerFactory != null)
        {
            builder.WithLogger(loggerFactory);
        }

        return builder;
    }
}
