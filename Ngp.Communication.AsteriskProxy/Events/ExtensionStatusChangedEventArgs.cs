using Ngp.Communication.AsteriskProxy.Models;

namespace Ngp.Communication.AsteriskProxy.Events;

/// <summary>
/// Event arguments for extension status changes
/// </summary>
public class ExtensionStatusChangedEventArgs : EventArgs
{
    /// <summary>
    /// Gets the endpoint identifier
    /// </summary>
    public string EndpointId { get; }

    /// <summary>
    /// Gets the extension number
    /// </summary>
    public string Extension { get; }

    /// <summary>
    /// Gets the context for the extension
    /// </summary>
    public string Context { get; }

    /// <summary>
    /// Gets the previous extension status
    /// </summary>
    public ExtensionStatus? PreviousStatus { get; }

    /// <summary>
    /// Gets the current extension status
    /// </summary>
    public ExtensionStatus CurrentStatus { get; }

    /// <summary>
    /// Gets the timestamp when the status change occurred
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// Gets whether this is the first status update for this extension
    /// </summary>
    public bool IsInitialStatus { get; }

    /// <summary>
    /// Initializes a new instance of the ExtensionStatusChangedEventArgs class
    /// </summary>
    /// <param name="endpointId">The endpoint identifier</param>
    /// <param name="extension">The extension number</param>
    /// <param name="context">The context for the extension</param>
    /// <param name="previousStatus">The previous extension status</param>
    /// <param name="currentStatus">The current extension status</param>
    /// <param name="isInitialStatus">Whether this is the first status update</param>
    public ExtensionStatusChangedEventArgs(
        string endpointId,
        string extension,
        string context,
        ExtensionStatus? previousStatus,
        ExtensionStatus currentStatus,
        bool isInitialStatus = false)
    {
        EndpointId = endpointId;
        Extension = extension;
        Context = context;
        PreviousStatus = previousStatus;
        CurrentStatus = currentStatus;
        Timestamp = DateTime.UtcNow;
        IsInitialStatus = isInitialStatus;
    }
}
