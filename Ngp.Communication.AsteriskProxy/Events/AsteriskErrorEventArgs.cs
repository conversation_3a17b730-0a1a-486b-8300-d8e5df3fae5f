namespace Ngp.Communication.AsteriskProxy.Events;

/// <summary>
/// Event arguments for Asterisk API errors
/// </summary>
public class AsteriskErrorEventArgs : EventArgs
{
    /// <summary>
    /// Gets the endpoint identifier
    /// </summary>
    public string EndpointId { get; }

    /// <summary>
    /// Gets the error message
    /// </summary>
    public string ErrorMessage { get; }

    /// <summary>
    /// Gets the HTTP status code if applicable
    /// </summary>
    public int? HttpStatusCode { get; }

    /// <summary>
    /// Gets the exception that caused the error
    /// </summary>
    public Exception? Exception { get; }

    /// <summary>
    /// Gets the timestamp when the error occurred
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// Gets the request URL that caused the error
    /// </summary>
    public string? RequestUrl { get; }

    /// <summary>
    /// Gets the response content if available
    /// </summary>
    public string? ResponseContent { get; }

    /// <summary>
    /// Initializes a new instance of the AsteriskErrorEventArgs class
    /// </summary>
    /// <param name="endpointId">The endpoint identifier</param>
    /// <param name="errorMessage">The error message</param>
    /// <param name="httpStatusCode">Optional HTTP status code</param>
    /// <param name="exception">Optional exception</param>
    /// <param name="requestUrl">Optional request URL</param>
    /// <param name="responseContent">Optional response content</param>
    public AsteriskErrorEventArgs(
        string endpointId,
        string errorMessage,
        int? httpStatusCode = null,
        Exception? exception = null,
        string? requestUrl = null,
        string? responseContent = null)
    {
        EndpointId = endpointId;
        ErrorMessage = errorMessage;
        HttpStatusCode = httpStatusCode;
        Exception = exception;
        Timestamp = DateTime.UtcNow;
        RequestUrl = requestUrl;
        ResponseContent = responseContent;
    }
}
