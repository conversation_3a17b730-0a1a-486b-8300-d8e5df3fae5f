using Ngp.Communication.AsteriskProxy.Enums;
using Ngp.Communication.AsteriskProxy.Events;
using Ngp.Communication.AsteriskProxy.Models;

namespace Ngp.Communication.AsteriskProxy.Interfaces;

/// <summary>
/// Interface for Asterisk REST API proxy functionality
/// </summary>
public interface IAsteriskProxy : IDisposable
{
    /// <summary>
    /// Event raised when a connection state changes
    /// </summary>
    event EventHandler<ConnectionStateChangedEventArgs>? ConnectionStateChanged;

    /// <summary>
    /// Event raised when an extension status changes
    /// </summary>
    event EventHandler<ExtensionStatusChangedEventArgs>? ExtensionStatusChanged;

    /// <summary>
    /// Event raised when an Asterisk API error occurs
    /// </summary>
    event EventHandler<AsteriskErrorEventArgs>? AsteriskError;

    /// <summary>
    /// Gets the current connection state
    /// </summary>
    ConnectionState ConnectionState { get; }

    /// <summary>
    /// Gets the endpoint configuration
    /// </summary>
    EndpointConfiguration Configuration { get; }

    /// <summary>
    /// Gets the list of monitored extensions
    /// </summary>
    IReadOnlyList<ExtensionDefinition> MonitoredExtensions { get; }

    /// <summary>
    /// Starts the Asterisk proxy and begins monitoring extensions
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task StartAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Stops the Asterisk proxy and ceases monitoring
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task StopAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the current status of a specific extension
    /// </summary>
    /// <param name="extension">Extension number</param>
    /// <param name="context">Extension context</param>
    /// <returns>Current extension status, or null if not found</returns>
    ExtensionStatus? GetExtensionStatus(string extension, string context = "default");

    /// <summary>
    /// Gets the current status of all monitored extensions
    /// </summary>
    /// <returns>Dictionary of extension statuses keyed by extension key</returns>
    IReadOnlyDictionary<string, ExtensionStatus> GetAllExtensionStatuses();

    /// <summary>
    /// Adds an extension for monitoring
    /// </summary>
    /// <param name="extension">The extension definition</param>
    void AddExtension(ExtensionDefinition extension);

    /// <summary>
    /// Removes an extension from monitoring
    /// </summary>
    /// <param name="extension">Extension number</param>
    /// <param name="context">Extension context</param>
    /// <returns>True if removed, false if not found</returns>
    bool RemoveExtension(string extension, string context = "default");

    /// <summary>
    /// Forces a refresh of all extension statuses
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task RefreshAllExtensionsAsync(CancellationToken cancellationToken = default);
}
