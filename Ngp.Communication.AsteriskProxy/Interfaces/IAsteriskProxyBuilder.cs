using Microsoft.Extensions.Logging;
using Ngp.Communication.AsteriskProxy.Enums;
using Ngp.Communication.AsteriskProxy.Models;

namespace Ngp.Communication.AsteriskProxy.Interfaces;

/// <summary>
/// Builder interface for creating Asterisk proxy instances
/// </summary>
public interface IAsteriskProxyBuilder
{
    /// <summary>
    /// Sets the endpoint configuration
    /// </summary>
    /// <param name="baseUrl">Base URL for the Asterisk REST API</param>
    /// <param name="username">Username for authentication</param>
    /// <param name="password">Password for authentication</param>
    /// <returns>The builder instance</returns>
    IAsteriskProxyBuilder WithEndpoint(string baseUrl, string username, string password);

    /// <summary>
    /// Sets the endpoint identifier
    /// </summary>
    /// <param name="id">Endpoint identifier</param>
    /// <returns>The builder instance</returns>
    IAsteriskProxyBuilder WithId(string id);

    /// <summary>
    /// Sets whether to verify SSL certificates
    /// </summary>
    /// <param name="verify">Whether to verify SSL certificates</param>
    /// <returns>The builder instance</returns>
    IAsteriskProxyBuilder WithSslVerification(bool verify);

    /// <summary>
    /// Sets the API version to use
    /// </summary>
    /// <param name="version">API version</param>
    /// <returns>The builder instance</returns>
    IAsteriskProxyBuilder WithApiVersion(AsteriskApiVersion version);

    /// <summary>
    /// Sets timeout configurations
    /// </summary>
    /// <param name="connectionTimeoutMs">Connection timeout in milliseconds</param>
    /// <param name="requestTimeoutMs">Request timeout in milliseconds</param>
    /// <returns>The builder instance</returns>
    IAsteriskProxyBuilder WithTimeouts(int connectionTimeoutMs, int requestTimeoutMs);

    /// <summary>
    /// Sets polling configuration
    /// </summary>
    /// <param name="intervalMs">Polling interval in milliseconds</param>
    /// <returns>The builder instance</returns>
    IAsteriskProxyBuilder WithPollingInterval(int intervalMs);

    /// <summary>
    /// Sets retry policy
    /// </summary>
    /// <param name="maxAttempts">Maximum retry attempts</param>
    /// <param name="delayMs">Retry delay in milliseconds</param>
    /// <returns>The builder instance</returns>
    IAsteriskProxyBuilder WithRetryPolicy(int maxAttempts, int delayMs);



    /// <summary>
    /// Adds an extension for monitoring
    /// </summary>
    /// <param name="extension">Extension number</param>
    /// <param name="context">Extension context</param>
    /// <param name="tag">Optional tag</param>
    /// <param name="description">Optional description</param>
    /// <returns>The builder instance</returns>
    IAsteriskProxyBuilder AddExtension(
        string extension,
        string context = "default",
        string? tag = null,
        string? description = null);

    /// <summary>
    /// Adds multiple extensions for monitoring
    /// </summary>
    /// <param name="extensions">Extension definitions</param>
    /// <returns>The builder instance</returns>
    IAsteriskProxyBuilder AddExtensions(params ExtensionDefinition[] extensions);

    /// <summary>
    /// Adds a range of extensions for monitoring
    /// </summary>
    /// <param name="startExtension">Starting extension number</param>
    /// <param name="endExtension">Ending extension number</param>
    /// <param name="context">Extension context</param>
    /// <param name="tagPrefix">Optional tag prefix</param>
    /// <param name="description">Optional description</param>
    /// <returns>The builder instance</returns>
    IAsteriskProxyBuilder AddExtensionRange(
        int startExtension,
        int endExtension,
        string context = "default",
        string? tagPrefix = null,
        string? description = null);

    /// <summary>
    /// Sets the logger factory
    /// </summary>
    /// <param name="loggerFactory">Logger factory instance</param>
    /// <returns>The builder instance</returns>
    IAsteriskProxyBuilder WithLogger(ILoggerFactory loggerFactory);

    /// <summary>
    /// Builds the Asterisk proxy instance
    /// </summary>
    /// <returns>The configured Asterisk proxy</returns>
    IAsteriskProxy Build();
}
