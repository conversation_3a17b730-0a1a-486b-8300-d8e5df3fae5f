using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Ngp.Communication.AsteriskProxy.Api;
using Ngp.Communication.AsteriskProxy.Connection;
using Ngp.Communication.AsteriskProxy.Enums;
using Ngp.Communication.AsteriskProxy.Events;
using Ngp.Communication.AsteriskProxy.Interfaces;
using Ngp.Communication.AsteriskProxy.Models;
using Ngp.Communication.AsteriskProxy.Polling;
using Ngp.Communication.AsteriskProxy.Utilities;

namespace Ngp.Communication.AsteriskProxy;

/// <summary>
/// Main implementation of the Asterisk REST API proxy
/// </summary>
public class AsteriskProxy : IAsteriskProxy
{
    private readonly EndpointConfiguration _configuration;
    private readonly List<ExtensionDefinition> _extensions;
    private readonly ILogger<AsteriskProxy> _logger;

    private readonly ConnectionManager _connectionManager;
    private readonly AsteriskApiClient _apiClient;
    private readonly PollingEngine _pollingEngine;

    private bool _disposed = false;

    /// <summary>
    /// Event raised when a connection state changes
    /// </summary>
    public event EventHandler<ConnectionStateChangedEventArgs>? ConnectionStateChanged;

    /// <summary>
    /// Event raised when an extension status changes
    /// </summary>
    public event EventHandler<ExtensionStatusChangedEventArgs>? ExtensionStatusChanged;

    /// <summary>
    /// Event raised when an Asterisk API error occurs
    /// </summary>
    public event EventHandler<AsteriskErrorEventArgs>? AsteriskError;

    /// <summary>
    /// Gets the current connection state
    /// </summary>
    public ConnectionState ConnectionState => _connectionManager.State;

    /// <summary>
    /// Gets the endpoint configuration
    /// </summary>
    public EndpointConfiguration Configuration => _configuration;

    /// <summary>
    /// Gets the list of monitored extensions
    /// </summary>
    public IReadOnlyList<ExtensionDefinition> MonitoredExtensions => _extensions.AsReadOnly();

    /// <summary>
    /// Initializes a new instance of the AsteriskProxy class
    /// </summary>
    /// <param name="configuration">Endpoint configuration</param>
    /// <param name="extensions">List of extensions to monitor</param>
    /// <param name="logger">Logger instance</param>
    public AsteriskProxy(
        EndpointConfiguration configuration,
        List<ExtensionDefinition> extensions,
        ILogger<AsteriskProxy>? logger = null)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _extensions = extensions?.ToList() ?? throw new ArgumentNullException(nameof(extensions));
        _logger = logger ?? NullLogger<AsteriskProxy>.Instance;

        if (!_configuration.IsValid())
        {
            throw new ArgumentException("Invalid endpoint configuration", nameof(configuration));
        }

        if (_extensions.Count == 0)
        {
            throw new ArgumentException("At least one extension must be provided", nameof(extensions));
        }

        // Initialize components with proper logging
        _connectionManager = new ConnectionManager(_configuration,
            logger != null ? new LoggerAdapter<ConnectionManager>(logger) : NullLogger<ConnectionManager>.Instance);

        _apiClient = new AsteriskApiClient(_connectionManager,
            logger != null ? new LoggerAdapter<AsteriskApiClient>(logger) : NullLogger<AsteriskApiClient>.Instance);

        _pollingEngine = new PollingEngine(_configuration, _apiClient,
            logger != null ? new LoggerAdapter<PollingEngine>(logger) : NullLogger<PollingEngine>.Instance);

        // Wire up events
        _connectionManager.ConnectionStateChanged += OnConnectionStateChanged;
        _pollingEngine.ExtensionStatusChanged += OnExtensionStatusChanged;
        _pollingEngine.AsteriskError += OnAsteriskError;

        // Set extensions for polling
        _logger.LogInformation("Setting {ExtensionCount} extensions for monitoring", _extensions.Count);
        try
        {
            _pollingEngine.SetExtensions(_extensions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting extensions for monitoring");
            throw;
        }

        _logger.LogInformation("AsteriskProxy created for endpoint {EndpointId} with {ExtensionCount} extensions",
            _configuration.Id, _extensions.Count);
    }

    /// <summary>
    /// Starts the Asterisk proxy and begins monitoring extensions
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AsteriskProxy));

        _logger.LogInformation("Starting AsteriskProxy for endpoint {EndpointId}", _configuration.Id);

        try
        {
            // Connect to the Asterisk REST API
            var connected = await _connectionManager.ConnectAsync(cancellationToken);
            if (!connected)
            {
                throw new InvalidOperationException($"Failed to connect to Asterisk REST API at {_configuration.BaseUrl}");
            }

            // Start polling engine
            _logger.LogInformation("Starting polling engine for endpoint {EndpointId}", _configuration.Id);
            await _pollingEngine.StartAsync(cancellationToken);
            _logger.LogInformation("Polling engine started successfully for endpoint {EndpointId}", _configuration.Id);

            _logger.LogInformation("AsteriskProxy started successfully for endpoint {EndpointId}", _configuration.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start AsteriskProxy for endpoint {EndpointId}: {ErrorMessage}",
                _configuration.Id, ex.Message);
            throw;
        }
    }

    /// <summary>
    /// Stops the Asterisk proxy and ceases monitoring
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed) return;

        _logger.LogInformation("Stopping AsteriskProxy for endpoint {EndpointId}", _configuration.Id);

        try
        {
            // Stop polling engine first
            await _pollingEngine.StopAsync(cancellationToken);
            _logger.LogDebug("Polling engine stopped for endpoint {EndpointId}", _configuration.Id);

            // Disconnect from API
            await _connectionManager.DisconnectAsync(cancellationToken);
            _logger.LogDebug("Disconnected from endpoint {EndpointId}", _configuration.Id);

            _logger.LogInformation("AsteriskProxy stopped successfully for endpoint {EndpointId}", _configuration.Id);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error while stopping AsteriskProxy for endpoint {EndpointId}: {ErrorMessage}",
                _configuration.Id, ex.Message);
        }
    }

    /// <summary>
    /// Gets the current status of a specific extension
    /// </summary>
    /// <param name="extension">Extension number</param>
    /// <param name="context">Extension context</param>
    /// <returns>Current extension status, or null if not found</returns>
    public ExtensionStatus? GetExtensionStatus(string extension, string context = "default")
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AsteriskProxy));
        return _pollingEngine.GetExtensionStatus(extension, context);
    }

    /// <summary>
    /// Gets the current status of all monitored extensions
    /// </summary>
    /// <returns>Dictionary of extension statuses keyed by extension key</returns>
    public IReadOnlyDictionary<string, ExtensionStatus> GetAllExtensionStatuses()
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AsteriskProxy));
        return _pollingEngine.GetAllExtensionStatuses();
    }

    /// <summary>
    /// Adds an extension for monitoring
    /// </summary>
    /// <param name="extension">The extension definition</param>
    public void AddExtension(ExtensionDefinition extension)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AsteriskProxy));
        if (extension == null) throw new ArgumentNullException(nameof(extension));
        if (!extension.IsValid()) throw new ArgumentException("Invalid extension definition", nameof(extension));

        // Check for duplicates
        var key = extension.GetKey();
        if (_extensions.Any(e => e.GetKey() == key))
        {
            throw new ArgumentException($"Extension {extension} is already being monitored", nameof(extension));
        }

        _extensions.Add(extension);
        _pollingEngine.SetExtensions(_extensions);

        _logger.LogDebug("Added extension for monitoring: {Extension}", extension);
    }

    /// <summary>
    /// Removes an extension from monitoring
    /// </summary>
    /// <param name="extension">Extension number</param>
    /// <param name="context">Extension context</param>
    /// <returns>True if removed, false if not found</returns>
    public bool RemoveExtension(string extension, string context = "default")
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AsteriskProxy));
        if (string.IsNullOrWhiteSpace(extension))
            throw new ArgumentException("Extension cannot be null or empty", nameof(extension));

        var key = $"{context}:{extension}";
        var extensionToRemove = _extensions.FirstOrDefault(e => e.GetKey() == key);

        if (extensionToRemove == null)
        {
            return false;
        }

        _extensions.Remove(extensionToRemove);
        _pollingEngine.SetExtensions(_extensions);

        _logger.LogDebug("Removed extension from monitoring: {Extension}@{Context}", extension, context);
        return true;
    }

    /// <summary>
    /// Forces a refresh of all extension statuses
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task RefreshAllExtensionsAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AsteriskProxy));

        _logger.LogDebug("Refreshing all extension statuses for endpoint {EndpointId}", _configuration.Id);
        await _pollingEngine.RefreshAllExtensionsAsync(cancellationToken);
        _logger.LogDebug("Completed refresh of all extension statuses for endpoint {EndpointId}", _configuration.Id);
    }

    /// <summary>
    /// Handles connection state change events
    /// </summary>
    private void OnConnectionStateChanged(object? sender, ConnectionStateChangedEventArgs e)
    {
        _logger.LogDebug("Connection state changed from {PreviousState} to {CurrentState} for {EndpointId}",
            e.PreviousState, e.CurrentState, e.EndpointId);

        ConnectionStateChanged?.Invoke(this, e);
    }

    /// <summary>
    /// Handles extension status change events
    /// </summary>
    private void OnExtensionStatusChanged(object? sender, ExtensionStatusChangedEventArgs e)
    {
        // Only log if not initial status to avoid excessive logging
        if (!e.IsInitialStatus)
        {
            _logger.LogInformation("Extension status changed: {Extension}@{Context} - {PreviousState} -> {CurrentState}",
                e.Extension, e.Context, e.PreviousStatus?.State, e.CurrentStatus.State);
        }

        ExtensionStatusChanged?.Invoke(this, e);
    }

    /// <summary>
    /// Handles Asterisk API error events
    /// </summary>
    private void OnAsteriskError(object? sender, AsteriskErrorEventArgs e)
    {
        _logger.LogError("Asterisk API error for {EndpointId}: {ErrorMessage}", e.EndpointId, e.ErrorMessage);
        AsteriskError?.Invoke(this, e);
    }

    /// <summary>
    /// Disposes the Asterisk proxy
    /// </summary>
    public void Dispose()
    {
        if (_disposed) return;

        _disposed = true;

        try
        {
            StopAsync().GetAwaiter().GetResult();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error during disposal of AsteriskProxy for {EndpointId}", _configuration.Id);
        }

        _pollingEngine.Dispose();
        _apiClient.Dispose();
        _connectionManager.Dispose();
    }
}
