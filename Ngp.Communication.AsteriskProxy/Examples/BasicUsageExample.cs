using Microsoft.Extensions.Logging;
using Ngp.Communication.AsteriskProxy.Enums;
using Ngp.Communication.AsteriskProxy.Events;

namespace Ngp.Communication.AsteriskProxy.Examples;

/// <summary>
/// Basic usage example for AsteriskProxy
/// </summary>
public class BasicUsageExample
{
    private readonly ILogger<BasicUsageExample> _logger;

    public BasicUsageExample(ILogger<BasicUsageExample> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Demonstrates basic usage of AsteriskProxy
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task RunExampleAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting AsteriskProxy basic usage example");

        // Create Asterisk proxy with fluent API
        var proxy = AsteriskProxyFactory
            .CreateForEndpoint(
                "asterisk-server-1",
                "http://192.168.1.100:8088",
                "admin",
                "secret",
                LoggerFactory.Create(builder => builder.AddConsole()))
            .WithSslVerification(false)
            .WithApiVersion(AsteriskApiVersion.Version22)
            .WithTimeouts(5000, 10000)
            .WithPollingInterval(1000)
            .WithRetryPolicy(3, 2000)
            // Add individual extensions
            .AddExtension("1001", "internal", "Reception", "Reception desk")
            .AddExtension("1002", "internal", "Manager", "Manager office")
            .AddExtension("1003", "internal", "Sales", "Sales department")
            // Add a range of extensions
            .AddExtensionRange(2000, 2010, "internal", "Employee", "Employee extensions")
            .Build();

        // Wire up events
        proxy.ConnectionStateChanged += OnConnectionStateChanged;
        proxy.ExtensionStatusChanged += OnExtensionStatusChanged;
        proxy.AsteriskError += OnAsteriskError;

        try
        {
            // Start monitoring
            _logger.LogInformation("Starting Asterisk proxy...");
            await proxy.StartAsync(cancellationToken);

            // Wait for initial status updates
            await Task.Delay(5000, cancellationToken);

            // Get current status of all extensions
            var allStatuses = proxy.GetAllExtensionStatuses();
            _logger.LogInformation("Current status of {Count} extensions:", allStatuses.Count);
            foreach (var (key, status) in allStatuses)
            {
                _logger.LogInformation("  {Key}: {Status}", key, status);
            }

            // Get specific extension status
            var receptionStatus = proxy.GetExtensionStatus("1001", "internal");
            if (receptionStatus != null)
            {
                _logger.LogInformation("Reception status: {Status}", receptionStatus);
            }

            // Add a new extension dynamically
            proxy.AddExtension(new Models.ExtensionDefinition
            {
                Extension = "9999",
                Context = "internal",
                Tag = "Emergency",
                Description = "Emergency hotline"
            });

            // Force refresh of all extensions
            await proxy.RefreshAllExtensionsAsync(cancellationToken);

            // Run for a while to observe status changes
            _logger.LogInformation("Monitoring extensions for 30 seconds...");
            await Task.Delay(30000, cancellationToken);

            // Remove an extension
            var removed = proxy.RemoveExtension("9999", "internal");
            _logger.LogInformation("Removed emergency extension: {Removed}", removed);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Example cancelled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in example: {ErrorMessage}", ex.Message);
        }
        finally
        {
            // Stop monitoring
            _logger.LogInformation("Stopping Asterisk proxy...");
            await proxy.StopAsync(cancellationToken);
            proxy.Dispose();
            _logger.LogInformation("AsteriskProxy example completed");
        }
    }

    /// <summary>
    /// Handles connection state change events
    /// </summary>
    private void OnConnectionStateChanged(object? sender, ConnectionStateChangedEventArgs e)
    {
        _logger.LogInformation("Connection state changed: {EndpointId} - {PreviousState} -> {CurrentState}",
            e.EndpointId, e.PreviousState, e.CurrentState);

        if (!string.IsNullOrEmpty(e.ErrorMessage))
        {
            _logger.LogWarning("Connection error: {ErrorMessage}", e.ErrorMessage);
        }
    }

    /// <summary>
    /// Handles extension status change events
    /// </summary>
    private void OnExtensionStatusChanged(object? sender, ExtensionStatusChangedEventArgs e)
    {
        if (e.IsInitialStatus)
        {
            _logger.LogInformation("Initial status for {Extension}@{Context}: {Status}",
                e.Extension, e.Context, e.CurrentStatus);
        }
        else
        {
            _logger.LogInformation("Status changed for {Extension}@{Context}: {PreviousState} -> {CurrentState}",
                e.Extension, e.Context, e.PreviousStatus?.State, e.CurrentStatus.State);

            // Log specific changes
            if (e.PreviousStatus?.IsInCall != e.CurrentStatus.IsInCall)
            {
                var callStatus = e.CurrentStatus.IsInCall ? "started" : "ended";
                _logger.LogInformation("Call {CallStatus} for {Extension}@{Context}",
                    callStatus, e.Extension, e.Context);
            }

            if (e.PreviousStatus?.IsConnected != e.CurrentStatus.IsConnected)
            {
                var connectionStatus = e.CurrentStatus.IsConnected ? "connected" : "disconnected";
                _logger.LogInformation("Extension {Extension}@{Context} {ConnectionStatus}",
                    e.Extension, e.Context, connectionStatus);
            }
        }
    }

    /// <summary>
    /// Handles Asterisk API error events
    /// </summary>
    private void OnAsteriskError(object? sender, AsteriskErrorEventArgs e)
    {
        _logger.LogError("Asterisk API error for {EndpointId}: {ErrorMessage}",
            e.EndpointId, e.ErrorMessage);

        if (e.HttpStatusCode.HasValue)
        {
            _logger.LogError("HTTP Status Code: {StatusCode}", e.HttpStatusCode.Value);
        }

        if (e.Exception != null)
        {
            _logger.LogError(e.Exception, "Exception details");
        }
    }
}

/// <summary>
/// Console application entry point for running the example
/// </summary>
public class Program
{
    public static async Task Main(string[] args)
    {
        using var loggerFactory = LoggerFactory.Create(builder =>
            builder.AddConsole().SetMinimumLevel(LogLevel.Debug));

        var logger = loggerFactory.CreateLogger<BasicUsageExample>();
        var example = new BasicUsageExample(logger);

        using var cts = new CancellationTokenSource();

        // Handle Ctrl+C gracefully
        Console.CancelKeyPress += (_, e) =>
        {
            e.Cancel = true;
            cts.Cancel();
        };

        try
        {
            await example.RunExampleAsync(cts.Token);
        }
        catch (OperationCanceledException)
        {
            Console.WriteLine("Example cancelled by user");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Example failed: {ex.Message}");
        }

        Console.WriteLine("Press any key to exit...");
        Console.ReadKey();
    }
}
