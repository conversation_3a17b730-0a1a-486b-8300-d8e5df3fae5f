using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Ngp.Communication.AsteriskProxy.Enums;
using Ngp.Communication.AsteriskProxy.Interfaces;
using Ngp.Communication.AsteriskProxy.Models;

namespace Ngp.Communication.AsteriskProxy.Builders;

/// <summary>
/// Builder for creating Asterisk proxy instances
/// </summary>
public class AsteriskProxyBuilder : IAsteriskProxyBuilder
{
    private readonly EndpointConfiguration _configuration;
    private readonly List<ExtensionDefinition> _extensions;
    private ILoggerFactory? _loggerFactory;

    /// <summary>
    /// Initializes a new instance of the AsteriskProxyBuilder class
    /// </summary>
    public AsteriskProxyBuilder()
    {
        _configuration = new EndpointConfiguration();
        _extensions = new List<ExtensionDefinition>();
    }

    /// <summary>
    /// Sets the endpoint configuration
    /// </summary>
    /// <param name="baseUrl">Base URL for the Asterisk REST API</param>
    /// <param name="username">Username for authentication</param>
    /// <param name="password">Password for authentication</param>
    /// <returns>The builder instance</returns>
    public IAsteriskProxyBuilder WithEndpoint(string baseUrl, string username, string password)
    {
        if (string.IsNullOrWhiteSpace(baseUrl))
            throw new ArgumentException("Base URL cannot be null or empty", nameof(baseUrl));
        if (string.IsNullOrWhiteSpace(username))
            throw new ArgumentException("Username cannot be null or empty", nameof(username));
        if (string.IsNullOrWhiteSpace(password))
            throw new ArgumentException("Password cannot be null or empty", nameof(password));

        _configuration.BaseUrl = baseUrl.TrimEnd('/');
        _configuration.Username = username;
        _configuration.Password = password;

        // Generate default ID if not set
        if (string.IsNullOrEmpty(_configuration.Id))
        {
            var uri = new Uri(baseUrl);
            _configuration.Id = $"{uri.Host}:{uri.Port}";
        }

        return this;
    }

    /// <summary>
    /// Sets the endpoint identifier
    /// </summary>
    /// <param name="id">Endpoint identifier</param>
    /// <returns>The builder instance</returns>
    public IAsteriskProxyBuilder WithId(string id)
    {
        if (string.IsNullOrWhiteSpace(id))
            throw new ArgumentException("ID cannot be null or empty", nameof(id));

        _configuration.Id = id;
        return this;
    }

    /// <summary>
    /// Sets whether to verify SSL certificates
    /// </summary>
    /// <param name="verify">Whether to verify SSL certificates</param>
    /// <returns>The builder instance</returns>
    public IAsteriskProxyBuilder WithSslVerification(bool verify)
    {
        _configuration.VerifySslCertificate = verify;
        return this;
    }

    /// <summary>
    /// Sets the API version to use
    /// </summary>
    /// <param name="version">API version</param>
    /// <returns>The builder instance</returns>
    public IAsteriskProxyBuilder WithApiVersion(AsteriskApiVersion version)
    {
        _configuration.ApiVersion = version;
        return this;
    }

    /// <summary>
    /// Sets timeout configurations
    /// </summary>
    /// <param name="connectionTimeoutMs">Connection timeout in milliseconds</param>
    /// <param name="requestTimeoutMs">Request timeout in milliseconds</param>
    /// <returns>The builder instance</returns>
    public IAsteriskProxyBuilder WithTimeouts(int connectionTimeoutMs, int requestTimeoutMs)
    {
        if (connectionTimeoutMs <= 0)
            throw new ArgumentException("Connection timeout must be positive", nameof(connectionTimeoutMs));
        if (requestTimeoutMs <= 0)
            throw new ArgumentException("Request timeout must be positive", nameof(requestTimeoutMs));

        _configuration.ConnectionTimeoutMs = connectionTimeoutMs;
        _configuration.RequestTimeoutMs = requestTimeoutMs;
        return this;
    }

    /// <summary>
    /// Sets polling configuration
    /// </summary>
    /// <param name="intervalMs">Polling interval in milliseconds</param>
    /// <returns>The builder instance</returns>
    public IAsteriskProxyBuilder WithPollingInterval(int intervalMs)
    {
        if (intervalMs <= 0)
            throw new ArgumentException("Polling interval must be positive", nameof(intervalMs));

        _configuration.PollingIntervalMs = intervalMs;
        return this;
    }

    /// <summary>
    /// Sets retry policy
    /// </summary>
    /// <param name="maxAttempts">Maximum retry attempts</param>
    /// <param name="delayMs">Retry delay in milliseconds</param>
    /// <returns>The builder instance</returns>
    public IAsteriskProxyBuilder WithRetryPolicy(int maxAttempts, int delayMs)
    {
        if (maxAttempts < 0)
            throw new ArgumentException("Max attempts cannot be negative", nameof(maxAttempts));
        if (delayMs < 0)
            throw new ArgumentException("Retry delay cannot be negative", nameof(delayMs));

        _configuration.MaxRetryAttempts = maxAttempts;
        _configuration.RetryDelayMs = delayMs;
        return this;
    }



    /// <summary>
    /// Adds an extension for monitoring
    /// </summary>
    /// <param name="extension">Extension number</param>
    /// <param name="context">Extension context</param>
    /// <param name="tag">Optional tag</param>
    /// <param name="description">Optional description</param>
    /// <returns>The builder instance</returns>
    public IAsteriskProxyBuilder AddExtension(
        string extension,
        string context = "default",
        string? tag = null,
        string? description = null)
    {
        if (string.IsNullOrWhiteSpace(extension))
            throw new ArgumentException("Extension cannot be null or empty", nameof(extension));
        if (string.IsNullOrWhiteSpace(context))
            throw new ArgumentException("Context cannot be null or empty", nameof(context));

        var extensionDef = new ExtensionDefinition
        {
            Extension = extension,
            Context = context,
            Tag = tag,
            Description = description
        };

        if (!extensionDef.IsValid())
        {
            throw new ArgumentException($"Invalid extension definition: {extension}@{context}");
        }

        _extensions.Add(extensionDef);
        return this;
    }

    /// <summary>
    /// Adds multiple extensions for monitoring
    /// </summary>
    /// <param name="extensions">Extension definitions</param>
    /// <returns>The builder instance</returns>
    public IAsteriskProxyBuilder AddExtensions(params ExtensionDefinition[] extensions)
    {
        if (extensions == null)
            throw new ArgumentNullException(nameof(extensions));

        foreach (var extension in extensions)
        {
            if (!extension.IsValid())
            {
                throw new ArgumentException($"Invalid extension definition: {extension}");
            }
            _extensions.Add(extension);
        }

        return this;
    }

    /// <summary>
    /// Adds a range of extensions for monitoring
    /// </summary>
    /// <param name="startExtension">Starting extension number</param>
    /// <param name="endExtension">Ending extension number</param>
    /// <param name="context">Extension context</param>
    /// <param name="tagPrefix">Optional tag prefix</param>
    /// <param name="description">Optional description</param>
    /// <returns>The builder instance</returns>
    public IAsteriskProxyBuilder AddExtensionRange(
        int startExtension,
        int endExtension,
        string context = "default",
        string? tagPrefix = null,
        string? description = null)
    {
        if (startExtension < 0)
            throw new ArgumentException("Start extension cannot be negative", nameof(startExtension));
        if (endExtension < startExtension)
            throw new ArgumentException("End extension must be greater than or equal to start extension", nameof(endExtension));
        if (string.IsNullOrWhiteSpace(context))
            throw new ArgumentException("Context cannot be null or empty", nameof(context));

        for (int ext = startExtension; ext <= endExtension; ext++)
        {
            var tag = string.IsNullOrWhiteSpace(tagPrefix) ? null : $"{tagPrefix}_{ext}";
            AddExtension(ext.ToString(), context, tag, description);
        }

        return this;
    }

    /// <summary>
    /// Sets the logger factory
    /// </summary>
    /// <param name="loggerFactory">Logger factory instance</param>
    /// <returns>The builder instance</returns>
    public IAsteriskProxyBuilder WithLogger(ILoggerFactory loggerFactory)
    {
        _loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
        return this;
    }

    /// <summary>
    /// Builds the Asterisk proxy instance
    /// </summary>
    /// <returns>The configured Asterisk proxy</returns>
    public IAsteriskProxy Build()
    {
        ValidateConfiguration();

        var logger = _loggerFactory?.CreateLogger<AsteriskProxy>() ?? NullLogger<AsteriskProxy>.Instance;
        return new AsteriskProxy(_configuration, _extensions, logger);
    }

    /// <summary>
    /// Validates the current configuration
    /// </summary>
    private void ValidateConfiguration()
    {
        if (!_configuration.IsValid())
        {
            throw new InvalidOperationException("Invalid endpoint configuration. Please check all required settings.");
        }

        if (_extensions.Count == 0)
        {
            throw new InvalidOperationException("At least one extension must be configured for monitoring.");
        }

        // Check for duplicate extensions
        var duplicates = _extensions
            .GroupBy(e => e.GetKey())
            .Where(g => g.Count() > 1)
            .Select(g => g.Key)
            .ToList();

        if (duplicates.Count > 0)
        {
            throw new InvalidOperationException($"Duplicate extensions found: {string.Join(", ", duplicates)}");
        }
    }
}
