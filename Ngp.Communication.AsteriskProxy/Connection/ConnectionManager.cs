using Microsoft.Extensions.Logging;
using Ngp.Communication.AsteriskProxy.Enums;
using Ngp.Communication.AsteriskProxy.Events;
using Ngp.Communication.AsteriskProxy.Models;
using System.Net;
using System.Text;

namespace Ngp.Communication.AsteriskProxy.Connection;

/// <summary>
/// Manages HTTP connections to the Asterisk REST API
/// </summary>
public class ConnectionManager : IDisposable
{
    private readonly EndpointConfiguration _configuration;
    private readonly ILogger<ConnectionManager> _logger;
    private readonly SemaphoreSlim _connectionSemaphore;
    private readonly CancellationTokenSource _cancellationTokenSource;

    private HttpClient? _httpClient;
    private ConnectionState _state = ConnectionState.Disconnected;
    private DateTime _lastConnectionAttempt = DateTime.MinValue;
    private int _currentRetryAttempt = 0;
    private bool _disposed = false;

    /// <summary>
    /// Event raised when connection state changes
    /// </summary>
    public event EventHandler<ConnectionStateChangedEventArgs>? ConnectionStateChanged;

    /// <summary>
    /// Gets the current connection state
    /// </summary>
    public ConnectionState State => _state;

    /// <summary>
    /// Gets the HTTP client instance
    /// </summary>
    public HttpClient? HttpClient => _httpClient;

    /// <summary>
    /// Initializes a new instance of the ConnectionManager class
    /// </summary>
    /// <param name="configuration">Endpoint configuration</param>
    /// <param name="logger">Logger instance</param>
    public ConnectionManager(EndpointConfiguration configuration, ILogger<ConnectionManager> logger)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _connectionSemaphore = new SemaphoreSlim(1, 1);
        _cancellationTokenSource = new CancellationTokenSource();
    }

    /// <summary>
    /// Establishes connection to the Asterisk REST API
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if connection successful, false otherwise</returns>
    public async Task<bool> ConnectAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(ConnectionManager));

        await _connectionSemaphore.WaitAsync(cancellationToken);
        try
        {
            if (_state == ConnectionState.Connected)
            {
                _logger.LogDebug("Already connected to {EndpointId}", _configuration.Id);
                return true;
            }

            SetConnectionState(ConnectionState.Connecting);
            _lastConnectionAttempt = DateTime.UtcNow;

            try
            {
                // Create HTTP client with configuration
                var handler = new HttpClientHandler();
                if (!_configuration.VerifySslCertificate)
                {
                    handler.ServerCertificateCustomValidationCallback = (_, _, _, _) => true;
                }

                _httpClient = new HttpClient(handler)
                {
                    BaseAddress = new Uri(_configuration.BaseUrl),
                    Timeout = TimeSpan.FromMilliseconds(_configuration.RequestTimeoutMs)
                };

                // Set authentication header
                var authBytes = Encoding.UTF8.GetBytes($"{_configuration.Username}:{_configuration.Password}");
                var authHeader = Convert.ToBase64String(authBytes);
                _httpClient.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", authHeader);

                // Set common headers
                _httpClient.DefaultRequestHeaders.Accept.Clear();
                _httpClient.DefaultRequestHeaders.Accept.Add(
                    new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));

                // Test connection with a simple API call
                var testUrl = GetApiUrl("asterisk/info");
                using var testResponse = await _httpClient.GetAsync(testUrl, cancellationToken);

                if (testResponse.IsSuccessStatusCode)
                {
                    SetConnectionState(ConnectionState.Connected);
                    _currentRetryAttempt = 0;
                    _logger.LogInformation("Successfully connected to Asterisk REST API at {BaseUrl}", _configuration.BaseUrl);
                    return true;
                }
                else
                {
                    var errorMessage = $"HTTP {testResponse.StatusCode}: {testResponse.ReasonPhrase}";
                    _logger.LogWarning("Connection test failed for {EndpointId}: {ErrorMessage}", 
                        _configuration.Id, errorMessage);
                    SetConnectionState(ConnectionState.Error, errorMessage);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to connect to {EndpointId}: {ErrorMessage}", 
                    _configuration.Id, ex.Message);
                SetConnectionState(ConnectionState.Error, ex.Message);
                return false;
            }
        }
        finally
        {
            _connectionSemaphore.Release();
        }
    }

    /// <summary>
    /// Disconnects from the Asterisk REST API
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task DisconnectAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed) return;

        await _connectionSemaphore.WaitAsync(cancellationToken);
        try
        {
            if (_state == ConnectionState.Disconnected)
            {
                return;
            }

            SetConnectionState(ConnectionState.Disconnecting);

            _httpClient?.Dispose();
            _httpClient = null;

            SetConnectionState(ConnectionState.Disconnected);
            _logger.LogInformation("Disconnected from {EndpointId}", _configuration.Id);
        }
        finally
        {
            _connectionSemaphore.Release();
        }
    }

    /// <summary>
    /// Attempts to reconnect with retry logic
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if reconnection successful, false otherwise</returns>
    public async Task<bool> ReconnectAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(ConnectionManager));

        if (_currentRetryAttempt >= _configuration.MaxRetryAttempts)
        {
            _logger.LogError("Maximum retry attempts ({MaxRetryAttempts}) reached for {EndpointId}", 
                _configuration.MaxRetryAttempts, _configuration.Id);
            return false;
        }

        _currentRetryAttempt++;
        SetConnectionState(ConnectionState.Retrying);

        _logger.LogInformation("Attempting reconnection {CurrentAttempt}/{MaxAttempts} for {EndpointId}", 
            _currentRetryAttempt, _configuration.MaxRetryAttempts, _configuration.Id);

        // Wait for retry delay
        if (_configuration.RetryDelayMs > 0)
        {
            await Task.Delay(_configuration.RetryDelayMs, cancellationToken);
        }

        // Disconnect first
        await DisconnectAsync(cancellationToken);

        // Attempt to connect
        return await ConnectAsync(cancellationToken);
    }

    /// <summary>
    /// Gets the full API URL for a given endpoint
    /// </summary>
    /// <param name="endpoint">API endpoint</param>
    /// <returns>Full URL</returns>
    public string GetApiUrl(string endpoint)
    {
        var apiVersion = _configuration.ApiVersion switch
        {
            AsteriskApiVersion.Version22 => "ari",
            AsteriskApiVersion.Version23 => "ari",
            _ => "ari"
        };

        return $"{_configuration.BaseUrl}/{apiVersion}/{endpoint.TrimStart('/')}";
    }

    /// <summary>
    /// Sets the connection state and raises the event
    /// </summary>
    /// <param name="newState">New connection state</param>
    /// <param name="errorMessage">Optional error message</param>
    private void SetConnectionState(ConnectionState newState, string? errorMessage = null)
    {
        var previousState = _state;
        _state = newState;

        if (previousState != newState)
        {
            _logger.LogDebug("Connection state changed from {PreviousState} to {CurrentState} for {EndpointId}", 
                previousState, newState, _configuration.Id);

            ConnectionStateChanged?.Invoke(this, new ConnectionStateChangedEventArgs(
                _configuration.Id, previousState, newState, errorMessage));
        }
    }

    /// <summary>
    /// Disposes the connection manager
    /// </summary>
    public void Dispose()
    {
        if (_disposed) return;

        _disposed = true;
        _cancellationTokenSource.Cancel();

        try
        {
            DisconnectAsync().GetAwaiter().GetResult();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error during disposal of ConnectionManager for {EndpointId}", _configuration.Id);
        }

        _connectionSemaphore.Dispose();
        _cancellationTokenSource.Dispose();
        _httpClient?.Dispose();
    }
}
