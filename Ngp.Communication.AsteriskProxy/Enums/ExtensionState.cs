namespace Ngp.Communication.AsteriskProxy.Enums;

/// <summary>
/// Extension states as defined by Asterisk
/// </summary>
public enum ExtensionState
{
    /// <summary>
    /// Extension state is not in use
    /// </summary>
    NotInUse = 0,

    /// <summary>
    /// Extension is in use
    /// </summary>
    InUse = 1,

    /// <summary>
    /// Extension is busy
    /// </summary>
    Busy = 2,

    /// <summary>
    /// Extension is unavailable
    /// </summary>
    Unavailable = 4,

    /// <summary>
    /// Extension is ringing
    /// </summary>
    Ringing = 8,

    /// <summary>
    /// Extension is on hold
    /// </summary>
    OnHold = 16,

    /// <summary>
    /// Extension state is unknown
    /// </summary>
    Unknown = -1
}
