using Microsoft.Extensions.Logging;
using Ngp.Communication.AsteriskProxy.Connection;
using Ngp.Communication.AsteriskProxy.Enums;
using Ngp.Communication.AsteriskProxy.Models;
using System.Net;
using System.Text.Json;

namespace Ngp.Communication.AsteriskProxy.Api;

/// <summary>
/// Client for interacting with Asterisk REST API
/// </summary>
public class AsteriskApiClient : IDisposable
{
    private readonly ConnectionManager _connectionManager;
    private readonly ILogger<AsteriskApiClient> _logger;
    private readonly JsonSerializerOptions _jsonOptions;
    private bool _disposed = false;

    /// <summary>
    /// Initializes a new instance of the AsteriskApiClient class
    /// </summary>
    /// <param name="connectionManager">Connection manager instance</param>
    /// <param name="logger">Logger instance</param>
    public AsteriskApiClient(ConnectionManager connectionManager, ILogger<AsteriskApiClient> logger)
    {
        _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
        };

        // Add custom DateTime converters for Asterisk REST API 22 compatibility
        _jsonOptions.Converters.Add(new AsteriskDateTimeConverter());
        _jsonOptions.Converters.Add(new AsteriskNullableDateTimeConverter());
    }



    /// <summary>
    /// Gets all endpoints from Asterisk
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of endpoint responses</returns>
    public async Task<List<AsteriskEndpointResponse>> GetEndpointsAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AsteriskApiClient));

        var httpClient = _connectionManager.HttpClient;
        if (httpClient == null)
        {
            _logger.LogWarning("HTTP client is not available for endpoints request");
            return new List<AsteriskEndpointResponse>();
        }

        try
        {
            var url = _connectionManager.GetApiUrl("endpoints");
            _logger.LogDebug("Requesting endpoints from {Url}", url);

            using var response = await httpClient.GetAsync(url, cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogWarning("Failed to get endpoints: {StatusCode} - {Content}", 
                    response.StatusCode, errorContent);
                return new List<AsteriskEndpointResponse>();
            }

            var content = await response.Content.ReadAsStringAsync(cancellationToken);
            _logger.LogTrace("Endpoints response: {Content}", content);

            var endpoints = JsonSerializer.Deserialize<List<AsteriskEndpointResponse>>(content, _jsonOptions);
            return endpoints ?? new List<AsteriskEndpointResponse>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting endpoints: {ErrorMessage}", ex.Message);
            return new List<AsteriskEndpointResponse>();
        }
    }

    /// <summary>
    /// Gets all active channels from Asterisk
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of channel responses</returns>
    public async Task<List<AsteriskChannelResponse>> GetChannelsAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AsteriskApiClient));

        var httpClient = _connectionManager.HttpClient;
        if (httpClient == null)
        {
            _logger.LogWarning("HTTP client is not available for channels request");
            return new List<AsteriskChannelResponse>();
        }

        try
        {
            var url = _connectionManager.GetApiUrl("channels");
            _logger.LogDebug("Requesting channels from {Url}", url);

            using var response = await httpClient.GetAsync(url, cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogWarning("Failed to get channels: {StatusCode} - {Content}", 
                    response.StatusCode, errorContent);
                return new List<AsteriskChannelResponse>();
            }

            var content = await response.Content.ReadAsStringAsync(cancellationToken);
            _logger.LogTrace("Channels response: {Content}", content);

            var channels = JsonSerializer.Deserialize<List<AsteriskChannelResponse>>(content, _jsonOptions);
            return channels ?? new List<AsteriskChannelResponse>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting channels: {ErrorMessage}", ex.Message);
            return new List<AsteriskChannelResponse>();
        }
    }

    /// <summary>
    /// Gets system information from Asterisk
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>System info response or null if failed</returns>
    public async Task<AsteriskSystemInfoResponse?> GetSystemInfoAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AsteriskApiClient));

        var httpClient = _connectionManager.HttpClient;
        if (httpClient == null)
        {
            _logger.LogWarning("HTTP client is not available for system info request");
            return null;
        }

        try
        {
            var url = _connectionManager.GetApiUrl("asterisk/info");
            _logger.LogDebug("Requesting system info from {Url}", url);

            using var response = await httpClient.GetAsync(url, cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogWarning("Failed to get system info: {StatusCode} - {Content}", 
                    response.StatusCode, errorContent);
                return null;
            }

            var content = await response.Content.ReadAsStringAsync(cancellationToken);
            _logger.LogTrace("System info response: {Content}", content);

            var systemInfo = JsonSerializer.Deserialize<AsteriskSystemInfoResponse>(content, _jsonOptions);
            return systemInfo;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting system info: {ErrorMessage}", ex.Message);
            return null;
        }
    }

    /// <summary>
    /// Converts Asterisk API extension state to internal enum
    /// </summary>
    /// <param name="asteriskState">Asterisk state value</param>
    /// <returns>Internal extension state</returns>
    public static ExtensionState ConvertAsteriskState(int asteriskState)
    {
        return asteriskState switch
        {
            0 => ExtensionState.NotInUse,
            1 => ExtensionState.InUse,
            2 => ExtensionState.Busy,
            4 => ExtensionState.Unavailable,
            8 => ExtensionState.Ringing,
            16 => ExtensionState.OnHold,
            _ => ExtensionState.Unknown
        };
    }

    /// <summary>
    /// Disposes the API client
    /// </summary>
    public void Dispose()
    {
        if (_disposed) return;
        _disposed = true;
    }
}
