using Microsoft.Extensions.Logging;
using Ngp.Communication.AsteriskProxy.Api;
using Ngp.Communication.AsteriskProxy.Enums;
using Ngp.Communication.AsteriskProxy.Events;
using Ngp.Communication.AsteriskProxy.Models;
using System.Collections.Concurrent;

namespace Ngp.Communication.AsteriskProxy.Polling;

/// <summary>
/// Manages background polling of extension statuses and triggers events on changes
/// </summary>
public class PollingEngine : IDisposable
{
    private readonly EndpointConfiguration _configuration;
    private readonly AsteriskApiClient _apiClient;
    private readonly ILogger<PollingEngine> _logger;

    private readonly ConcurrentDictionary<string, ExtensionStatus> _extensionStatuses;
    private readonly SemaphoreSlim _pollingSemaphore;
    private readonly CancellationTokenSource _cancellationTokenSource;

    private List<ExtensionDefinition> _extensions = new();
    private Task? _pollingTask;
    private bool _isPolling = false;
    private bool _disposed = false;

    /// <summary>
    /// Event raised when an extension status changes
    /// </summary>
    public event EventHandler<ExtensionStatusChangedEventArgs>? ExtensionStatusChanged;

    /// <summary>
    /// Event raised when an Asterisk API error occurs
    /// </summary>
    public event EventHandler<AsteriskErrorEventArgs>? AsteriskError;

    /// <summary>
    /// Initializes a new instance of the PollingEngine class
    /// </summary>
    /// <param name="configuration">Endpoint configuration</param>
    /// <param name="apiClient">API client instance</param>
    /// <param name="logger">Logger instance</param>
    public PollingEngine(
        EndpointConfiguration configuration,
        AsteriskApiClient apiClient,
        ILogger<PollingEngine> logger)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        _extensionStatuses = new ConcurrentDictionary<string, ExtensionStatus>();
        _pollingSemaphore = new SemaphoreSlim(1, 1);
        _cancellationTokenSource = new CancellationTokenSource();
    }

    /// <summary>
    /// Sets the extensions to monitor
    /// </summary>
    /// <param name="extensions">List of extension definitions</param>
    public void SetExtensions(List<ExtensionDefinition> extensions)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(PollingEngine));

        _extensions = extensions?.ToList() ?? new List<ExtensionDefinition>();
        _logger.LogDebug("Set {ExtensionCount} extensions for polling", _extensions.Count);
    }

    /// <summary>
    /// Starts the polling engine
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(PollingEngine));

        await _pollingSemaphore.WaitAsync(cancellationToken);
        try
        {
            if (_isPolling)
            {
                _logger.LogDebug("Polling engine is already running");
                return;
            }

            _isPolling = true;
            _pollingTask = Task.Run(() => PollingLoopAsync(_cancellationTokenSource.Token), cancellationToken);

            _logger.LogInformation("Polling engine started with {ExtensionCount} extensions", _extensions.Count);
        }
        finally
        {
            _pollingSemaphore.Release();
        }
    }

    /// <summary>
    /// Stops the polling engine
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed) return;

        await _pollingSemaphore.WaitAsync(cancellationToken);
        try
        {
            if (!_isPolling)
            {
                return;
            }

            _isPolling = false;
            _cancellationTokenSource.Cancel();

            if (_pollingTask != null)
            {
                try
                {
                    await _pollingTask;
                }
                catch (OperationCanceledException)
                {
                    // Expected when cancellation is requested
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error while stopping polling engine");
                }
            }

            _logger.LogInformation("Polling engine stopped");
        }
        finally
        {
            _pollingSemaphore.Release();
        }
    }

    /// <summary>
    /// Gets the current status of all monitored extensions
    /// </summary>
    /// <returns>Dictionary of extension statuses</returns>
    public IReadOnlyDictionary<string, ExtensionStatus> GetAllExtensionStatuses()
    {
        return _extensionStatuses.ToDictionary(kvp => kvp.Key, kvp => kvp.Value.Clone());
    }

    /// <summary>
    /// Gets the current status of a specific extension
    /// </summary>
    /// <param name="extension">Extension number</param>
    /// <param name="context">Extension context</param>
    /// <returns>Extension status or null if not found</returns>
    public ExtensionStatus? GetExtensionStatus(string extension, string context = "default")
    {
        var key = $"{context}:{extension}";
        return _extensionStatuses.TryGetValue(key, out var status) ? status.Clone() : null;
    }

    /// <summary>
    /// Forces a refresh of all extension statuses
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task RefreshAllExtensionsAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(PollingEngine));

        _logger.LogDebug("Forcing refresh of all extension statuses");

        var tasks = _extensions.Select(ext => PollExtensionAsync(ext, cancellationToken));
        await Task.WhenAll(tasks);

        _logger.LogDebug("Completed refresh of all extension statuses");
    }

    /// <summary>
    /// Main polling loop
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    private async Task PollingLoopAsync(CancellationToken cancellationToken)
    {
        _logger.LogDebug("Starting polling loop");

        while (!cancellationToken.IsCancellationRequested && _isPolling)
        {
            try
            {
                var pollingTasks = _extensions.Select(ext => PollExtensionAsync(ext, cancellationToken));
                await Task.WhenAll(pollingTasks);

                await Task.Delay(_configuration.PollingIntervalMs, cancellationToken);
            }
            catch (OperationCanceledException)
            {
                _logger.LogDebug("Polling loop cancelled");
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in polling loop: {ErrorMessage}", ex.Message);

                // Raise error event
                AsteriskError?.Invoke(this, new AsteriskErrorEventArgs(
                    _configuration.Id, $"Polling loop error: {ex.Message}", null, ex));

                // Wait before retrying
                try
                {
                    await Task.Delay(Math.Min(_configuration.PollingIntervalMs * 2, 10000), cancellationToken);
                }
                catch (OperationCanceledException)
                {
                    break;
                }
            }
        }

        _logger.LogDebug("Polling loop ended");
    }

    /// <summary>
    /// Polls a single extension for status changes
    /// </summary>
    /// <param name="extension">Extension definition</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    private async Task PollExtensionAsync(ExtensionDefinition extension, CancellationToken cancellationToken)
    {
        try
        {
            var key = extension.GetKey();

            // Get current status from API
            var newStatus = await GetExtensionStatusFromApiAsync(extension, cancellationToken);
            if (newStatus == null)
            {
                return; // Skip if we couldn't get status
            }

            // Get previous status
            var previousStatus = _extensionStatuses.TryGetValue(key, out var prev) ? prev : null;
            var isInitialStatus = previousStatus == null;

            // Check if status has changed
            if (HasStatusChanged(previousStatus, newStatus))
            {
                // Update stored status
                _extensionStatuses[key] = newStatus;

                // Log the change (only if not initial status to avoid excessive logging)
                if (!isInitialStatus)
                {
                    _logger.LogInformation("Extension status changed for {Extension}: {PreviousState} -> {CurrentState}",
                        extension, previousStatus?.State, newStatus.State);
                }

                // Raise event
                ExtensionStatusChanged?.Invoke(this, new ExtensionStatusChangedEventArgs(
                    _configuration.Id, extension.Extension, extension.Context,
                    previousStatus, newStatus, isInitialStatus));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error polling extension {Extension}: {ErrorMessage}", extension, ex.Message);
        }
    }

    /// <summary>
    /// Gets extension status from the Asterisk API using endpoints and channels
    /// </summary>
    /// <param name="extension">Extension definition</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Extension status or null if failed</returns>
    private async Task<ExtensionStatus?> GetExtensionStatusFromApiAsync(
        ExtensionDefinition extension,
        CancellationToken cancellationToken)
    {
        try
        {
            // Get all endpoints and channels from Asterisk
            var endpoints = await _apiClient.GetEndpointsAsync(cancellationToken);
            var channels = await _apiClient.GetChannelsAsync(cancellationToken);

            // Find matching endpoints for this extension using multiple methods
            var matchingEndpoints = new List<AsteriskEndpointResponse>();

            // Method 1: Match by Resource containing extension number
            var byResource = endpoints.Where(e =>
                !string.IsNullOrEmpty(e.Resource) && e.Resource.Contains(extension.Extension)).ToList();
            if (byResource.Any())
            {
                _logger.LogDebug("Extension {Extension} matched {Count} endpoints by Resource",
                    extension.Extension, byResource.Count);
                matchingEndpoints.AddRange(byResource);
            }

            // Method 2: Use the GetExtensionNumber method
            var byExtract = endpoints.Where(e =>
                e.GetExtensionNumber() == extension.Extension &&
                !matchingEndpoints.Contains(e)).ToList();
            if (byExtract.Any())
            {
                _logger.LogDebug("Extension {Extension} matched {Count} endpoints by extraction",
                    extension.Extension, byExtract.Count);
                matchingEndpoints.AddRange(byExtract);
            }

            // Find channels related to this extension
            var extensionChannels = channels.Where(c =>
            {
                // Check dialplan information
                if (c.Dialplan?.Extension == extension.Extension &&
                    c.Dialplan?.Context == extension.Context)
                    return true;

                // Check if channel is related to extension using the new method
                if (c.IsRelatedToExtension(extension.Extension))
                    return true;

                return false;
            }).ToList();

            // Determine connection and call status based on endpoints and channels
            bool isConnected = false;
            bool isInCall = false;
            ExtensionState state = ExtensionState.Unknown;
            string statusText = "Unknown";

            if (matchingEndpoints.Any())
            {
                // Check if any endpoint is online
                var validOnlineStates = new[] { "online", "available", "registered", "reachable" };
                isConnected = matchingEndpoints.Any(e =>
                    !string.IsNullOrEmpty(e.State) &&
                    validOnlineStates.Contains(e.State.ToLower()));

                if (isConnected)
                {
                    state = ExtensionState.NotInUse;
                    statusText = "Available";
                }
                else
                {
                    state = ExtensionState.Unavailable;
                    statusText = "Unavailable";
                }
            }
            else if (extensionChannels.Any())
            {
                // If no endpoints found but channels exist, consider it connected
                isConnected = true;
                state = ExtensionState.NotInUse;
                statusText = "Available (via channels)";
            }

            // Check for active calls
            if (extensionChannels.Any())
            {
                var validCallStates = new[] { "up", "ringing", "busy", "dialing", "ring", "progress" };
                var activeChannel = extensionChannels.FirstOrDefault(c =>
                    !string.IsNullOrEmpty(c.State) &&
                    !c.State.Equals("down", StringComparison.OrdinalIgnoreCase) &&
                    validCallStates.Contains(c.State.ToLower()));

                if (activeChannel != null)
                {
                    isInCall = true;
                    state = ExtensionState.InUse;
                    statusText = $"In call ({activeChannel.State})";

                    _logger.LogDebug("Extension {Extension} is in call via channel {Name} with state {State}",
                        extension.Extension, activeChannel.Name, activeChannel.State);
                }
            }

            // Create extension status
            var status = new ExtensionStatus
            {
                Extension = extension.Extension,
                Context = extension.Context,
                State = state,
                Hint = null, // Not available from endpoints API
                StatusText = statusText,
                LastUpdated = DateTime.UtcNow,
                IsConnected = isConnected,
                IsInCall = isInCall,
                ActiveChannels = extensionChannels.Count
            };

            return status;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting extension status from API for {Extension}: {ErrorMessage}",
                extension, ex.Message);
            return null;
        }
    }

    /// <summary>
    /// Checks if extension status has changed
    /// </summary>
    /// <param name="previous">Previous status</param>
    /// <param name="current">Current status</param>
    /// <returns>True if status has changed</returns>
    private static bool HasStatusChanged(ExtensionStatus? previous, ExtensionStatus current)
    {
        if (previous == null) return true;

        return previous.State != current.State ||
               previous.IsConnected != current.IsConnected ||
               previous.IsInCall != current.IsInCall ||
               previous.ActiveChannels != current.ActiveChannels;
    }

    /// <summary>
    /// Disposes the polling engine
    /// </summary>
    public void Dispose()
    {
        if (_disposed) return;

        _disposed = true;

        try
        {
            StopAsync().GetAwaiter().GetResult();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error during disposal of PollingEngine");
        }

        _pollingSemaphore.Dispose();
        _cancellationTokenSource.Dispose();
    }
}
