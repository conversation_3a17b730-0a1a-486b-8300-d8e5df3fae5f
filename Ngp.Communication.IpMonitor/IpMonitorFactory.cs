using Microsoft.Extensions.Logging;
using Ngp.Communication.IpMonitor.Builders;
using Ngp.Communication.IpMonitor.Interfaces;

namespace Ngp.Communication.IpMonitor;

/// <summary>
/// Factory class for creating IP monitor instances using Fluent API
/// </summary>
public static class IpMonitorFactory
{
    /// <summary>
    /// Creates a new IP monitor builder
    /// All configuration should be done through the Fluent API after calling this method
    /// </summary>
    /// <param name="loggerFactory">Optional logger factory</param>
    /// <returns>A new builder instance</returns>
    /// <example>
    /// <code>
    /// var monitor = IpMonitorFactory.Create(loggerFactory)
    ///     .WithId("my-monitor")
    ///     .WithMonitoringMode(MonitoringMode.Normal)
    ///     .WithDetectionMethod(DetectionMethod.IcmpPing)
    ///     .WithMaxConcurrency(50)
    ///     .WithTimeout(5000)
    ///     .WithPollingInterval(2000)
    ///     .WithRetryPolicy(3, 1000)
    ///     .WithConsecutiveSuccessCount(2)
    ///     .AddIpAddress("*******")
    ///     .AddIpRange("***********~*************")
    ///     .Build();
    /// </code>
    /// </example>
    public static IIpMonitorBuilder Create(ILoggerFactory? loggerFactory = null)
    {
        return new IpMonitorBuilder(loggerFactory);
    }
}
