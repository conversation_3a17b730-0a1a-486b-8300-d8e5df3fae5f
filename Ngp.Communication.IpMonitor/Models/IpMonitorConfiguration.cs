using Ngp.Communication.IpMonitor.Enums;

namespace Ngp.Communication.IpMonitor.Models;

/// <summary>
/// Configuration settings for IP monitoring
/// </summary>
public class IpMonitorConfiguration
{
    /// <summary>
    /// Gets or sets the monitor identifier
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the monitoring mode
    /// </summary>
    public MonitoringMode MonitoringMode { get; set; } = MonitoringMode.Normal;

    /// <summary>
    /// Gets or sets the detection method
    /// </summary>
    public DetectionMethod DetectionMethod { get; set; } = DetectionMethod.IcmpPing;

    /// <summary>
    /// Gets or sets the maximum number of concurrent ping operations
    /// </summary>
    public int MaxConcurrency { get; set; } = 50;

    /// <summary>
    /// Gets or sets the ping timeout in milliseconds
    /// </summary>
    public int TimeoutMs { get; set; } = 5000;

    /// <summary>
    /// Gets or sets the polling interval in milliseconds
    /// </summary>
    public int PollingIntervalMs { get; set; } = 1000;

    /// <summary>
    /// Gets or sets the maintenance time in milliseconds (for Simple mode)
    /// Time window to consider IP online if any response received within this period
    /// </summary>
    public int MaintenanceTimeMs { get; set; } = 300000; // 5 minutes

    /// <summary>
    /// Gets or sets the retry count (for Normal mode)
    /// </summary>
    public int RetryCount { get; set; } = 3;

    /// <summary>
    /// Gets or sets the retry delay in milliseconds (for Normal mode)
    /// </summary>
    public int RetryDelayMs { get; set; } = 1000;

    /// <summary>
    /// Gets or sets the consecutive success count required to confirm online status (for Normal mode)
    /// </summary>
    public int ConsecutiveSuccessCount { get; set; } = 2;

    /// <summary>
    /// Validates the configuration
    /// </summary>
    /// <returns>True if configuration is valid</returns>
    public bool IsValid()
    {
        return !string.IsNullOrEmpty(Id) &&
               MaxConcurrency > 0 &&
               TimeoutMs > 0 &&
               PollingIntervalMs > 0 &&
               MaintenanceTimeMs > 0 &&
               RetryCount > 0 &&
               RetryDelayMs >= 0 &&
               ConsecutiveSuccessCount > 0;
    }
}
