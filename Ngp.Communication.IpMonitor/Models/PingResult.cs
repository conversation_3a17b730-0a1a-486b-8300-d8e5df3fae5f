using System.Net;
using System.Net.NetworkInformation;

namespace Ngp.Communication.IpMonitor.Models;

/// <summary>
/// Represents the result of a ping operation
/// </summary>
public class PingResult
{
    /// <summary>
    /// Gets or sets the target IP address
    /// </summary>
    public IPAddress IpAddress { get; set; } = IPAddress.None;

    /// <summary>
    /// Gets or sets whether the ping was successful
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// Gets or sets the response time in milliseconds
    /// </summary>
    public long ResponseTimeMs { get; set; }

    /// <summary>
    /// Gets or sets the ping status
    /// </summary>
    public IPStatus Status { get; set; } = IPStatus.Unknown;

    /// <summary>
    /// Gets or sets the timestamp when the ping was performed
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Gets or sets the error message if ping failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Gets or sets the exception if ping failed
    /// </summary>
    public Exception? Exception { get; set; }

    /// <summary>
    /// Creates a successful ping result
    /// </summary>
    /// <param name="ipAddress">Target IP address</param>
    /// <param name="responseTimeMs">Response time in milliseconds</param>
    /// <returns>Successful ping result</returns>
    public static PingResult Success(IPAddress ipAddress, long responseTimeMs)
    {
        return new PingResult
        {
            IpAddress = ipAddress,
            IsSuccess = true,
            ResponseTimeMs = responseTimeMs,
            Status = IPStatus.Success,
            Timestamp = DateTime.UtcNow
        };
    }

    /// <summary>
    /// Creates a failed ping result
    /// </summary>
    /// <param name="ipAddress">Target IP address</param>
    /// <param name="status">Ping status</param>
    /// <param name="errorMessage">Error message</param>
    /// <param name="exception">Exception</param>
    /// <returns>Failed ping result</returns>
    public static PingResult Failure(IPAddress ipAddress, IPStatus status, string? errorMessage = null, Exception? exception = null)
    {
        return new PingResult
        {
            IpAddress = ipAddress,
            IsSuccess = false,
            ResponseTimeMs = 0,
            Status = status,
            ErrorMessage = errorMessage,
            Exception = exception,
            Timestamp = DateTime.UtcNow
        };
    }
}
