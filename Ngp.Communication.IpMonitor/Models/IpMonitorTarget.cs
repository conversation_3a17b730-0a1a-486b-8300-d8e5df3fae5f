using System.Net;
using Ngp.Communication.IpMonitor.Enums;

namespace Ngp.Communication.IpMonitor.Models;

/// <summary>
/// Represents an IP monitoring target
/// </summary>
public class IpMonitorTarget
{
    /// <summary>
    /// Gets or sets the IP address
    /// </summary>
    public IPAddress IpAddress { get; set; } = IPAddress.None;

    /// <summary>
    /// Gets or sets the current status
    /// </summary>
    public IpStatus CurrentStatus { get; set; } = IpStatus.Unknown;

    /// <summary>
    /// Gets or sets the previous status
    /// </summary>
    public IpStatus PreviousStatus { get; set; } = IpStatus.Unknown;

    /// <summary>
    /// Gets or sets the last successful ping timestamp
    /// </summary>
    public DateTime LastSuccessTime { get; set; } = DateTime.MinValue;

    /// <summary>
    /// Gets or sets the last failed ping timestamp
    /// </summary>
    public DateTime LastFailureTime { get; set; } = DateTime.MinValue;

    /// <summary>
    /// Gets or sets the last status change timestamp
    /// </summary>
    public DateTime LastStatusChangeTime { get; set; } = DateTime.MinValue;

    /// <summary>
    /// Gets or sets the consecutive success count (for Normal mode)
    /// </summary>
    public int ConsecutiveSuccessCount { get; set; } = 0;

    /// <summary>
    /// Gets or sets the consecutive failure count (for Normal mode)
    /// </summary>
    public int ConsecutiveFailureCount { get; set; } = 0;

    /// <summary>
    /// Gets or sets the total ping count
    /// </summary>
    public long TotalPingCount { get; set; } = 0;

    /// <summary>
    /// Gets or sets the successful ping count
    /// </summary>
    public long SuccessfulPingCount { get; set; } = 0;

    /// <summary>
    /// Gets or sets the failed ping count
    /// </summary>
    public long FailedPingCount { get; set; } = 0;

    /// <summary>
    /// Gets or sets the average response time in milliseconds
    /// </summary>
    public double AverageResponseTimeMs { get; set; } = 0;

    /// <summary>
    /// Gets or sets the last response time in milliseconds
    /// </summary>
    public long LastResponseTimeMs { get; set; } = 0;

    /// <summary>
    /// Gets or sets optional tag for identification
    /// </summary>
    public string? Tag { get; set; }

    /// <summary>
    /// Gets or sets optional description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Creates a clone of this target
    /// </summary>
    /// <returns>Cloned target</returns>
    public IpMonitorTarget Clone()
    {
        return new IpMonitorTarget
        {
            IpAddress = IpAddress,
            CurrentStatus = CurrentStatus,
            PreviousStatus = PreviousStatus,
            LastSuccessTime = LastSuccessTime,
            LastFailureTime = LastFailureTime,
            LastStatusChangeTime = LastStatusChangeTime,
            ConsecutiveSuccessCount = ConsecutiveSuccessCount,
            ConsecutiveFailureCount = ConsecutiveFailureCount,
            TotalPingCount = TotalPingCount,
            SuccessfulPingCount = SuccessfulPingCount,
            FailedPingCount = FailedPingCount,
            AverageResponseTimeMs = AverageResponseTimeMs,
            LastResponseTimeMs = LastResponseTimeMs,
            Tag = Tag,
            Description = Description
        };
    }
}
