using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Ngp.Communication.IpMonitor.Enums;
using Ngp.Communication.IpMonitor.Models;

namespace Ngp.Communication.IpMonitor.Monitoring;

/// <summary>
/// Processes monitoring results based on different monitoring modes
/// </summary>
public class MonitoringModeProcessor
{
    private readonly IpMonitorConfiguration _configuration;
    private readonly ILogger<MonitoringModeProcessor> _logger;

    /// <summary>
    /// Initializes a new instance of the MonitoringModeProcessor class
    /// </summary>
    /// <param name="configuration">Monitor configuration</param>
    /// <param name="logger">Optional logger instance</param>
    public MonitoringModeProcessor(IpMonitorConfiguration configuration, ILogger<MonitoringModeProcessor>? logger = null)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? NullLogger<MonitoringModeProcessor>.Instance;
    }

    /// <summary>
    /// Processes a ping result and determines if status should change
    /// </summary>
    /// <param name="target">Target being monitored</param>
    /// <param name="pingResult">Latest ping result</param>
    /// <returns>True if status should change, false otherwise</returns>
    public bool ProcessPingResult(IpMonitorTarget target, PingResult pingResult)
    {
        if (target == null) throw new ArgumentNullException(nameof(target));
        if (pingResult == null) throw new ArgumentNullException(nameof(pingResult));

        // Update statistics
        target.TotalPingCount++;
        target.LastResponseTimeMs = pingResult.ResponseTimeMs;

        if (pingResult.IsSuccess)
        {
            target.SuccessfulPingCount++;
            target.LastSuccessTime = pingResult.Timestamp;
            target.ConsecutiveSuccessCount++;
            target.ConsecutiveFailureCount = 0;
            
            // Update average response time
            UpdateAverageResponseTime(target, pingResult.ResponseTimeMs);
        }
        else
        {
            target.FailedPingCount++;
            target.LastFailureTime = pingResult.Timestamp;
            target.ConsecutiveFailureCount++;
            target.ConsecutiveSuccessCount = 0;
        }

        return _configuration.MonitoringMode switch
        {
            MonitoringMode.Immediate => ProcessImmediateMode(target, pingResult),
            MonitoringMode.Normal => ProcessNormalMode(target, pingResult),
            MonitoringMode.Simple => ProcessSimpleMode(target, pingResult),
            _ => ProcessImmediateMode(target, pingResult)
        };
    }

    /// <summary>
    /// Processes immediate monitoring mode - any success or failure is immediately reflected
    /// </summary>
    private bool ProcessImmediateMode(IpMonitorTarget target, PingResult pingResult)
    {
        var newStatus = pingResult.IsSuccess ? IpStatus.Online : IpStatus.Offline;
        
        if (target.CurrentStatus != newStatus)
        {
            _logger.LogTrace("Immediate mode: {IpAddress} status changing from {OldStatus} to {NewStatus}",
                target.IpAddress, target.CurrentStatus, newStatus);
            
            target.PreviousStatus = target.CurrentStatus;
            target.CurrentStatus = newStatus;
            target.LastStatusChangeTime = DateTime.UtcNow;
            return true;
        }

        return false;
    }

    /// <summary>
    /// Processes normal monitoring mode - provides retry mechanism to confirm status changes
    /// </summary>
    private bool ProcessNormalMode(IpMonitorTarget target, PingResult pingResult)
    {
        if (pingResult.IsSuccess)
        {
            // If currently offline and we have enough consecutive successes, go online
            if (target.CurrentStatus == IpStatus.Offline && 
                target.ConsecutiveSuccessCount >= _configuration.ConsecutiveSuccessCount)
            {
                _logger.LogTrace("Normal mode: {IpAddress} going online after {SuccessCount} consecutive successes",
                    target.IpAddress, target.ConsecutiveSuccessCount);
                
                target.PreviousStatus = target.CurrentStatus;
                target.CurrentStatus = IpStatus.Online;
                target.LastStatusChangeTime = DateTime.UtcNow;
                return true;
            }
            // If status is unknown, go online immediately on first success
            else if (target.CurrentStatus == IpStatus.Unknown)
            {
                _logger.LogTrace("Normal mode: {IpAddress} initial status set to online",
                    target.IpAddress);
                
                target.PreviousStatus = target.CurrentStatus;
                target.CurrentStatus = IpStatus.Online;
                target.LastStatusChangeTime = DateTime.UtcNow;
                return true;
            }
        }
        else
        {
            // If currently online and we have enough consecutive failures, go offline
            if (target.CurrentStatus == IpStatus.Online && 
                target.ConsecutiveFailureCount >= _configuration.RetryCount)
            {
                _logger.LogTrace("Normal mode: {IpAddress} going offline after {FailureCount} consecutive failures",
                    target.IpAddress, target.ConsecutiveFailureCount);
                
                target.PreviousStatus = target.CurrentStatus;
                target.CurrentStatus = IpStatus.Offline;
                target.LastStatusChangeTime = DateTime.UtcNow;
                return true;
            }
            // If status is unknown, go offline after retry count
            else if (target.CurrentStatus == IpStatus.Unknown && 
                     target.ConsecutiveFailureCount >= _configuration.RetryCount)
            {
                _logger.LogTrace("Normal mode: {IpAddress} initial status set to offline after {FailureCount} failures",
                    target.IpAddress, target.ConsecutiveFailureCount);
                
                target.PreviousStatus = target.CurrentStatus;
                target.CurrentStatus = IpStatus.Offline;
                target.LastStatusChangeTime = DateTime.UtcNow;
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// Processes simple monitoring mode - considers online if any response within maintenance time
    /// </summary>
    private bool ProcessSimpleMode(IpMonitorTarget target, PingResult pingResult)
    {
        var now = DateTime.UtcNow;
        var maintenanceWindow = TimeSpan.FromMilliseconds(_configuration.MaintenanceTimeMs);

        if (pingResult.IsSuccess)
        {
            // Any successful ping means we're online
            if (target.CurrentStatus != IpStatus.Online)
            {
                _logger.LogTrace("Simple mode: {IpAddress} going online due to successful ping",
                    target.IpAddress);
                
                target.PreviousStatus = target.CurrentStatus;
                target.CurrentStatus = IpStatus.Online;
                target.LastStatusChangeTime = now;
                return true;
            }
        }
        else
        {
            // Check if we're within maintenance window from last success
            if (target.LastSuccessTime != DateTime.MinValue)
            {
                var timeSinceLastSuccess = now - target.LastSuccessTime;
                
                if (timeSinceLastSuccess > maintenanceWindow && target.CurrentStatus != IpStatus.Offline)
                {
                    _logger.LogTrace("Simple mode: {IpAddress} going offline - no response for {TimeSinceSuccess}",
                        target.IpAddress, timeSinceLastSuccess);
                    
                    target.PreviousStatus = target.CurrentStatus;
                    target.CurrentStatus = IpStatus.Offline;
                    target.LastStatusChangeTime = now;
                    return true;
                }
            }
            else
            {
                // No successful ping yet, check if we should go offline based on total monitoring time
                var timeSinceFirstPing = target.TotalPingCount > 0 ? 
                    now - target.LastFailureTime : TimeSpan.Zero;
                
                if (timeSinceFirstPing > maintenanceWindow && target.CurrentStatus != IpStatus.Offline)
                {
                    _logger.LogTrace("Simple mode: {IpAddress} going offline - no successful ping within maintenance window",
                        target.IpAddress);
                    
                    target.PreviousStatus = target.CurrentStatus;
                    target.CurrentStatus = IpStatus.Offline;
                    target.LastStatusChangeTime = now;
                    return true;
                }
            }
        }

        return false;
    }

    /// <summary>
    /// Updates the average response time for a target
    /// </summary>
    private void UpdateAverageResponseTime(IpMonitorTarget target, long responseTimeMs)
    {
        if (target.SuccessfulPingCount == 1)
        {
            target.AverageResponseTimeMs = responseTimeMs;
        }
        else
        {
            // Calculate running average
            target.AverageResponseTimeMs = 
                ((target.AverageResponseTimeMs * (target.SuccessfulPingCount - 1)) + responseTimeMs) / 
                target.SuccessfulPingCount;
        }
    }
}
