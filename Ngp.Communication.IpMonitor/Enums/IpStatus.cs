namespace Ngp.Communication.IpMonitor.Enums;

/// <summary>
/// Represents the status of an IP address
/// </summary>
public enum IpStatus
{
    /// <summary>
    /// IP status is unknown or not yet determined
    /// </summary>
    Unknown = 0,

    /// <summary>
    /// IP is online and responding
    /// </summary>
    Online = 1,

    /// <summary>
    /// IP is offline and not responding
    /// </summary>
    Offline = 2
}
