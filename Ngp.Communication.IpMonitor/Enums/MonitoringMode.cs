namespace Ngp.Communication.IpMonitor.Enums;

/// <summary>
/// Represents different monitoring modes for IP status detection
/// </summary>
public enum MonitoringMode
{
    /// <summary>
    /// Immediate mode - any success or failure is immediately reflected
    /// </summary>
    Immediate = 0,

    /// <summary>
    /// Normal mode - provides reasonable retry mechanism to confirm status changes
    /// If IP was online and monitoring fails, retry to confirm it's really offline
    /// If IP was offline and now responds, require consecutive successes to confirm online
    /// </summary>
    Normal = 1,

    /// <summary>
    /// Simple mode - set a time window, if there's any response within this time, consider it online
    /// Other failures can be ignored, only consider offline if no response within the time window
    /// </summary>
    Simple = 2
}
