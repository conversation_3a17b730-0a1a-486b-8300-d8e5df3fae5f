using System.Net;
using System.Net.NetworkInformation;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Ngp.Communication.IpMonitor.Models;

namespace Ngp.Communication.IpMonitor.Detection;

/// <summary>
/// ICMP Ping detection engine
/// </summary>
public class IcmpDetectionEngine : IDisposable
{
    private readonly ILogger<IcmpDetectionEngine> _logger;
    private readonly SemaphoreSlim _semaphore;
    private readonly int _timeoutMs;
    private bool _disposed = false;

    /// <summary>
    /// Initializes a new instance of the IcmpDetectionEngine class
    /// </summary>
    /// <param name="maxConcurrency">Maximum concurrent ping operations</param>
    /// <param name="timeoutMs">Ping timeout in milliseconds</param>
    /// <param name="logger">Optional logger instance</param>
    public IcmpDetectionEngine(int maxConcurrency, int timeoutMs, ILogger<IcmpDetectionEngine>? logger = null)
    {
        if (maxConcurrency <= 0)
            throw new ArgumentException("Max concurrency must be greater than 0", nameof(maxConcurrency));
        if (timeoutMs <= 0)
            throw new ArgumentException("Timeout must be greater than 0", nameof(timeoutMs));

        _semaphore = new SemaphoreSlim(maxConcurrency, maxConcurrency);
        _timeoutMs = timeoutMs;
        _logger = logger ?? NullLogger<IcmpDetectionEngine>.Instance;
    }

    /// <summary>
    /// Performs an ICMP ping to the specified IP address
    /// </summary>
    /// <param name="ipAddress">Target IP address</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Ping result</returns>
    public async Task<PingResult> PingAsync(IPAddress ipAddress, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(IcmpDetectionEngine));

        await _semaphore.WaitAsync(cancellationToken);
        
        try
        {
            using var ping = new Ping();
            var reply = await ping.SendPingAsync(ipAddress, _timeoutMs);

            if (reply.Status == IPStatus.Success)
            {
                _logger.LogTrace("ICMP ping to {IpAddress} succeeded in {ResponseTime}ms", 
                    ipAddress, reply.RoundtripTime);
                return PingResult.Success(ipAddress, reply.RoundtripTime);
            }
            else
            {
                _logger.LogTrace("ICMP ping to {IpAddress} failed with status {Status}", 
                    ipAddress, reply.Status);
                return PingResult.Failure(ipAddress, reply.Status, $"Ping failed: {reply.Status}");
            }
        }
        catch (PingException ex)
        {
            _logger.LogTrace(ex, "ICMP ping to {IpAddress} failed with PingException", ipAddress);
            return PingResult.Failure(ipAddress, IPStatus.Unknown, ex.Message, ex);
        }
        catch (Exception ex)
        {
            _logger.LogTrace(ex, "ICMP ping to {IpAddress} failed with unexpected exception", ipAddress);
            return PingResult.Failure(ipAddress, IPStatus.Unknown, ex.Message, ex);
        }
        finally
        {
            _semaphore.Release();
        }
    }

    /// <summary>
    /// Performs ICMP pings to multiple IP addresses concurrently
    /// </summary>
    /// <param name="ipAddresses">Target IP addresses</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of ping results</returns>
    public async Task<IEnumerable<PingResult>> PingAsync(IEnumerable<IPAddress> ipAddresses, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(IcmpDetectionEngine));

        var tasks = ipAddresses.Select(ip => PingAsync(ip, cancellationToken));
        return await Task.WhenAll(tasks);
    }

    /// <summary>
    /// Gets the current available concurrency slots
    /// </summary>
    public int AvailableSlots => _semaphore.CurrentCount;

    /// <summary>
    /// Gets the maximum concurrency
    /// </summary>
    public int MaxConcurrency => _semaphore.CurrentCount + (_semaphore.CurrentCount == 0 ? 1 : 0);

    /// <summary>
    /// Disposes the detection engine
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _semaphore?.Dispose();
            _disposed = true;
        }
        GC.SuppressFinalize(this);
    }
}
