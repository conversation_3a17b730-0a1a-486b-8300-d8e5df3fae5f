using System.Net;
using Ngp.Communication.IpMonitor.Enums;

namespace Ngp.Communication.IpMonitor.Events;

/// <summary>
/// Event arguments for IP monitor errors
/// </summary>
public class IpMonitorErrorEventArgs : EventArgs
{
    /// <summary>
    /// Gets the monitor identifier
    /// </summary>
    public string MonitorId { get; }

    /// <summary>
    /// Gets the IP address related to the error (if applicable)
    /// </summary>
    public IPAddress? IpAddress { get; }

    /// <summary>
    /// Gets the detection method that caused the error
    /// </summary>
    public DetectionMethod? DetectionMethod { get; }

    /// <summary>
    /// Gets the error message
    /// </summary>
    public string ErrorMessage { get; }

    /// <summary>
    /// Gets the exception that caused the error, if any
    /// </summary>
    public Exception? Exception { get; }

    /// <summary>
    /// Gets the timestamp when the error occurred
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// Gets whether this error should trigger a monitor restart
    /// </summary>
    public bool ShouldRestart { get; }

    /// <summary>
    /// Gets the error severity level
    /// </summary>
    public ErrorSeverity Severity { get; }

    /// <summary>
    /// Gets optional tag for identification
    /// </summary>
    public string? Tag { get; }

    /// <summary>
    /// Initializes a new instance of the IpMonitorErrorEventArgs class
    /// </summary>
    /// <param name="monitorId">The monitor identifier</param>
    /// <param name="errorMessage">The error message</param>
    /// <param name="ipAddress">Optional IP address related to the error</param>
    /// <param name="detectionMethod">Optional detection method that caused the error</param>
    /// <param name="exception">Optional exception</param>
    /// <param name="shouldRestart">Whether this error should trigger a monitor restart</param>
    /// <param name="severity">Error severity level</param>
    /// <param name="tag">Optional tag for identification</param>
    public IpMonitorErrorEventArgs(
        string monitorId,
        string errorMessage,
        IPAddress? ipAddress = null,
        DetectionMethod? detectionMethod = null,
        Exception? exception = null,
        bool shouldRestart = false,
        ErrorSeverity severity = ErrorSeverity.Warning,
        string? tag = null)
    {
        MonitorId = monitorId;
        ErrorMessage = errorMessage;
        IpAddress = ipAddress;
        DetectionMethod = detectionMethod;
        Exception = exception;
        ShouldRestart = shouldRestart;
        Severity = severity;
        Tag = tag;
        Timestamp = DateTime.UtcNow;
    }

    /// <summary>
    /// Gets a formatted error message
    /// </summary>
    /// <returns>Formatted error message</returns>
    public string GetFormattedErrorMessage()
    {
        var parts = new List<string> { $"Monitor {MonitorId}" };
        
        if (IpAddress != null)
        {
            parts.Add($"IP {IpAddress}");
        }
        
        if (DetectionMethod.HasValue)
        {
            parts.Add($"Method {DetectionMethod}");
        }
        
        if (!string.IsNullOrEmpty(Tag))
        {
            parts.Add($"Tag {Tag}");
        }
        
        return $"[{Severity}] {string.Join(" | ", parts)}: {ErrorMessage}";
    }
}

/// <summary>
/// Represents error severity levels
/// </summary>
public enum ErrorSeverity
{
    /// <summary>
    /// Information level
    /// </summary>
    Information = 0,

    /// <summary>
    /// Warning level
    /// </summary>
    Warning = 1,

    /// <summary>
    /// Error level
    /// </summary>
    Error = 2,

    /// <summary>
    /// Critical error level
    /// </summary>
    Critical = 3
}
