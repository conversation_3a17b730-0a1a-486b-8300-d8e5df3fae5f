using System.Net;
using Ngp.Communication.IpMonitor.Enums;
using Ngp.Communication.IpMonitor.Models;

namespace Ngp.Communication.IpMonitor.Events;

/// <summary>
/// Event arguments for IP status changes
/// </summary>
public class IpStatusChangedEventArgs : EventArgs
{
    /// <summary>
    /// Gets the monitor identifier
    /// </summary>
    public string MonitorId { get; }

    /// <summary>
    /// Gets the IP address that changed status
    /// </summary>
    public IPAddress IpAddress { get; }

    /// <summary>
    /// Gets the previous status
    /// </summary>
    public IpStatus PreviousStatus { get; }

    /// <summary>
    /// Gets the current status
    /// </summary>
    public IpStatus CurrentStatus { get; }

    /// <summary>
    /// Gets the timestamp when the status change occurred
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// Gets the response time in milliseconds (if applicable)
    /// </summary>
    public long ResponseTimeMs { get; }

    /// <summary>
    /// Gets the monitoring target details
    /// </summary>
    public IpMonitorTarget Target { get; }

    /// <summary>
    /// Gets optional tag for identification
    /// </summary>
    public string? Tag { get; }

    /// <summary>
    /// Gets optional description
    /// </summary>
    public string? Description { get; }

    /// <summary>
    /// Initializes a new instance of the IpStatusChangedEventArgs class
    /// </summary>
    /// <param name="monitorId">The monitor identifier</param>
    /// <param name="target">The monitoring target</param>
    /// <param name="previousStatus">The previous status</param>
    /// <param name="currentStatus">The current status</param>
    /// <param name="responseTimeMs">The response time in milliseconds</param>
    public IpStatusChangedEventArgs(
        string monitorId,
        IpMonitorTarget target,
        IpStatus previousStatus,
        IpStatus currentStatus,
        long responseTimeMs = 0)
    {
        MonitorId = monitorId;
        Target = target.Clone();
        IpAddress = target.IpAddress;
        PreviousStatus = previousStatus;
        CurrentStatus = currentStatus;
        ResponseTimeMs = responseTimeMs;
        Timestamp = DateTime.UtcNow;
        Tag = target.Tag;
        Description = target.Description;
    }

    /// <summary>
    /// Gets whether the status changed from offline to online
    /// </summary>
    public bool IsComingOnline => PreviousStatus == IpStatus.Offline && CurrentStatus == IpStatus.Online;

    /// <summary>
    /// Gets whether the status changed from online to offline
    /// </summary>
    public bool IsGoingOffline => PreviousStatus == IpStatus.Online && CurrentStatus == IpStatus.Offline;

    /// <summary>
    /// Gets whether this is the first status determination
    /// </summary>
    public bool IsInitialStatus => PreviousStatus == IpStatus.Unknown;

    /// <summary>
    /// Gets a summary of the status change
    /// </summary>
    /// <returns>String describing the status change</returns>
    public string GetStatusChangeSummary()
    {
        var ipStr = IpAddress.ToString();
        var tagStr = !string.IsNullOrEmpty(Tag) ? $" ({Tag})" : "";
        
        if (IsInitialStatus)
        {
            return $"IP {ipStr}{tagStr} initial status: {CurrentStatus}";
        }
        
        if (IsComingOnline)
        {
            return $"IP {ipStr}{tagStr} came online (response: {ResponseTimeMs}ms)";
        }
        
        if (IsGoingOffline)
        {
            return $"IP {ipStr}{tagStr} went offline";
        }
        
        return $"IP {ipStr}{tagStr} status: {PreviousStatus} -> {CurrentStatus}";
    }
}
