using Microsoft.Extensions.Logging;

namespace Ngp.Communication.IpMonitor.Utilities;

/// <summary>
/// Logger adapter to convert ILogger to ILogger&lt;T&gt;
/// </summary>
/// <typeparam name="T">Target type for the logger</typeparam>
public class LoggerAdapter<T> : ILogger<T>
{
    private readonly ILogger _logger;

    /// <summary>
    /// Initializes a new instance of the LoggerAdapter class
    /// </summary>
    /// <param name="logger">Source logger</param>
    public LoggerAdapter(ILogger logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Begins a logical operation scope
    /// </summary>
    /// <typeparam name="TState">State type</typeparam>
    /// <param name="state">State object</param>
    /// <returns>Disposable scope</returns>
    public IDisposable? BeginScope<TState>(TState state) where TState : notnull
    {
        return _logger.BeginScope(state);
    }

    /// <summary>
    /// Checks if the given log level is enabled
    /// </summary>
    /// <param name="logLevel">Log level to check</param>
    /// <returns>True if enabled</returns>
    public bool IsEnabled(LogLevel logLevel)
    {
        return _logger.IsEnabled(logLevel);
    }

    /// <summary>
    /// Writes a log entry
    /// </summary>
    /// <typeparam name="TState">State type</typeparam>
    /// <param name="logLevel">Log level</param>
    /// <param name="eventId">Event ID</param>
    /// <param name="state">State object</param>
    /// <param name="exception">Exception</param>
    /// <param name="formatter">Formatter function</param>
    public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
    {
        _logger.Log(logLevel, eventId, state, exception, formatter);
    }
}
