using System.Net;

namespace Ngp.Communication.IpMonitor.Utilities;

/// <summary>
/// Utility class for parsing IP addresses and IP ranges
/// </summary>
public static class IpRangeParser
{
    /// <summary>
    /// Parses a single IP address string
    /// </summary>
    /// <param name="ipString">IP address string</param>
    /// <param name="ipAddress">Parsed IP address</param>
    /// <returns>True if parsing was successful</returns>
    public static bool TryParseIpAddress(string ipString, out IPAddress ipAddress)
    {
        ipAddress = IPAddress.None;

        if (string.IsNullOrWhiteSpace(ipString))
            return false;

        return IPAddress.TryParse(ipString.Trim(), out ipAddress!);
    }

    /// <summary>
    /// Parses an IP range string and returns all IP addresses in the range
    /// Supports formats like "***********~*************" or "***********~***************"
    /// </summary>
    /// <param name="ipRangeString">IP range string</param>
    /// <param name="ipAddresses">List of IP addresses in the range</param>
    /// <returns>True if parsing was successful</returns>
    public static bool TryParseIpRange(string ipRangeString, out List<IPAddress> ipAddresses)
    {
        ipAddresses = new List<IPAddress>();
        
        if (string.IsNullOrWhiteSpace(ipRangeString))
            return false;

        // Check if it's a range (contains ~)
        if (ipRangeString.Contains('~'))
        {
            var parts = ipRangeString.Split('~', StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length != 2)
                return false;

            if (!TryParseIpAddress(parts[0], out var startIp) || 
                !TryParseIpAddress(parts[1], out var endIp))
                return false;

            return TryGenerateIpRange(startIp, endIp, out ipAddresses);
        }
        else
        {
            // Single IP address
            if (TryParseIpAddress(ipRangeString, out var singleIp))
            {
                ipAddresses.Add(singleIp);
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// Generates a list of IP addresses between start and end IP (inclusive)
    /// </summary>
    /// <param name="startIp">Start IP address</param>
    /// <param name="endIp">End IP address</param>
    /// <param name="ipAddresses">Generated IP addresses</param>
    /// <returns>True if generation was successful</returns>
    public static bool TryGenerateIpRange(IPAddress startIp, IPAddress endIp, out List<IPAddress> ipAddresses)
    {
        ipAddresses = new List<IPAddress>();

        try
        {
            // Only support IPv4 for now
            if (startIp.AddressFamily != System.Net.Sockets.AddressFamily.InterNetwork ||
                endIp.AddressFamily != System.Net.Sockets.AddressFamily.InterNetwork)
                return false;

            var startBytes = startIp.GetAddressBytes();
            var endBytes = endIp.GetAddressBytes();

            // Convert to uint for easier comparison and increment
            var startUint = BitConverter.ToUInt32(startBytes.Reverse().ToArray(), 0);
            var endUint = BitConverter.ToUInt32(endBytes.Reverse().ToArray(), 0);

            if (startUint > endUint)
                return false;

            // Limit the range to prevent memory issues (max 65536 IPs)
            if (endUint - startUint > 65535)
                return false;

            for (var current = startUint; current <= endUint; current++)
            {
                var currentBytes = BitConverter.GetBytes(current).Reverse().ToArray();
                ipAddresses.Add(new IPAddress(currentBytes));
            }

            return true;
        }
        catch
        {
            ipAddresses.Clear();
            return false;
        }
    }

    /// <summary>
    /// Validates if an IP range string is in correct format
    /// </summary>
    /// <param name="ipRangeString">IP range string to validate</param>
    /// <returns>True if format is valid</returns>
    public static bool IsValidIpRangeFormat(string ipRangeString)
    {
        return TryParseIpRange(ipRangeString, out _);
    }

    /// <summary>
    /// Gets the count of IP addresses in a range without generating the full list
    /// </summary>
    /// <param name="startIp">Start IP address</param>
    /// <param name="endIp">End IP address</param>
    /// <returns>Number of IP addresses in the range, or -1 if invalid</returns>
    public static long GetIpRangeCount(IPAddress startIp, IPAddress endIp)
    {
        try
        {
            if (startIp.AddressFamily != System.Net.Sockets.AddressFamily.InterNetwork ||
                endIp.AddressFamily != System.Net.Sockets.AddressFamily.InterNetwork)
                return -1;

            var startBytes = startIp.GetAddressBytes();
            var endBytes = endIp.GetAddressBytes();

            var startUint = BitConverter.ToUInt32(startBytes.Reverse().ToArray(), 0);
            var endUint = BitConverter.ToUInt32(endBytes.Reverse().ToArray(), 0);

            if (startUint > endUint)
                return -1;

            return (long)(endUint - startUint + 1);
        }
        catch
        {
            return -1;
        }
    }

    /// <summary>
    /// Gets the count of IP addresses in a range string without generating the full list
    /// </summary>
    /// <param name="ipRangeString">IP range string</param>
    /// <returns>Number of IP addresses in the range, or -1 if invalid</returns>
    public static long GetIpRangeCount(string ipRangeString)
    {
        if (string.IsNullOrWhiteSpace(ipRangeString))
            return -1;

        if (ipRangeString.Contains('~'))
        {
            var parts = ipRangeString.Split('~', StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length != 2)
                return -1;

            if (!TryParseIpAddress(parts[0], out var startIp) || 
                !TryParseIpAddress(parts[1], out var endIp))
                return -1;

            return GetIpRangeCount(startIp, endIp);
        }
        else
        {
            // Single IP address
            return TryParseIpAddress(ipRangeString, out _) ? 1 : -1;
        }
    }
}
