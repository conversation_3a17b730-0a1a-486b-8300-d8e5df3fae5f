using System.Net;
using Ngp.Communication.IpMonitor.Events;
using Ngp.Communication.IpMonitor.Models;

namespace Ngp.Communication.IpMonitor.Interfaces;

/// <summary>
/// Interface for IP monitoring functionality
/// </summary>
public interface IIpMonitor : IDisposable
{
    /// <summary>
    /// Event raised when an IP status changes
    /// </summary>
    event EventHandler<IpStatusChangedEventArgs>? IpStatusChanged;

    /// <summary>
    /// Event raised when a monitoring error occurs
    /// </summary>
    event EventHandler<IpMonitorErrorEventArgs>? MonitorError;

    /// <summary>
    /// Gets the monitor configuration
    /// </summary>
    IpMonitorConfiguration Configuration { get; }

    /// <summary>
    /// Gets the list of monitored IP targets
    /// </summary>
    IReadOnlyList<IpMonitorTarget> MonitoredTargets { get; }

    /// <summary>
    /// Gets whether the monitor is currently running
    /// </summary>
    bool IsRunning { get; }

    /// <summary>
    /// Gets the total number of monitored IPs
    /// </summary>
    int TotalTargetCount { get; }

    /// <summary>
    /// Gets the number of online IPs
    /// </summary>
    int OnlineCount { get; }

    /// <summary>
    /// Gets the number of offline IPs
    /// </summary>
    int OfflineCount { get; }

    /// <summary>
    /// Starts the IP monitoring
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task StartAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Stops the IP monitoring
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task StopAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Adds a single IP address to monitor
    /// </summary>
    /// <param name="ipAddress">IP address to monitor</param>
    /// <param name="tag">Optional tag for identification</param>
    /// <param name="description">Optional description</param>
    /// <returns>True if added successfully</returns>
    bool AddIpAddress(IPAddress ipAddress, string? tag = null, string? description = null);

    /// <summary>
    /// Adds a single IP address to monitor
    /// </summary>
    /// <param name="ipAddress">IP address string to monitor</param>
    /// <param name="tag">Optional tag for identification</param>
    /// <param name="description">Optional description</param>
    /// <returns>True if added successfully</returns>
    bool AddIpAddress(string ipAddress, string? tag = null, string? description = null);

    /// <summary>
    /// Adds an IP range to monitor
    /// </summary>
    /// <param name="startIp">Start IP address</param>
    /// <param name="endIp">End IP address</param>
    /// <param name="tag">Optional tag for identification</param>
    /// <param name="description">Optional description</param>
    /// <returns>Number of IPs added</returns>
    int AddIpRange(IPAddress startIp, IPAddress endIp, string? tag = null, string? description = null);

    /// <summary>
    /// Adds an IP range to monitor using string format (e.g., "***********~*************")
    /// </summary>
    /// <param name="ipRange">IP range string</param>
    /// <param name="tag">Optional tag for identification</param>
    /// <param name="description">Optional description</param>
    /// <returns>Number of IPs added</returns>
    int AddIpRange(string ipRange, string? tag = null, string? description = null);

    /// <summary>
    /// Removes an IP address from monitoring
    /// </summary>
    /// <param name="ipAddress">IP address to remove</param>
    /// <returns>True if removed successfully</returns>
    bool RemoveIpAddress(IPAddress ipAddress);

    /// <summary>
    /// Removes an IP address from monitoring
    /// </summary>
    /// <param name="ipAddress">IP address string to remove</param>
    /// <returns>True if removed successfully</returns>
    bool RemoveIpAddress(string ipAddress);

    /// <summary>
    /// Clears all monitored IP addresses
    /// </summary>
    void ClearAllIpAddresses();

    /// <summary>
    /// Gets the current status of a specific IP
    /// </summary>
    /// <param name="ipAddress">IP address to check</param>
    /// <returns>Current target information, or null if not found</returns>
    IpMonitorTarget? GetIpStatus(IPAddress ipAddress);

    /// <summary>
    /// Gets the current status of a specific IP
    /// </summary>
    /// <param name="ipAddress">IP address string to check</param>
    /// <returns>Current target information, or null if not found</returns>
    IpMonitorTarget? GetIpStatus(string ipAddress);

    /// <summary>
    /// Forces an immediate ping test for a specific IP
    /// </summary>
    /// <param name="ipAddress">IP address to test</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Ping result</returns>
    Task<PingResult> PingAsync(IPAddress ipAddress, CancellationToken cancellationToken = default);

    /// <summary>
    /// Forces an immediate ping test for a specific IP
    /// </summary>
    /// <param name="ipAddress">IP address string to test</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Ping result</returns>
    Task<PingResult> PingAsync(string ipAddress, CancellationToken cancellationToken = default);
}
