using System.Net;
using Ngp.Communication.IpMonitor.Enums;

namespace Ngp.Communication.IpMonitor.Interfaces;

/// <summary>
/// Interface for building IP monitor instances using Fluent API
/// </summary>
public interface IIpMonitorBuilder
{
    /// <summary>
    /// Sets the monitor identifier
    /// </summary>
    /// <param name="id">Monitor identifier</param>
    /// <returns>The builder instance</returns>
    IIpMonitorBuilder WithId(string id);

    /// <summary>
    /// Sets the monitoring mode
    /// </summary>
    /// <param name="mode">Monitoring mode</param>
    /// <returns>The builder instance</returns>
    IIpMonitorBuilder WithMonitoringMode(MonitoringMode mode);

    /// <summary>
    /// Sets the detection method
    /// </summary>
    /// <param name="method">Detection method</param>
    /// <returns>The builder instance</returns>
    IIpMonitorBuilder WithDetectionMethod(DetectionMethod method);

    /// <summary>
    /// Sets the maximum concurrency for ping operations
    /// </summary>
    /// <param name="maxConcurrency">Maximum concurrent operations</param>
    /// <returns>The builder instance</returns>
    IIpMonitorBuilder WithMaxConcurrency(int maxConcurrency);

    /// <summary>
    /// Sets the ping timeout
    /// </summary>
    /// <param name="timeoutMs">Timeout in milliseconds</param>
    /// <returns>The builder instance</returns>
    IIpMonitorBuilder WithTimeout(int timeoutMs);

    /// <summary>
    /// Sets the polling interval
    /// </summary>
    /// <param name="intervalMs">Polling interval in milliseconds</param>
    /// <returns>The builder instance</returns>
    IIpMonitorBuilder WithPollingInterval(int intervalMs);

    /// <summary>
    /// Sets the maintenance time for Simple monitoring mode
    /// </summary>
    /// <param name="maintenanceTimeMs">Maintenance time in milliseconds</param>
    /// <returns>The builder instance</returns>
    IIpMonitorBuilder WithMaintenanceTime(int maintenanceTimeMs);

    /// <summary>
    /// Sets the retry policy for Normal monitoring mode
    /// </summary>
    /// <param name="retryCount">Number of retries</param>
    /// <param name="retryDelayMs">Delay between retries in milliseconds</param>
    /// <returns>The builder instance</returns>
    IIpMonitorBuilder WithRetryPolicy(int retryCount, int retryDelayMs);

    /// <summary>
    /// Sets the consecutive success count required for Normal monitoring mode
    /// </summary>
    /// <param name="successCount">Consecutive success count</param>
    /// <returns>The builder instance</returns>
    IIpMonitorBuilder WithConsecutiveSuccessCount(int successCount);

    /// <summary>
    /// Adds a single IP address to monitor
    /// </summary>
    /// <param name="ipAddress">IP address to monitor</param>
    /// <param name="tag">Optional tag for identification</param>
    /// <param name="description">Optional description</param>
    /// <returns>The builder instance</returns>
    IIpMonitorBuilder AddIpAddress(IPAddress ipAddress, string? tag = null, string? description = null);

    /// <summary>
    /// Adds a single IP address to monitor
    /// </summary>
    /// <param name="ipAddress">IP address string to monitor</param>
    /// <param name="tag">Optional tag for identification</param>
    /// <param name="description">Optional description</param>
    /// <returns>The builder instance</returns>
    IIpMonitorBuilder AddIpAddress(string ipAddress, string? tag = null, string? description = null);

    /// <summary>
    /// Adds an IP range to monitor
    /// </summary>
    /// <param name="startIp">Start IP address</param>
    /// <param name="endIp">End IP address</param>
    /// <param name="tag">Optional tag for identification</param>
    /// <param name="description">Optional description</param>
    /// <returns>The builder instance</returns>
    IIpMonitorBuilder AddIpRange(IPAddress startIp, IPAddress endIp, string? tag = null, string? description = null);

    /// <summary>
    /// Adds an IP range to monitor using string format (e.g., "***********~*************")
    /// </summary>
    /// <param name="ipRange">IP range string</param>
    /// <param name="tag">Optional tag for identification</param>
    /// <param name="description">Optional description</param>
    /// <returns>The builder instance</returns>
    IIpMonitorBuilder AddIpRange(string ipRange, string? tag = null, string? description = null);

    /// <summary>
    /// Adds multiple IP addresses to monitor
    /// </summary>
    /// <param name="ipAddresses">Collection of IP addresses</param>
    /// <param name="tag">Optional tag for identification</param>
    /// <param name="description">Optional description</param>
    /// <returns>The builder instance</returns>
    IIpMonitorBuilder AddIpAddresses(IEnumerable<IPAddress> ipAddresses, string? tag = null, string? description = null);

    /// <summary>
    /// Adds multiple IP addresses to monitor
    /// </summary>
    /// <param name="ipAddresses">Collection of IP address strings</param>
    /// <param name="tag">Optional tag for identification</param>
    /// <param name="description">Optional description</param>
    /// <returns>The builder instance</returns>
    IIpMonitorBuilder AddIpAddresses(IEnumerable<string> ipAddresses, string? tag = null, string? description = null);

    /// <summary>
    /// Builds the IP monitor instance
    /// </summary>
    /// <returns>Configured IP monitor instance</returns>
    IIpMonitor Build();
}
