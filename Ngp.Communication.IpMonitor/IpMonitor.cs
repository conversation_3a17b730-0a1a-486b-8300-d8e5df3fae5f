using System.Collections.Concurrent;
using System.Net;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Ngp.Communication.IpMonitor.Detection;
using Ngp.Communication.IpMonitor.Enums;
using Ngp.Communication.IpMonitor.Events;
using Ngp.Communication.IpMonitor.Interfaces;
using Ngp.Communication.IpMonitor.Models;
using Ngp.Communication.IpMonitor.Monitoring;
using Ngp.Communication.IpMonitor.Utilities;

namespace Ngp.Communication.IpMonitor;

/// <summary>
/// Main implementation of the IP Monitor
/// </summary>
public class IpMonitor : IIpMonitor
{
    private readonly IpMonitorConfiguration _configuration;
    private readonly ConcurrentDictionary<IPAddress, IpMonitorTarget> _targets;
    private readonly ILogger<IpMonitor> _logger;
    
    private readonly MonitoringModeProcessor _modeProcessor;
    private readonly IcmpDetectionEngine? _icmpEngine;
    private readonly SemaphoreSlim _operationSemaphore;
    
    private Task? _monitoringTask;
    private CancellationTokenSource? _cancellationTokenSource;
    private bool _disposed = false;
    private bool _isRunning = false;

    /// <summary>
    /// Event raised when an IP status changes
    /// </summary>
    public event EventHandler<IpStatusChangedEventArgs>? IpStatusChanged;

    /// <summary>
    /// Event raised when a monitoring error occurs
    /// </summary>
    public event EventHandler<IpMonitorErrorEventArgs>? MonitorError;

    /// <summary>
    /// Gets the monitor configuration
    /// </summary>
    public IpMonitorConfiguration Configuration => _configuration;

    /// <summary>
    /// Gets the list of monitored IP targets
    /// </summary>
    public IReadOnlyList<IpMonitorTarget> MonitoredTargets => 
        _targets.Values.Select(t => t.Clone()).ToList();

    /// <summary>
    /// Gets whether the monitor is currently running
    /// </summary>
    public bool IsRunning => _isRunning;

    /// <summary>
    /// Gets the total number of monitored IPs
    /// </summary>
    public int TotalTargetCount => _targets.Count;

    /// <summary>
    /// Gets the number of online IPs
    /// </summary>
    public int OnlineCount => _targets.Values.Count(t => t.CurrentStatus == IpStatus.Online);

    /// <summary>
    /// Gets the number of offline IPs
    /// </summary>
    public int OfflineCount => _targets.Values.Count(t => t.CurrentStatus == IpStatus.Offline);

    /// <summary>
    /// Initializes a new instance of the IpMonitor class
    /// </summary>
    /// <param name="configuration">Monitor configuration</param>
    /// <param name="targets">Initial targets to monitor</param>
    /// <param name="logger">Optional logger instance</param>
    public IpMonitor(IpMonitorConfiguration configuration, IEnumerable<IpMonitorTarget> targets, ILogger<IpMonitor>? logger = null)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? NullLogger<IpMonitor>.Instance;
        
        if (!_configuration.IsValid())
            throw new ArgumentException("Invalid configuration", nameof(configuration));

        _targets = new ConcurrentDictionary<IPAddress, IpMonitorTarget>();
        foreach (var target in targets ?? Enumerable.Empty<IpMonitorTarget>())
        {
            _targets.TryAdd(target.IpAddress, target);
        }

        _modeProcessor = new MonitoringModeProcessor(_configuration, 
            logger != null ? new LoggerAdapter<MonitoringModeProcessor>(logger) : null);

        _operationSemaphore = new SemaphoreSlim(1, 1);

        // Initialize detection engines based on configuration
        switch (_configuration.DetectionMethod)
        {
            case DetectionMethod.IcmpPing:
                _icmpEngine = new IcmpDetectionEngine(_configuration.MaxConcurrency, _configuration.TimeoutMs,
                    logger != null ? new LoggerAdapter<IcmpDetectionEngine>(logger) : null);
                break;
            case DetectionMethod.FPing:
                // FPing implementation would go here
                _logger.LogWarning("FPing detection method not yet implemented, falling back to ICMP");
                _icmpEngine = new IcmpDetectionEngine(_configuration.MaxConcurrency, _configuration.TimeoutMs,
                    logger != null ? new LoggerAdapter<IcmpDetectionEngine>(logger) : null);
                break;
        }

        _logger.LogInformation("IpMonitor initialized with {TargetCount} targets using {DetectionMethod} detection", 
            _targets.Count, _configuration.DetectionMethod);
    }

    /// <summary>
    /// Starts the IP monitoring
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(IpMonitor));

        await _operationSemaphore.WaitAsync(cancellationToken);
        try
        {
            if (_isRunning)
            {
                _logger.LogWarning("Monitor {MonitorId} is already running", _configuration.Id);
                return;
            }

            if (_targets.Count == 0)
            {
                throw new InvalidOperationException("No IP addresses to monitor");
            }

            _cancellationTokenSource = new CancellationTokenSource();
            var combinedToken = CancellationTokenSource.CreateLinkedTokenSource(
                cancellationToken, _cancellationTokenSource.Token).Token;

            _monitoringTask = MonitoringLoopAsync(combinedToken);
            _isRunning = true;

            _logger.LogInformation("IP Monitor {MonitorId} started successfully with {TargetCount} targets", 
                _configuration.Id, _targets.Count);
        }
        finally
        {
            _operationSemaphore.Release();
        }
    }

    /// <summary>
    /// Stops the IP monitoring
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            return;

        await _operationSemaphore.WaitAsync(cancellationToken);
        try
        {
            if (!_isRunning)
            {
                _logger.LogWarning("Monitor {MonitorId} is not running", _configuration.Id);
                return;
            }

            _cancellationTokenSource?.Cancel();

            if (_monitoringTask != null)
            {
                try
                {
                    await _monitoringTask;
                }
                catch (OperationCanceledException)
                {
                    // Expected when cancellation is requested
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred while stopping monitor {MonitorId}", _configuration.Id);
                }
            }

            _isRunning = false;
            _logger.LogInformation("IP Monitor {MonitorId} stopped successfully", _configuration.Id);
        }
        finally
        {
            _operationSemaphore.Release();
        }
    }

    /// <summary>
    /// Adds a single IP address to monitor
    /// </summary>
    /// <param name="ipAddress">IP address to monitor</param>
    /// <param name="tag">Optional tag for identification</param>
    /// <param name="description">Optional description</param>
    /// <returns>True if added successfully</returns>
    public bool AddIpAddress(IPAddress ipAddress, string? tag = null, string? description = null)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(IpMonitor));

        if (ipAddress == null)
            return false;

        var target = new IpMonitorTarget
        {
            IpAddress = ipAddress,
            Tag = tag,
            Description = description
        };

        var added = _targets.TryAdd(ipAddress, target);
        if (added)
        {
            _logger.LogDebug("Added IP {IpAddress} to monitor {MonitorId}", ipAddress, _configuration.Id);
        }

        return added;
    }

    /// <summary>
    /// Adds a single IP address to monitor
    /// </summary>
    /// <param name="ipAddress">IP address string to monitor</param>
    /// <param name="tag">Optional tag for identification</param>
    /// <param name="description">Optional description</param>
    /// <returns>True if added successfully</returns>
    public bool AddIpAddress(string ipAddress, string? tag = null, string? description = null)
    {
        if (!IpRangeParser.TryParseIpAddress(ipAddress, out var parsedIp))
            return false;

        return AddIpAddress(parsedIp, tag, description);
    }

    /// <summary>
    /// Adds an IP range to monitor
    /// </summary>
    /// <param name="startIp">Start IP address</param>
    /// <param name="endIp">End IP address</param>
    /// <param name="tag">Optional tag for identification</param>
    /// <param name="description">Optional description</param>
    /// <returns>Number of IPs added</returns>
    public int AddIpRange(IPAddress startIp, IPAddress endIp, string? tag = null, string? description = null)
    {
        if (!IpRangeParser.TryGenerateIpRange(startIp, endIp, out var ipAddresses))
            return 0;

        int addedCount = 0;
        foreach (var ip in ipAddresses)
        {
            if (AddIpAddress(ip, tag, description))
                addedCount++;
        }

        return addedCount;
    }

    /// <summary>
    /// Adds an IP range to monitor using string format
    /// </summary>
    /// <param name="ipRange">IP range string</param>
    /// <param name="tag">Optional tag for identification</param>
    /// <param name="description">Optional description</param>
    /// <returns>Number of IPs added</returns>
    public int AddIpRange(string ipRange, string? tag = null, string? description = null)
    {
        if (!IpRangeParser.TryParseIpRange(ipRange, out var ipAddresses))
            return 0;

        int addedCount = 0;
        foreach (var ip in ipAddresses)
        {
            if (AddIpAddress(ip, tag, description))
                addedCount++;
        }

        return addedCount;
    }

    /// <summary>
    /// Removes an IP address from monitoring
    /// </summary>
    /// <param name="ipAddress">IP address to remove</param>
    /// <returns>True if removed successfully</returns>
    public bool RemoveIpAddress(IPAddress ipAddress)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(IpMonitor));

        var removed = _targets.TryRemove(ipAddress, out _);
        if (removed)
        {
            _logger.LogDebug("Removed IP {IpAddress} from monitor {MonitorId}", ipAddress, _configuration.Id);
        }

        return removed;
    }

    /// <summary>
    /// Removes an IP address from monitoring
    /// </summary>
    /// <param name="ipAddress">IP address string to remove</param>
    /// <returns>True if removed successfully</returns>
    public bool RemoveIpAddress(string ipAddress)
    {
        if (!IpRangeParser.TryParseIpAddress(ipAddress, out var parsedIp))
            return false;

        return RemoveIpAddress(parsedIp);
    }

    /// <summary>
    /// Clears all monitored IP addresses
    /// </summary>
    public void ClearAllIpAddresses()
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(IpMonitor));

        var count = _targets.Count;
        _targets.Clear();
        _logger.LogDebug("Cleared {Count} IP addresses from monitor {MonitorId}", count, _configuration.Id);
    }

    /// <summary>
    /// Gets the current status of a specific IP
    /// </summary>
    /// <param name="ipAddress">IP address to check</param>
    /// <returns>Current target information, or null if not found</returns>
    public IpMonitorTarget? GetIpStatus(IPAddress ipAddress)
    {
        return _targets.TryGetValue(ipAddress, out var target) ? target.Clone() : null;
    }

    /// <summary>
    /// Gets the current status of a specific IP
    /// </summary>
    /// <param name="ipAddress">IP address string to check</param>
    /// <returns>Current target information, or null if not found</returns>
    public IpMonitorTarget? GetIpStatus(string ipAddress)
    {
        if (!IpRangeParser.TryParseIpAddress(ipAddress, out var parsedIp))
            return null;

        return GetIpStatus(parsedIp);
    }

    /// <summary>
    /// Forces an immediate ping test for a specific IP
    /// </summary>
    /// <param name="ipAddress">IP address to test</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Ping result</returns>
    public async Task<PingResult> PingAsync(IPAddress ipAddress, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(IpMonitor));

        return await PerformPingAsync(ipAddress, cancellationToken);
    }

    /// <summary>
    /// Forces an immediate ping test for a specific IP
    /// </summary>
    /// <param name="ipAddress">IP address string to test</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Ping result</returns>
    public async Task<PingResult> PingAsync(string ipAddress, CancellationToken cancellationToken = default)
    {
        if (!IpRangeParser.TryParseIpAddress(ipAddress, out var parsedIp))
        {
            return PingResult.Failure(IPAddress.None, System.Net.NetworkInformation.IPStatus.Unknown,
                $"Invalid IP address: {ipAddress}");
        }

        return await PingAsync(parsedIp, cancellationToken);
    }

    /// <summary>
    /// Main monitoring loop
    /// </summary>
    private async Task MonitoringLoopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starting monitoring loop for {MonitorId} with {TargetCount} targets",
            _configuration.Id, _targets.Count);

        try
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                var startTime = DateTime.UtcNow;

                // Ping all targets concurrently
                var pingTasks = _targets.Values.Select(async target =>
                {
                    try
                    {
                        var pingResult = await PerformPingAsync(target.IpAddress, cancellationToken);
                        await ProcessPingResultAsync(target, pingResult);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error pinging {IpAddress} in monitor {MonitorId}",
                            target.IpAddress, _configuration.Id);

                        OnMonitorError(new IpMonitorErrorEventArgs(_configuration.Id,
                            $"Ping error for {target.IpAddress}: {ex.Message}",
                            target.IpAddress, _configuration.DetectionMethod, ex));
                    }
                });

                await Task.WhenAll(pingTasks);

                // Calculate next polling time
                var elapsed = DateTime.UtcNow - startTime;
                var delay = TimeSpan.FromMilliseconds(_configuration.PollingIntervalMs) - elapsed;

                if (delay > TimeSpan.Zero)
                {
                    await Task.Delay(delay, cancellationToken);
                }
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Monitoring loop cancelled for {MonitorId}", _configuration.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Monitoring loop error for {MonitorId}", _configuration.Id);
            OnMonitorError(new IpMonitorErrorEventArgs(_configuration.Id,
                $"Monitoring loop error: {ex.Message}", exception: ex, shouldRestart: true));
        }
    }

    /// <summary>
    /// Performs a ping operation using the configured detection method
    /// </summary>
    private async Task<PingResult> PerformPingAsync(IPAddress ipAddress, CancellationToken cancellationToken)
    {
        return _configuration.DetectionMethod switch
        {
            DetectionMethod.IcmpPing => _icmpEngine != null
                ? await _icmpEngine.PingAsync(ipAddress, cancellationToken)
                : PingResult.Failure(ipAddress, System.Net.NetworkInformation.IPStatus.Unknown, "ICMP engine not initialized"),

            _ => PingResult.Failure(ipAddress, System.Net.NetworkInformation.IPStatus.Unknown, "Unsupported detection method")
        };
    }

    /// <summary>
    /// Processes a ping result and raises events if status changes
    /// </summary>
    private async Task ProcessPingResultAsync(IpMonitorTarget target, PingResult pingResult)
    {
        var statusChanged = _modeProcessor.ProcessPingResult(target, pingResult);

        if (statusChanged)
        {
            _logger.LogInformation("IP {IpAddress} status changed from {PreviousStatus} to {CurrentStatus} in monitor {MonitorId}",
                target.IpAddress, target.PreviousStatus, target.CurrentStatus, _configuration.Id);

            OnIpStatusChanged(new IpStatusChangedEventArgs(_configuration.Id, target,
                target.PreviousStatus, target.CurrentStatus, pingResult.ResponseTimeMs));
        }

        // Add retry delay for Normal mode if needed
        if (_configuration.MonitoringMode == MonitoringMode.Normal &&
            !pingResult.IsSuccess &&
            target.ConsecutiveFailureCount < _configuration.RetryCount)
        {
            await Task.Delay(_configuration.RetryDelayMs);
        }
    }

    /// <summary>
    /// Raises the IpStatusChanged event
    /// </summary>
    protected virtual void OnIpStatusChanged(IpStatusChangedEventArgs e)
    {
        IpStatusChanged?.Invoke(this, e);
    }

    /// <summary>
    /// Raises the MonitorError event
    /// </summary>
    protected virtual void OnMonitorError(IpMonitorErrorEventArgs e)
    {
        MonitorError?.Invoke(this, e);
    }

    /// <summary>
    /// Disposes the IP monitor
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            StopAsync().GetAwaiter().GetResult();

            _cancellationTokenSource?.Dispose();
            _operationSemaphore?.Dispose();
            _icmpEngine?.Dispose();

            _disposed = true;
        }
    }
}
