using System.Net;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Ngp.Communication.IpMonitor.Enums;
using Ngp.Communication.IpMonitor.Interfaces;
using Ngp.Communication.IpMonitor.Models;
using Ngp.Communication.IpMonitor.Utilities;

namespace Ngp.Communication.IpMonitor.Builders;

/// <summary>
/// Builder class for creating IP monitor instances using Fluent API
/// </summary>
public class IpMonitorBuilder : IIpMonitorBuilder
{
    private readonly IpMonitorConfiguration _configuration;
    private readonly List<IpMonitorTarget> _targets;
    private ILoggerFactory? _loggerFactory;

    /// <summary>
    /// Initializes a new instance of the IpMonitorBuilder class
    /// </summary>
    /// <param name="loggerFactory">Optional logger factory</param>
    public IpMonitorBuilder(ILoggerFactory? loggerFactory = null)
    {
        _configuration = new IpMonitorConfiguration();
        _targets = new List<IpMonitorTarget>();
        _loggerFactory = loggerFactory;
    }

    /// <summary>
    /// Sets the monitor identifier
    /// </summary>
    /// <param name="id">Monitor identifier</param>
    /// <returns>The builder instance</returns>
    public IIpMonitorBuilder WithId(string id)
    {
        if (string.IsNullOrWhiteSpace(id))
            throw new ArgumentException("Monitor ID cannot be null or empty", nameof(id));
        
        _configuration.Id = id;
        return this;
    }

    /// <summary>
    /// Sets the monitoring mode
    /// </summary>
    /// <param name="mode">Monitoring mode</param>
    /// <returns>The builder instance</returns>
    public IIpMonitorBuilder WithMonitoringMode(MonitoringMode mode)
    {
        _configuration.MonitoringMode = mode;
        return this;
    }

    /// <summary>
    /// Sets the detection method
    /// </summary>
    /// <param name="method">Detection method</param>
    /// <returns>The builder instance</returns>
    public IIpMonitorBuilder WithDetectionMethod(DetectionMethod method)
    {
        _configuration.DetectionMethod = method;
        return this;
    }

    /// <summary>
    /// Sets the maximum concurrency for ping operations
    /// </summary>
    /// <param name="maxConcurrency">Maximum concurrent operations</param>
    /// <returns>The builder instance</returns>
    public IIpMonitorBuilder WithMaxConcurrency(int maxConcurrency)
    {
        if (maxConcurrency <= 0)
            throw new ArgumentException("Max concurrency must be greater than 0", nameof(maxConcurrency));
        
        _configuration.MaxConcurrency = maxConcurrency;
        return this;
    }

    /// <summary>
    /// Sets the ping timeout
    /// </summary>
    /// <param name="timeoutMs">Timeout in milliseconds</param>
    /// <returns>The builder instance</returns>
    public IIpMonitorBuilder WithTimeout(int timeoutMs)
    {
        if (timeoutMs <= 0)
            throw new ArgumentException("Timeout must be greater than 0", nameof(timeoutMs));
        
        _configuration.TimeoutMs = timeoutMs;
        return this;
    }

    /// <summary>
    /// Sets the polling interval
    /// </summary>
    /// <param name="intervalMs">Polling interval in milliseconds</param>
    /// <returns>The builder instance</returns>
    public IIpMonitorBuilder WithPollingInterval(int intervalMs)
    {
        if (intervalMs <= 0)
            throw new ArgumentException("Polling interval must be greater than 0", nameof(intervalMs));
        
        _configuration.PollingIntervalMs = intervalMs;
        return this;
    }

    /// <summary>
    /// Sets the maintenance time for Simple monitoring mode
    /// </summary>
    /// <param name="maintenanceTimeMs">Maintenance time in milliseconds</param>
    /// <returns>The builder instance</returns>
    public IIpMonitorBuilder WithMaintenanceTime(int maintenanceTimeMs)
    {
        if (maintenanceTimeMs <= 0)
            throw new ArgumentException("Maintenance time must be greater than 0", nameof(maintenanceTimeMs));
        
        _configuration.MaintenanceTimeMs = maintenanceTimeMs;
        return this;
    }

    /// <summary>
    /// Sets the retry policy for Normal monitoring mode
    /// </summary>
    /// <param name="retryCount">Number of retries</param>
    /// <param name="retryDelayMs">Delay between retries in milliseconds</param>
    /// <returns>The builder instance</returns>
    public IIpMonitorBuilder WithRetryPolicy(int retryCount, int retryDelayMs)
    {
        if (retryCount <= 0)
            throw new ArgumentException("Retry count must be greater than 0", nameof(retryCount));
        if (retryDelayMs < 0)
            throw new ArgumentException("Retry delay cannot be negative", nameof(retryDelayMs));
        
        _configuration.RetryCount = retryCount;
        _configuration.RetryDelayMs = retryDelayMs;
        return this;
    }

    /// <summary>
    /// Sets the consecutive success count required for Normal monitoring mode
    /// </summary>
    /// <param name="successCount">Consecutive success count</param>
    /// <returns>The builder instance</returns>
    public IIpMonitorBuilder WithConsecutiveSuccessCount(int successCount)
    {
        if (successCount <= 0)
            throw new ArgumentException("Consecutive success count must be greater than 0", nameof(successCount));
        
        _configuration.ConsecutiveSuccessCount = successCount;
        return this;
    }

    /// <summary>
    /// Adds a single IP address to monitor
    /// </summary>
    /// <param name="ipAddress">IP address to monitor</param>
    /// <param name="tag">Optional tag for identification</param>
    /// <param name="description">Optional description</param>
    /// <returns>The builder instance</returns>
    public IIpMonitorBuilder AddIpAddress(IPAddress ipAddress, string? tag = null, string? description = null)
    {
        if (ipAddress == null)
            throw new ArgumentNullException(nameof(ipAddress));
        
        // Check if IP already exists
        if (_targets.Any(t => t.IpAddress.Equals(ipAddress)))
            return this; // Skip duplicate
        
        _targets.Add(new IpMonitorTarget
        {
            IpAddress = ipAddress,
            Tag = tag,
            Description = description
        });
        
        return this;
    }

    /// <summary>
    /// Adds a single IP address to monitor
    /// </summary>
    /// <param name="ipAddress">IP address string to monitor</param>
    /// <param name="tag">Optional tag for identification</param>
    /// <param name="description">Optional description</param>
    /// <returns>The builder instance</returns>
    public IIpMonitorBuilder AddIpAddress(string ipAddress, string? tag = null, string? description = null)
    {
        if (!IpRangeParser.TryParseIpAddress(ipAddress, out var parsedIp))
            throw new ArgumentException($"Invalid IP address: {ipAddress}", nameof(ipAddress));
        
        return AddIpAddress(parsedIp, tag, description);
    }

    /// <summary>
    /// Adds an IP range to monitor
    /// </summary>
    /// <param name="startIp">Start IP address</param>
    /// <param name="endIp">End IP address</param>
    /// <param name="tag">Optional tag for identification</param>
    /// <param name="description">Optional description</param>
    /// <returns>The builder instance</returns>
    public IIpMonitorBuilder AddIpRange(IPAddress startIp, IPAddress endIp, string? tag = null, string? description = null)
    {
        if (!IpRangeParser.TryGenerateIpRange(startIp, endIp, out var ipAddresses))
            throw new ArgumentException($"Invalid IP range: {startIp} to {endIp}");
        
        foreach (var ip in ipAddresses)
        {
            AddIpAddress(ip, tag, description);
        }
        
        return this;
    }

    /// <summary>
    /// Adds an IP range to monitor using string format
    /// </summary>
    /// <param name="ipRange">IP range string</param>
    /// <param name="tag">Optional tag for identification</param>
    /// <param name="description">Optional description</param>
    /// <returns>The builder instance</returns>
    public IIpMonitorBuilder AddIpRange(string ipRange, string? tag = null, string? description = null)
    {
        if (!IpRangeParser.TryParseIpRange(ipRange, out var ipAddresses))
            throw new ArgumentException($"Invalid IP range format: {ipRange}", nameof(ipRange));
        
        foreach (var ip in ipAddresses)
        {
            AddIpAddress(ip, tag, description);
        }
        
        return this;
    }

    /// <summary>
    /// Adds multiple IP addresses to monitor
    /// </summary>
    /// <param name="ipAddresses">Collection of IP addresses</param>
    /// <param name="tag">Optional tag for identification</param>
    /// <param name="description">Optional description</param>
    /// <returns>The builder instance</returns>
    public IIpMonitorBuilder AddIpAddresses(IEnumerable<IPAddress> ipAddresses, string? tag = null, string? description = null)
    {
        foreach (var ip in ipAddresses)
        {
            AddIpAddress(ip, tag, description);
        }
        
        return this;
    }

    /// <summary>
    /// Adds multiple IP addresses to monitor
    /// </summary>
    /// <param name="ipAddresses">Collection of IP address strings</param>
    /// <param name="tag">Optional tag for identification</param>
    /// <param name="description">Optional description</param>
    /// <returns>The builder instance</returns>
    public IIpMonitorBuilder AddIpAddresses(IEnumerable<string> ipAddresses, string? tag = null, string? description = null)
    {
        foreach (var ipStr in ipAddresses)
        {
            AddIpAddress(ipStr, tag, description);
        }
        
        return this;
    }

    /// <summary>
    /// Builds the IP monitor instance
    /// </summary>
    /// <returns>Configured IP monitor instance</returns>
    public IIpMonitor Build()
    {
        ValidateConfiguration();
        
        var logger = _loggerFactory?.CreateLogger<IpMonitor>();
        return new IpMonitor(_configuration, _targets, logger);
    }

    /// <summary>
    /// Validates the builder configuration
    /// </summary>
    private void ValidateConfiguration()
    {
        if (string.IsNullOrEmpty(_configuration.Id))
            throw new InvalidOperationException("Monitor ID must be set");
        
        if (!_configuration.IsValid())
            throw new InvalidOperationException("Invalid configuration");
        
        if (_targets.Count == 0)
            throw new InvalidOperationException("At least one IP address must be added");
    }
}
