using Ngp.Communication.SipMessageProxy.Examples;

namespace Ngp.Communication.SipMessageProxy.TestConsole;

class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("SIP Message Client Test Console");
        Console.WriteLine("==============================");

        try
        {
            await SimpleTest.RunSimpleTestAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }

        Console.WriteLine("\nPress any key to exit...");
        Console.ReadKey();
    }
}
