namespace Ngp.Shared.Models;

/// <summary>
/// Represents the status of a door
/// </summary>
public class DoorStatus
{
    /// <summary>
    /// Area identifier (0-255)
    /// </summary>
    public byte Area { get; init; }
    
    /// <summary>
    /// Node identifier (1-254)
    /// </summary>
    public byte NodeId { get; init; }
    
    /// <summary>
    /// Door position status (true = open, false = closed)
    /// </summary>
    public bool IsDoorOpen { get; init; }
    
    /// <summary>
    /// Lock status (true = locked, false = unlocked)
    /// </summary>
    public bool IsLocked { get; init; }
    
    /// <summary>
    /// Timestamp when status was last updated
    /// </summary>
    public DateTime LastUpdated { get; init; } = DateTime.UtcNow;
    
    /// <summary>
    /// Calculated Modbus start address for this door
    /// </summary>
    public ushort ModbusStartAddress => (ushort)((Area << 8) | NodeId);
}
