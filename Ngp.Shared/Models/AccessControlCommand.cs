namespace Ngp.Shared.Models;

/// <summary>
/// Represents a command to control access control system
/// </summary>
public class AccessControlCommand
{
    /// <summary>
    /// Area identifier (0-255)
    /// </summary>
    public byte Area { get; init; }
    
    /// <summary>
    /// Node identifier (1-254)
    /// </summary>
    public byte NodeId { get; init; }
    
    /// <summary>
    /// Command type
    /// </summary>
    public AccessControlCommandType CommandType { get; init; }
    
    /// <summary>
    /// Timestamp when command was created
    /// </summary>
    public DateTime CreatedAt { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// Types of access control commands
/// </summary>
public enum AccessControlCommandType
{
    /// <summary>
    /// Query current status
    /// </summary>
    QueryStatus,
    
    /// <summary>
    /// Unlock the door
    /// </summary>
    Unlock,
    
    /// <summary>
    /// Lock the door
    /// </summary>
    Lock
}
