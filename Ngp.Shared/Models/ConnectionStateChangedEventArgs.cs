using Ngp.Shared.Enums;

namespace Ngp.Shared.Models;

/// <summary>
/// Event arguments for connection state changes
/// </summary>
public class ConnectionStateChangedEventArgs : EventArgs
{
    /// <summary>
    /// Unique identifier for the endpoint
    /// </summary>
    public string EndpointId { get; init; } = string.Empty;
    
    /// <summary>
    /// Previous connection state
    /// </summary>
    public ConnectionState PreviousState { get; init; }
    
    /// <summary>
    /// Current connection state
    /// </summary>
    public ConnectionState CurrentState { get; init; }
    
    /// <summary>
    /// Timestamp when the state change occurred
    /// </summary>
    public DateTime Timestamp { get; init; } = DateTime.UtcNow;
    
    /// <summary>
    /// Optional error message if state change was due to error
    /// </summary>
    public string? ErrorMessage { get; init; }
}
