namespace Ngp.Shared.Models;

/// <summary>
/// Event arguments for Modbus register value changes
/// </summary>
public class ModbusRegisterValueChangedEventArgs : EventArgs
{
    /// <summary>
    /// Slave ID of the device
    /// </summary>
    public byte SlaveId { get; init; }
    
    /// <summary>
    /// Register address
    /// </summary>
    public ushort Address { get; init; }
    
    /// <summary>
    /// Previous value
    /// </summary>
    public bool PreviousValue { get; init; }
    
    /// <summary>
    /// Current value
    /// </summary>
    public bool CurrentValue { get; init; }
    
    /// <summary>
    /// Timestamp when the change occurred
    /// </summary>
    public DateTime Timestamp { get; init; } = DateTime.UtcNow;
    
    /// <summary>
    /// Type of register (Coil or DiscreteInput)
    /// </summary>
    public ModbusRegisterType RegisterType { get; init; }
}

/// <summary>
/// Types of Modbus registers
/// </summary>
public enum ModbusRegisterType
{
    /// <summary>
    /// Coil register (read/write)
    /// </summary>
    Coil,
    
    /// <summary>
    /// Discrete input register (read-only)
    /// </summary>
    DiscreteInput
}