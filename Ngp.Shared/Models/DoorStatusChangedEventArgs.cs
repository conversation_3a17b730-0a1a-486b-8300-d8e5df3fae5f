namespace Ngp.Shared.Models;

/// <summary>
/// Event arguments for door status changes
/// </summary>
public class DoorStatusChangedEventArgs : EventArgs
{
    /// <summary>
    /// Previous door status
    /// </summary>
    public DoorStatus? PreviousStatus { get; init; }
    
    /// <summary>
    /// Current door status
    /// </summary>
    public DoorStatus CurrentStatus { get; init; } = null!;
    
    /// <summary>
    /// Timestamp when the change occurred
    /// </summary>
    public DateTime Timestamp { get; init; } = DateTime.UtcNow;
}