using Microsoft.Extensions.Logging;
using Ngp.Shared.Models;

namespace Ngp.Shared.Factories;

/// <summary>
/// Factory interface for creating ModbusTcp master instances without direct project dependencies
/// </summary>
public interface IModbusTcpMasterFactory
{
    /// <summary>
    /// Create a ModbusTcp master for Soyal device
    /// </summary>
    /// <param name="endpointId">Endpoint identifier</param>
    /// <param name="host">Host address</param>
    /// <param name="port">Port number</param>
    /// <param name="loggerFactory">Logger factory</param>
    /// <returns>ModbusTcp master instance</returns>
    IModbusTcpMaster CreateSoyalModbusTcpMaster(string endpointId, string host, int port, ILoggerFactory loggerFactory);
}

/// <summary>
/// Interface for ModbusTcp master that abstracts the actual implementation
/// </summary>
public interface IModbusTcpMaster : IDisposable
{
    /// <summary>
    /// Event raised when connection state changes
    /// </summary>
    event EventHandler<ConnectionStateChangedEventArgs>? ConnectionStateChanged;

    /// <summary>
    /// Event raised when register values change
    /// </summary>
    event EventHandler<ModbusRegisterValueChangedEventArgs>? RegisterValueChanged;

    /// <summary>
    /// Start the ModbusTcp master
    /// </summary>
    Task StartAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Stop the ModbusTcp master
    /// </summary>
    Task StopAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get current value of a register
    /// </summary>
    byte[]? GetCurrentValue(byte slaveId, ushort address, ModbusFunction function);

    /// <summary>
    /// Write single coil
    /// </summary>
    Task<bool> WriteSingleCoilAsync(byte slaveId, ushort address, bool value, CancellationToken cancellationToken = default);
}

/// <summary>
/// Modbus function codes
/// </summary>
public enum ModbusFunction
{
    ReadCoils = 1,
    ReadDiscreteInputs = 2,
    ReadHoldingRegisters = 3,
    ReadInputRegisters = 4,
    WriteSingleCoil = 5,
    WriteSingleRegister = 6
}