using Ngp.Shared.Models;

namespace Ngp.Shared.Interfaces;

/// <summary>
/// Interface for ModbusTcp communication service
/// </summary>
public interface IModbusTcpService
{
    /// <summary>
    /// Event raised when connection state changes
    /// </summary>
    event EventHandler<ConnectionStateChangedEventArgs> ConnectionStateChanged;
    
    /// <summary>
    /// Event raised when register values change
    /// </summary>
    event EventHandler<ModbusRegisterValueChangedEventArgs> RegisterValueChanged;
    
    /// <summary>
    /// Start the service
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task StartAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Stop the service
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task StopAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Read discrete inputs from ModbusTcp slave
    /// </summary>
    /// <param name="slaveId">Slave ID</param>
    /// <param name="startAddress">Starting address</param>
    /// <param name="length">Number of inputs to read</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Array of boolean values</returns>
    Task<bool[]> ReadDiscreteInputsAsync(byte slaveId, ushort startAddress, ushort length, 
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Read coils from ModbusTcp slave
    /// </summary>
    /// <param name="slaveId">Slave ID</param>
    /// <param name="startAddress">Starting address</param>
    /// <param name="length">Number of coils to read</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Array of boolean values</returns>
    Task<bool[]> ReadCoilsAsync(byte slaveId, ushort startAddress, ushort length, 
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Write single coil to ModbusTcp slave
    /// </summary>
    /// <param name="slaveId">Slave ID</param>
    /// <param name="address">Coil address</param>
    /// <param name="value">Value to write</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task WriteSingleCoilAsync(byte slaveId, ushort address, bool value, 
        CancellationToken cancellationToken = default);
}