namespace Ngp.Shared.Interfaces;

/// <summary>
/// Interface for SIP message service functionality
/// </summary>
public interface ISipMessageService
{
    /// <summary>
    /// Sends a SIP message to the specified extension
    /// </summary>
    /// <param name="toExtension">Target extension</param>
    /// <param name="content">Message content</param>
    /// <param name="contentType">Content type (default: text/plain)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task with the message ID for tracking</returns>
    Task<string> SendMessageAsync(
        string toExtension,
        string content,
        string contentType = "text/plain",
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets whether the SIP service is available
    /// </summary>
    bool IsAvailable { get; }

    /// <summary>
    /// Event raised when SIP service availability changes
    /// </summary>
    event EventHandler<SipServiceAvailabilityChangedEventArgs>? AvailabilityChanged;
}

/// <summary>
/// Event arguments for SIP service availability changes
/// </summary>
public class SipServiceAvailabilityChangedEventArgs : EventArgs
{
    /// <summary>
    /// Gets whether the service is now available
    /// </summary>
    public bool IsAvailable { get; }

    /// <summary>
    /// Gets the timestamp when the availability changed
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// Gets the reason for the availability change
    /// </summary>
    public string? Reason { get; }

    /// <summary>
    /// Initializes a new instance of the SipServiceAvailabilityChangedEventArgs class
    /// </summary>
    /// <param name="isAvailable">Whether the service is available</param>
    /// <param name="reason">Reason for the change</param>
    public SipServiceAvailabilityChangedEventArgs(bool isAvailable, string? reason = null)
    {
        IsAvailable = isAvailable;
        Timestamp = DateTime.UtcNow;
        Reason = reason;
    }
}
