using Ngp.Shared.Models;

namespace Ngp.Shared.Interfaces;

/// <summary>
/// Interface for Soyal access control proxy service
/// </summary>
public interface ISoyalProxyService
{
    /// <summary>
    /// Event raised when connection state changes
    /// </summary>
    event EventHandler<ConnectionStateChangedEventArgs> ConnectionStateChanged;
    
    /// <summary>
    /// Event raised when door status changes
    /// </summary>
    event EventHandler<DoorStatusChangedEventArgs> DoorStatusChanged;
    
    /// <summary>
    /// Start the service
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task StartAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Stop the service
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task StopAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Execute access control command
    /// </summary>
    /// <param name="command">Command to execute</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task ExecuteCommandAsync(AccessControlCommand command, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get current door status
    /// </summary>
    /// <param name="area">Area identifier</param>
    /// <param name="nodeId">Node identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Current door status</returns>
    Task<DoorStatus?> GetDoorStatusAsync(byte area, byte nodeId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get all monitored door statuses
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of door statuses</returns>
    Task<IEnumerable<DoorStatus>> GetAllDoorStatusesAsync(CancellationToken cancellationToken = default);
}