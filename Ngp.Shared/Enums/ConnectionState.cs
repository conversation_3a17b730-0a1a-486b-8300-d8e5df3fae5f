namespace Ngp.Shared.Enums;

/// <summary>
/// Represents the connection state of a communication service
/// </summary>
public enum ConnectionState
{
    /// <summary>
    /// Connection is disconnected
    /// </summary>
    Disconnected,
    
    /// <summary>
    /// Connection is in progress
    /// </summary>
    Connecting,
    
    /// <summary>
    /// Connection is established and active
    /// </summary>
    Connected,
    
    /// <summary>
    /// Connection is being closed
    /// </summary>
    Disconnecting,
    
    /// <summary>
    /// Connection failed due to error
    /// </summary>
    Error
}
