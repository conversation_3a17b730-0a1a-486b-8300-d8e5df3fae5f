using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Ngp.Communication.TwentyFourDioPoller.Commands;
using Ngp.Communication.TwentyFourDioPoller.Connection;
using Ngp.Communication.TwentyFourDioPoller.Enums;
using Ngp.Communication.TwentyFourDioPoller.Events;
using Ngp.Communication.TwentyFourDioPoller.Interfaces;
using Ngp.Communication.TwentyFourDioPoller.Models;
using Ngp.Communication.TwentyFourDioPoller.Polling;

namespace Ngp.Communication.TwentyFourDioPoller;

/// <summary>
/// Main implementation of the 24Dio Poller
/// </summary>
public class TwentyFourDioPoller : ITwentyFourDioPoller
{
    private readonly EndpointConfiguration _configuration;
    private readonly ILogger<TwentyFourDioPoller> _logger;
    
    private readonly ConnectionManager _connectionManager;
    private readonly CommandProcessor _commandProcessor;
    private readonly PollingEngine _pollingEngine;
    
    private bool _disposed = false;

    /// <summary>
    /// Event raised when a connection state changes
    /// </summary>
    public event EventHandler<ConnectionStateChangedEventArgs>? ConnectionStateChanged;

    /// <summary>
    /// Event raised when DI/DO values change
    /// </summary>
    public event EventHandler<DioValueChangedEventArgs>? DioValueChanged;

    /// <summary>
    /// Event raised when an error occurs
    /// </summary>
    public event EventHandler<DioErrorEventArgs>? DioError;

    /// <summary>
    /// Gets the current connection state
    /// </summary>
    public ConnectionState ConnectionState => _connectionManager.State;

    /// <summary>
    /// Gets the endpoint configuration
    /// </summary>
    public EndpointConfiguration Configuration => _configuration;

    /// <summary>
    /// Gets the current DIO state
    /// </summary>
    public DioState? CurrentState => _pollingEngine.CurrentState;

    /// <summary>
    /// Gets whether the poller is running
    /// </summary>
    public bool IsRunning => _pollingEngine.IsRunning;

    /// <summary>
    /// Initializes a new instance of the TwentyFourDioPoller class
    /// </summary>
    /// <param name="configuration">Endpoint configuration</param>
    /// <param name="logger">Logger instance</param>
    public TwentyFourDioPoller(EndpointConfiguration configuration, ILogger<TwentyFourDioPoller>? logger = null)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? NullLogger<TwentyFourDioPoller>.Instance;

        // Initialize components with proper logging
        _connectionManager = new ConnectionManager(_configuration,
            logger != null ? new LoggerAdapter<ConnectionManager>(logger) : NullLogger<ConnectionManager>.Instance);

        _commandProcessor = new CommandProcessor(_configuration, _connectionManager,
            logger != null ? new LoggerAdapter<CommandProcessor>(logger) : NullLogger<CommandProcessor>.Instance);

        _pollingEngine = new PollingEngine(_configuration, _commandProcessor, _connectionManager,
            logger != null ? new LoggerAdapter<PollingEngine>(logger) : NullLogger<PollingEngine>.Instance);

        // Wire up events
        _connectionManager.ConnectionStateChanged += OnConnectionStateChanged;
        _connectionManager.ErrorOccurred += OnDioError;
        _commandProcessor.CommandError += OnDioError;
        _pollingEngine.DioValueChanged += OnDioValueChanged;
        _pollingEngine.PollingError += OnDioError;

        _logger.LogInformation("TwentyFourDioPoller created for endpoint {EndpointId} at {IpAddress}:{Port}",
            _configuration.Id, _configuration.IpAddress, _configuration.Port);
    }

    /// <summary>
    /// Starts the 24Dio poller
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(TwentyFourDioPoller));

        _logger.LogInformation("Starting TwentyFourDioPoller for endpoint {EndpointId}", _configuration.Id);

        try
        {
            // Start connection manager
            var connected = await _connectionManager.StartAsync(cancellationToken);
            if (!connected)
            {
                throw new InvalidOperationException($"Failed to connect to endpoint {_configuration.Id}");
            }

            // Start polling engine
            _logger.LogInformation("Starting polling engine for endpoint {EndpointId}", _configuration.Id);
            await _pollingEngine.StartAsync(cancellationToken);
            _logger.LogInformation("Polling engine started successfully for endpoint {EndpointId}", _configuration.Id);

            _logger.LogInformation("TwentyFourDioPoller started successfully for endpoint {EndpointId}", _configuration.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start TwentyFourDioPoller for endpoint {EndpointId}", _configuration.Id);
            throw;
        }
    }

    /// <summary>
    /// Stops the 24Dio poller
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed) return;

        _logger.LogInformation("Stopping TwentyFourDioPoller for endpoint {EndpointId}", _configuration.Id);

        try
        {
            // Stop polling engine first
            await _pollingEngine.StopAsync(cancellationToken);
            _logger.LogDebug("Polling engine stopped for endpoint {EndpointId}", _configuration.Id);

            // Stop connection manager
            await _connectionManager.StopAsync(cancellationToken);
            _logger.LogDebug("Connection manager stopped for endpoint {EndpointId}", _configuration.Id);

            _logger.LogInformation("TwentyFourDioPoller stopped successfully for endpoint {EndpointId}", _configuration.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping TwentyFourDioPoller for endpoint {EndpointId}", _configuration.Id);
            throw;
        }
    }

    /// <summary>
    /// Sets specific DO points to 1
    /// </summary>
    /// <param name="doStates">8-bit DO states string</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if command was executed successfully</returns>
    public async Task<bool> SetDOAsync(string doStates, CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(TwentyFourDioPoller));

        _logger.LogDebug("Setting DO states for endpoint {EndpointId}: {DoStates}", _configuration.Id, doStates);
        return await _pollingEngine.SetDOAsync(doStates, cancellationToken);
    }

    /// <summary>
    /// Clears specific DO points to 0
    /// </summary>
    /// <param name="doStates">8-bit DO states string</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if command was executed successfully</returns>
    public async Task<bool> ClearDOAsync(string doStates, CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(TwentyFourDioPoller));

        _logger.LogDebug("Clearing DO states for endpoint {EndpointId}: {DoStates}", _configuration.Id, doStates);
        return await _pollingEngine.ClearDOAsync(doStates, cancellationToken);
    }

    /// <summary>
    /// Pins all DO points to specific states
    /// </summary>
    /// <param name="doStates">8-bit DO states string</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if command was executed successfully</returns>
    public async Task<bool> PinDOAsync(string doStates, CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(TwentyFourDioPoller));

        _logger.LogDebug("Pinning DO states for endpoint {EndpointId}: {DoStates}", _configuration.Id, doStates);
        return await _pollingEngine.PinDOAsync(doStates, cancellationToken);
    }

    /// <summary>
    /// Gets the current DI states
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>24-bit DI states string</returns>
    public async Task<string?> GetDIAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(TwentyFourDioPoller));

        return await _commandProcessor.GetDIAsync(cancellationToken);
    }

    /// <summary>
    /// Gets the current DO states
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>8-bit DO states string</returns>
    public async Task<string?> GetDOAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(TwentyFourDioPoller));

        return await _commandProcessor.GetDOAsync(cancellationToken);
    }

    /// <summary>
    /// Forces a reconnection to the device
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task ForceReconnectAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(TwentyFourDioPoller));

        _logger.LogInformation("Force reconnect requested for endpoint {EndpointId}", _configuration.Id);
        await _connectionManager.ForceReconnectAsync(cancellationToken);
    }

    /// <summary>
    /// Handles connection state changes
    /// </summary>
    private void OnConnectionStateChanged(object? sender, ConnectionStateChangedEventArgs e)
    {
        ConnectionStateChanged?.Invoke(this, e);
    }

    /// <summary>
    /// Handles DIO value changes
    /// </summary>
    private void OnDioValueChanged(object? sender, DioValueChangedEventArgs e)
    {
        DioValueChanged?.Invoke(this, e);
    }

    /// <summary>
    /// Handles DIO errors
    /// </summary>
    private void OnDioError(object? sender, DioErrorEventArgs e)
    {
        DioError?.Invoke(this, e);
    }

    /// <summary>
    /// Disposes the 24Dio poller
    /// </summary>
    public void Dispose()
    {
        if (_disposed) return;

        _disposed = true;

        try
        {
            _logger.LogInformation("Disposing TwentyFourDioPoller for endpoint {EndpointId}", _configuration.Id);

            // Stop the poller
            StopAsync().GetAwaiter().GetResult();

            // Dispose components
            _pollingEngine?.Dispose();
            _commandProcessor?.Dispose();
            _connectionManager?.Dispose();

            _logger.LogInformation("TwentyFourDioPoller disposed for endpoint {EndpointId}", _configuration.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disposing TwentyFourDioPoller for endpoint {EndpointId}", _configuration.Id);
        }
    }
}

/// <summary>
/// Logger adapter to convert generic logger types
/// </summary>
/// <typeparam name="T">Target logger type</typeparam>
internal class LoggerAdapter<T> : ILogger<T>
{
    private readonly ILogger _logger;

    public LoggerAdapter(ILogger logger)
    {
        _logger = logger;
    }

    public IDisposable? BeginScope<TState>(TState state) where TState : notnull
    {
        return _logger.BeginScope(state);
    }

    public bool IsEnabled(LogLevel logLevel)
    {
        return _logger.IsEnabled(logLevel);
    }

    public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
    {
        _logger.Log(logLevel, eventId, state, exception, formatter);
    }
}
