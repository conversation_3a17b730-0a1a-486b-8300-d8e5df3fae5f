using Microsoft.Extensions.Logging;
using Ngp.Communication.TwentyFourDioPoller.Enums;
using Ngp.Communication.TwentyFourDioPoller.Events;
using Ngp.Communication.TwentyFourDioPoller.Models;
using System.Collections.Concurrent;
using System.Net.Sockets;
using System.Text;

namespace Ngp.Communication.TwentyFourDioPoller.Connection;

/// <summary>
/// Manages TCP connection to 24Dio device with automatic reconnection and packet handling
/// </summary>
public class ConnectionManager : IDisposable
{
    private readonly EndpointConfiguration _configuration;
    private readonly ILogger<ConnectionManager> _logger;
    private readonly SemaphoreSlim _connectionSemaphore;
    private readonly CancellationTokenSource _cancellationTokenSource;
    private readonly ConcurrentQueue<byte[]> _sendQueue;
    private readonly StringBuilder _receiveBuffer;

    private TcpClient? _tcpClient;
    private NetworkStream? _networkStream;
    private ConnectionState _currentState;
    private Task? _receiveTask;
    private Task? _sendTask;
    private Task? _reconnectTask;
    private DateTime _lastValidResponseTime;
    private int _invalidResponseCount;
    private bool _disposed;



    /// <summary>
    /// Event raised when connection state changes
    /// </summary>
    public event EventHandler<ConnectionStateChangedEventArgs>? ConnectionStateChanged;

    /// <summary>
    /// Event raised when data is received from the device
    /// </summary>
    public event EventHandler<string>? DataReceived;

    /// <summary>
    /// Event raised when an error occurs
    /// </summary>
    public event EventHandler<DioErrorEventArgs>? ErrorOccurred;

    /// <summary>
    /// Gets the current connection state
    /// </summary>
    public ConnectionState State => _currentState;

    /// <summary>
    /// Gets whether the connection is active
    /// </summary>
    public bool IsConnected => _currentState == ConnectionState.Connected;

    /// <summary>
    /// Initializes a new instance of the ConnectionManager class
    /// </summary>
    /// <param name="configuration">Endpoint configuration</param>
    /// <param name="logger">Logger instance</param>
    public ConnectionManager(EndpointConfiguration configuration, ILogger<ConnectionManager> logger)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        _connectionSemaphore = new SemaphoreSlim(1, 1);
        _cancellationTokenSource = new CancellationTokenSource();
        _sendQueue = new ConcurrentQueue<byte[]>();
        _receiveBuffer = new StringBuilder();
        _currentState = ConnectionState.Disconnected;
        _lastValidResponseTime = DateTime.UtcNow;
        _invalidResponseCount = 0;
    }

    /// <summary>
    /// Starts the connection manager
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task<bool> StartAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(ConnectionManager));

        await _connectionSemaphore.WaitAsync(cancellationToken);
        try
        {
            if (_currentState != ConnectionState.Disconnected)
                return IsConnected;

            _logger.LogInformation("Starting connection manager for endpoint {EndpointId}", _configuration.Id);

            // Start background tasks
            _reconnectTask = Task.Run(async () => await ReconnectLoopAsync(_cancellationTokenSource.Token), 
                _cancellationTokenSource.Token);

            return await ConnectAsync(cancellationToken);
        }
        finally
        {
            _connectionSemaphore.Release();
        }
    }

    /// <summary>
    /// Stops the connection manager
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed) return;

        await _connectionSemaphore.WaitAsync(cancellationToken);
        try
        {
            _logger.LogInformation("Stopping connection manager for endpoint {EndpointId}", _configuration.Id);

            _cancellationTokenSource.Cancel();

            await DisconnectAsync();

            // Wait for background tasks to complete
            var tasks = new List<Task>();
            if (_receiveTask != null) tasks.Add(_receiveTask);
            if (_sendTask != null) tasks.Add(_sendTask);
            if (_reconnectTask != null) tasks.Add(_reconnectTask);

            if (tasks.Count > 0)
            {
                await Task.WhenAll(tasks);
            }

            _logger.LogInformation("Connection manager stopped for endpoint {EndpointId}", _configuration.Id);
        }
        finally
        {
            _connectionSemaphore.Release();
        }
    }

    /// <summary>
    /// Sends data to the device
    /// </summary>
    /// <param name="data">Data to send</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task<bool> SendAsync(byte[] data, CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(ConnectionManager));
        if (data == null || data.Length == 0) return false;

        if (!IsConnected)
        {
            _logger.LogWarning("Cannot send data - not connected to endpoint {EndpointId}", _configuration.Id);
            return false;
        }

        _sendQueue.Enqueue(data);
        return true;
    }

    /// <summary>
    /// Forces a reconnection
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task ForceReconnectAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed) return;

        _logger.LogInformation("Force reconnect requested for endpoint {EndpointId}", _configuration.Id);

        await DisconnectAsync();

        // Reset error counters
        _invalidResponseCount = 0;
        _lastValidResponseTime = DateTime.UtcNow;

        // Trigger immediate reconnection
        await ConnectAsync(cancellationToken);
    }

    /// <summary>
    /// Establishes connection to the device
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if connected successfully</returns>
    private async Task<bool> ConnectAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed || cancellationToken.IsCancellationRequested) return false;

        try
        {
            SetConnectionState(ConnectionState.Connecting);

            _tcpClient = new TcpClient();
            _tcpClient.ReceiveTimeout = _configuration.ReceiveTimeoutMilliseconds;
            _tcpClient.SendTimeout = _configuration.ConnectionTimeoutMilliseconds;

            _logger.LogDebug("Connecting to {IpAddress}:{Port} for endpoint {EndpointId}",
                _configuration.IpAddress, _configuration.Port, _configuration.Id);

            await _tcpClient.ConnectAsync(_configuration.IpAddress, _configuration.Port, cancellationToken);

            if (!_tcpClient.Connected)
            {
                throw new InvalidOperationException("Failed to establish TCP connection");
            }

            _networkStream = _tcpClient.GetStream();

            // Start background tasks
            _receiveTask = Task.Run(async () => await ReceiveLoopAsync(_cancellationTokenSource.Token),
                _cancellationTokenSource.Token);
            _sendTask = Task.Run(async () => await SendLoopAsync(_cancellationTokenSource.Token),
                _cancellationTokenSource.Token);

            SetConnectionState(ConnectionState.Connected);
            _lastValidResponseTime = DateTime.UtcNow;
            _invalidResponseCount = 0;

            _logger.LogInformation("Successfully connected to endpoint {EndpointId} at {IpAddress}:{Port}",
                _configuration.Id, _configuration.IpAddress, _configuration.Port);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to connect to endpoint {EndpointId} at {IpAddress}:{Port}",
                _configuration.Id, _configuration.IpAddress, _configuration.Port);

            await DisconnectAsync();
            SetConnectionState(ConnectionState.Error, ex.Message, ex);

            OnErrorOccurred(new DioErrorEventArgs(_configuration.Id,
                $"Connection failed: {ex.Message}", exception: ex, shouldReconnect: true));

            return false;
        }
    }

    /// <summary>
    /// Disconnects from the device
    /// </summary>
    /// <returns>Task representing the async operation</returns>
    private async Task DisconnectAsync()
    {
        try
        {
            // Cancel background tasks
            if (_receiveTask != null && !_receiveTask.IsCompleted)
            {
                await _receiveTask;
            }

            if (_sendTask != null && !_sendTask.IsCompleted)
            {
                await _sendTask;
            }

            // Close network stream
            _networkStream?.Close();
            _networkStream?.Dispose();
            _networkStream = null;

            // Close TCP client
            _tcpClient?.Close();
            _tcpClient?.Dispose();
            _tcpClient = null;

            // Clear buffers
            _receiveBuffer.Clear();
            while (_sendQueue.TryDequeue(out _)) { }

            if (_currentState != ConnectionState.Disconnected)
            {
                SetConnectionState(ConnectionState.Disconnected);
                _logger.LogInformation("Disconnected from endpoint {EndpointId}", _configuration.Id);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during disconnect for endpoint {EndpointId}", _configuration.Id);
        }
    }

    /// <summary>
    /// Background loop for receiving data
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    private async Task ReceiveLoopAsync(CancellationToken cancellationToken)
    {
        var buffer = new byte[1024];

        try
        {
            while (!cancellationToken.IsCancellationRequested && _networkStream != null)
            {
                var bytesRead = await _networkStream.ReadAsync(buffer, 0, buffer.Length, cancellationToken);

                if (bytesRead == 0)
                {
                    _logger.LogWarning("Connection closed by remote host for endpoint {EndpointId}", _configuration.Id);
                    break;
                }

                // Convert received bytes to string and add to buffer
                var receivedData = Encoding.ASCII.GetString(buffer, 0, bytesRead);
                _receiveBuffer.Append(receivedData);

                // Process complete messages (ending with CR)
                await ProcessReceivedDataAsync();
            }
        }
        catch (Exception ex) when (!cancellationToken.IsCancellationRequested)
        {
            _logger.LogError(ex, "Error in receive loop for endpoint {EndpointId}", _configuration.Id);
            OnErrorOccurred(new DioErrorEventArgs(_configuration.Id,
                $"Receive error: {ex.Message}", exception: ex, shouldReconnect: true));
        }
    }

    /// <summary>
    /// Background loop for sending data
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    private async Task SendLoopAsync(CancellationToken cancellationToken)
    {
        try
        {
            while (!cancellationToken.IsCancellationRequested && _networkStream != null)
            {
                if (_sendQueue.TryDequeue(out var data))
                {
                    await _networkStream.WriteAsync(data, 0, data.Length, cancellationToken);
                    await _networkStream.FlushAsync(cancellationToken);

                    _logger.LogDebug("Sent {ByteCount} bytes to endpoint {EndpointId}: {Data}",
                        data.Length, _configuration.Id, Encoding.ASCII.GetString(data));
                }
                else
                {
                    await Task.Delay(10, cancellationToken);
                }
            }
        }
        catch (Exception ex) when (!cancellationToken.IsCancellationRequested)
        {
            _logger.LogError(ex, "Error in send loop for endpoint {EndpointId}", _configuration.Id);
            OnErrorOccurred(new DioErrorEventArgs(_configuration.Id,
                $"Send error: {ex.Message}", exception: ex, shouldReconnect: true));
        }
    }

    /// <summary>
    /// Background loop for automatic reconnection
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    private async Task ReconnectLoopAsync(CancellationToken cancellationToken)
    {
        try
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                // Check if reconnection is needed
                if (_currentState == ConnectionState.Error ||
                    (_currentState == ConnectionState.Connected && ShouldReconnectDueToTimeout()))
                {
                    SetConnectionState(ConnectionState.Reconnecting);

                    for (int attempt = 1; attempt <= _configuration.MaxRetryAttempts && !cancellationToken.IsCancellationRequested; attempt++)
                    {
                        _logger.LogInformation("Reconnection attempt {Attempt}/{MaxAttempts} for endpoint {EndpointId}",
                            attempt, _configuration.MaxRetryAttempts, _configuration.Id);

                        if (await ConnectAsync(cancellationToken))
                        {
                            break;
                        }

                        if (attempt < _configuration.MaxRetryAttempts)
                        {
                            await Task.Delay(_configuration.RetryDelayMilliseconds, cancellationToken);
                        }
                    }

                    if (!IsConnected)
                    {
                        _logger.LogError("Failed to reconnect to endpoint {EndpointId} after {MaxAttempts} attempts",
                            _configuration.Id, _configuration.MaxRetryAttempts);
                        SetConnectionState(ConnectionState.Error, "Reconnection failed after maximum attempts");
                    }
                }

                await Task.Delay(1000, cancellationToken); // Check every second
            }
        }
        catch (Exception ex) when (!cancellationToken.IsCancellationRequested)
        {
            _logger.LogError(ex, "Error in reconnect loop for endpoint {EndpointId}", _configuration.Id);
        }
    }

    /// <summary>
    /// Processes received data and extracts complete messages
    /// </summary>
    /// <returns>Task representing the async operation</returns>
    private async Task ProcessReceivedDataAsync()
    {
        var bufferContent = _receiveBuffer.ToString();
        var messages = new List<string>();

        // Split by CR (0x0D) to get complete messages
        var parts = bufferContent.Split('\r');

        // All parts except the last are complete messages
        for (int i = 0; i < parts.Length - 1; i++)
        {
            if (!string.IsNullOrEmpty(parts[i]))
            {
                messages.Add(parts[i]);
            }
        }

        // Keep the last part in buffer (incomplete message)
        _receiveBuffer.Clear();
        if (parts.Length > 0 && !string.IsNullOrEmpty(parts[^1]))
        {
            _receiveBuffer.Append(parts[^1]);
        }

        // Process each complete message
        foreach (var message in messages)
        {
            await ProcessCompleteMessageAsync(message);
        }
    }

    /// <summary>
    /// Processes a complete message received from the device
    /// </summary>
    /// <param name="message">Complete message string</param>
    /// <returns>Task representing the async operation</returns>
    private async Task ProcessCompleteMessageAsync(string message)
    {
        try
        {
            _logger.LogDebug("Received message from endpoint {EndpointId}: {Message}", _configuration.Id, message);

            // Validate message format
            if (IsValidDioResponse(message))
            {
                _lastValidResponseTime = DateTime.UtcNow;
                _invalidResponseCount = 0;

                // Raise data received event
                OnDataReceived(message);
            }
            else
            {
                _invalidResponseCount++;
                _logger.LogWarning("Invalid response from endpoint {EndpointId}: {Message} (Count: {Count})",
                    _configuration.Id, message, _invalidResponseCount);

                // Check if we should trigger reconnection due to too many invalid responses
                if (_invalidResponseCount >= _configuration.MaxInvalidResponseTolerance)
                {
                    _logger.LogError("Too many invalid responses from endpoint {EndpointId}, triggering reconnection",
                        _configuration.Id);

                    OnErrorOccurred(new DioErrorEventArgs(_configuration.Id,
                        $"Too many invalid responses: {_invalidResponseCount}", shouldReconnect: true));

                    SetConnectionState(ConnectionState.Error, "Too many invalid responses");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing message from endpoint {EndpointId}: {Message}",
                _configuration.Id, message);

            OnErrorOccurred(new DioErrorEventArgs(_configuration.Id,
                $"Message processing error: {ex.Message}", exception: ex));
        }

        await Task.CompletedTask;
    }



    /// <summary>
    /// Validates if a response is a valid DIO response
    /// </summary>
    /// <param name="message">Message to validate</param>
    /// <returns>True if valid DIO response</returns>
    private static bool IsValidDioResponse(string message)
    {
        if (string.IsNullOrEmpty(message)) return false;

        // Check for DI response: @DI followed by 24 binary digits
        if (message.StartsWith("@DI ") && message.Length >= 28)
        {
            var diData = message.Substring(4);
            return diData.Length >= 24 && diData.Take(24).All(c => c == '0' || c == '1');
        }

        // Check for DO response: @DO followed by 8 binary digits
        if (message.StartsWith("@DO ") && message.Length >= 12)
        {
            var doData = message.Substring(4);
            return doData.Length >= 8 && doData.Take(8).All(c => c == '0' || c == '1');
        }

        return false;
    }

    /// <summary>
    /// Checks if reconnection should be triggered due to timeout
    /// </summary>
    /// <returns>True if reconnection is needed</returns>
    private bool ShouldReconnectDueToTimeout()
    {
        var timeSinceLastResponse = DateTime.UtcNow - _lastValidResponseTime;
        return timeSinceLastResponse.TotalMilliseconds > _configuration.DisconnectDetectionTimeoutMilliseconds;
    }

    /// <summary>
    /// Sets the connection state and raises the event
    /// </summary>
    /// <param name="newState">New connection state</param>
    /// <param name="errorMessage">Optional error message</param>
    /// <param name="exception">Optional exception</param>
    private void SetConnectionState(ConnectionState newState, string? errorMessage = null, Exception? exception = null)
    {
        var previousState = _currentState;
        _currentState = newState;

        if (previousState != newState)
        {
            _logger.LogInformation("Connection state changed for endpoint {EndpointId}: {PreviousState} -> {CurrentState}",
                _configuration.Id, previousState, newState);

            OnConnectionStateChanged(new ConnectionStateChangedEventArgs(
                _configuration.Id, previousState, newState, errorMessage, exception));
        }
    }

    /// <summary>
    /// Raises the ConnectionStateChanged event
    /// </summary>
    /// <param name="e">Event arguments</param>
    protected virtual void OnConnectionStateChanged(ConnectionStateChangedEventArgs e)
    {
        ConnectionStateChanged?.Invoke(this, e);
    }

    /// <summary>
    /// Raises the DataReceived event
    /// </summary>
    /// <param name="data">Received data</param>
    protected virtual void OnDataReceived(string data)
    {
        DataReceived?.Invoke(this, data);
    }

    /// <summary>
    /// Raises the ErrorOccurred event
    /// </summary>
    /// <param name="e">Event arguments</param>
    protected virtual void OnErrorOccurred(DioErrorEventArgs e)
    {
        ErrorOccurred?.Invoke(this, e);
    }

    /// <summary>
    /// Disposes the connection manager
    /// </summary>
    public void Dispose()
    {
        if (_disposed) return;

        _disposed = true;

        try
        {
            _cancellationTokenSource.Cancel();

            // Wait for stop to complete
            StopAsync().GetAwaiter().GetResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during disposal of ConnectionManager for endpoint {EndpointId}",
                _configuration.Id);
        }
        finally
        {
            _connectionSemaphore?.Dispose();
            _cancellationTokenSource?.Dispose();
        }
    }
}
