using Microsoft.Extensions.Logging;
using Ngp.Communication.TwentyFourDioPoller.Builders;
using Ngp.Communication.TwentyFourDioPoller.Interfaces;

namespace Ngp.Communication.TwentyFourDioPoller;

/// <summary>
/// Factory for creating TwentyFourDioPoller instances
/// </summary>
public static class TwentyFourDioPollerFactory
{
    /// <summary>
    /// Creates a builder for the specified endpoint
    /// </summary>
    /// <param name="endpointId">Endpoint identifier</param>
    /// <param name="ipAddress">IP address of the 24Dio device</param>
    /// <param name="port">TCP port (default: 5801)</param>
    /// <returns>TwentyFourDioPollerBuilder instance</returns>
    public static ITwentyFourDioPollerBuilder CreateForEndpoint(string endpointId, string ipAddress, ushort port = 5801)
    {
        return new TwentyFourDioPollerBuilder(endpointId, ipAddress, port);
    }

    /// <summary>
    /// Creates a builder for the specified endpoint with logger factory
    /// </summary>
    /// <param name="endpointId">Endpoint identifier</param>
    /// <param name="ipAddress">IP address of the 24Dio device</param>
    /// <param name="port">TCP port (default: 5801)</param>
    /// <param name="loggerFactory">Logger factory for creating typed loggers</param>
    /// <returns>TwentyFourDioPollerBuilder instance</returns>
    public static ITwentyFourDioPollerBuilder CreateForEndpoint(
        string endpointId, 
        string ipAddress, 
        ushort port, 
        ILoggerFactory loggerFactory)
    {
        if (loggerFactory == null) throw new ArgumentNullException(nameof(loggerFactory));

        var logger = loggerFactory.CreateLogger<TwentyFourDioPoller>();
        return new TwentyFourDioPollerBuilder(endpointId, ipAddress, port)
            .WithLogger(logger);
    }

    /// <summary>
    /// Creates a simple TwentyFourDioPoller with default settings
    /// </summary>
    /// <param name="endpointId">Endpoint identifier</param>
    /// <param name="ipAddress">IP address of the 24Dio device</param>
    /// <param name="port">TCP port (default: 5801)</param>
    /// <param name="logger">Optional logger instance</param>
    /// <returns>Configured TwentyFourDioPoller instance</returns>
    public static ITwentyFourDioPoller CreateSimple(
        string endpointId, 
        string ipAddress, 
        ushort port = 5801, 
        ILogger? logger = null)
    {
        var builder = new TwentyFourDioPollerBuilder(endpointId, ipAddress, port);
        
        if (logger != null)
        {
            builder.WithLogger(logger);
        }

        return builder.Build();
    }

    /// <summary>
    /// Creates a TwentyFourDioPoller with production-ready settings
    /// </summary>
    /// <param name="endpointId">Endpoint identifier</param>
    /// <param name="ipAddress">IP address of the 24Dio device</param>
    /// <param name="port">TCP port (default: 5801)</param>
    /// <param name="logger">Optional logger instance</param>
    /// <returns>Configured TwentyFourDioPoller instance</returns>
    public static ITwentyFourDioPoller CreateProduction(
        string endpointId, 
        string ipAddress, 
        ushort port = 5801, 
        ILogger? logger = null)
    {
        var builder = new TwentyFourDioPollerBuilder(endpointId, ipAddress, port)
            .WithConnectionTimeout(10000)      // 10 seconds
            .WithReceiveTimeout(10000)         // 10 seconds
            .WithPollingInterval(100)          // 100ms polling
            .WithDebounceTime(50)              // 50ms debounce
            .WithRetryPolicy(5, 2000)          // 5 retries with 2s delay
            .WithDisconnectDetectionTimeout(15000)  // 15 seconds
            .WithInvalidResponseTolerance(10); // Allow 10 invalid responses

        if (logger != null)
        {
            builder.WithLogger(logger);
        }

        return builder.Build();
    }

    /// <summary>
    /// Creates a TwentyFourDioPoller with development/testing settings
    /// </summary>
    /// <param name="endpointId">Endpoint identifier</param>
    /// <param name="ipAddress">IP address of the 24Dio device</param>
    /// <param name="port">TCP port (default: 5801)</param>
    /// <param name="logger">Optional logger instance</param>
    /// <returns>Configured TwentyFourDioPoller instance</returns>
    public static ITwentyFourDioPoller CreateDevelopment(
        string endpointId, 
        string ipAddress, 
        ushort port = 5801, 
        ILogger? logger = null)
    {
        var builder = new TwentyFourDioPollerBuilder(endpointId, ipAddress, port)
            .WithConnectionTimeout(5000)       // 5 seconds
            .WithReceiveTimeout(5000)          // 5 seconds
            .WithPollingInterval(200)          // 200ms polling (slower for debugging)
            .WithDebounceTime(100)             // 100ms debounce (more tolerant)
            .WithRetryPolicy(3, 1000)          // 3 retries with 1s delay
            .WithDisconnectDetectionTimeout(10000)  // 10 seconds
            .WithInvalidResponseTolerance(3);  // Allow 3 invalid responses

        if (logger != null)
        {
            builder.WithLogger(logger);
        }

        return builder.Build();
    }

    /// <summary>
    /// Creates a TwentyFourDioPoller with high-performance settings
    /// </summary>
    /// <param name="endpointId">Endpoint identifier</param>
    /// <param name="ipAddress">IP address of the 24Dio device</param>
    /// <param name="port">TCP port (default: 5801)</param>
    /// <param name="logger">Optional logger instance</param>
    /// <returns>Configured TwentyFourDioPoller instance</returns>
    public static ITwentyFourDioPoller CreateHighPerformance(
        string endpointId, 
        string ipAddress, 
        ushort port = 5801, 
        ILogger? logger = null)
    {
        var builder = new TwentyFourDioPollerBuilder(endpointId, ipAddress, port)
            .WithConnectionTimeout(3000)       // 3 seconds
            .WithReceiveTimeout(3000)          // 3 seconds
            .WithPollingInterval(50)           // 50ms polling (very fast)
            .WithDebounceTime(20)              // 20ms debounce (minimal)
            .WithRetryPolicy(2, 500)           // 2 retries with 500ms delay
            .WithDisconnectDetectionTimeout(5000)   // 5 seconds
            .WithInvalidResponseTolerance(2);  // Allow only 2 invalid responses

        if (logger != null)
        {
            builder.WithLogger(logger);
        }

        return builder.Build();
    }
}
