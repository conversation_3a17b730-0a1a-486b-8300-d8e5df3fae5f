using Microsoft.Extensions.Logging;
using Ngp.Communication.TwentyFourDioPoller.Interfaces;
using Ngp.Communication.TwentyFourDioPoller.Models;

namespace Ngp.Communication.TwentyFourDioPoller.Builders;

/// <summary>
/// Builder for creating TwentyFourDioPoller instances with fluent API
/// </summary>
public class TwentyFourDioPollerBuilder : ITwentyFourDioPollerBuilder
{
    private readonly EndpointConfiguration _configuration;
    private ILogger? _logger;

    /// <summary>
    /// Initializes a new instance of the TwentyFourDioPollerBuilder class
    /// </summary>
    /// <param name="endpointId">Endpoint identifier</param>
    /// <param name="ipAddress">IP address of the 24Dio device</param>
    /// <param name="port">TCP port (default: 5801)</param>
    public TwentyFourDioPollerBuilder(string endpointId, string ipAddress, ushort port = 5801)
    {
        if (string.IsNullOrEmpty(endpointId))
            throw new ArgumentException("Endpoint ID cannot be null or empty", nameof(endpointId));
        if (string.IsNullOrEmpty(ipAddress))
            throw new ArgumentException("IP address cannot be null or empty", nameof(ipAddress));

        _configuration = new EndpointConfiguration
        {
            Id = endpointId,
            IpAddress = ipAddress,
            Port = port
        };
    }

    /// <summary>
    /// Sets the connection timeout in milliseconds
    /// </summary>
    /// <param name="connectionTimeoutMs">Connection timeout in milliseconds</param>
    /// <returns>The builder instance</returns>
    public ITwentyFourDioPollerBuilder WithConnectionTimeout(int connectionTimeoutMs)
    {
        if (connectionTimeoutMs <= 0)
            throw new ArgumentException("Connection timeout must be greater than zero", nameof(connectionTimeoutMs));

        _configuration.ConnectionTimeoutMilliseconds = connectionTimeoutMs;
        return this;
    }

    /// <summary>
    /// Sets the receive timeout in milliseconds
    /// </summary>
    /// <param name="receiveTimeoutMs">Receive timeout in milliseconds</param>
    /// <returns>The builder instance</returns>
    public ITwentyFourDioPollerBuilder WithReceiveTimeout(int receiveTimeoutMs)
    {
        if (receiveTimeoutMs <= 0)
            throw new ArgumentException("Receive timeout must be greater than zero", nameof(receiveTimeoutMs));

        _configuration.ReceiveTimeoutMilliseconds = receiveTimeoutMs;
        return this;
    }

    /// <summary>
    /// Sets the polling interval in milliseconds
    /// </summary>
    /// <param name="pollingIntervalMs">Polling interval in milliseconds</param>
    /// <returns>The builder instance</returns>
    public ITwentyFourDioPollerBuilder WithPollingInterval(int pollingIntervalMs)
    {
        if (pollingIntervalMs < 0)
            throw new ArgumentException("Polling interval cannot be negative", nameof(pollingIntervalMs));

        _configuration.PollingIntervalMilliseconds = pollingIntervalMs;
        return this;
    }

    /// <summary>
    /// Sets the debounce time in milliseconds
    /// </summary>
    /// <param name="debounceTimeMs">Debounce time in milliseconds</param>
    /// <returns>The builder instance</returns>
    public ITwentyFourDioPollerBuilder WithDebounceTime(int debounceTimeMs)
    {
        if (debounceTimeMs < 0)
            throw new ArgumentException("Debounce time cannot be negative", nameof(debounceTimeMs));

        _configuration.DebounceTimeMilliseconds = debounceTimeMs;
        return this;
    }

    /// <summary>
    /// Sets the retry policy
    /// </summary>
    /// <param name="maxRetryAttempts">Maximum retry attempts</param>
    /// <param name="retryDelayMs">Retry delay in milliseconds</param>
    /// <returns>The builder instance</returns>
    public ITwentyFourDioPollerBuilder WithRetryPolicy(int maxRetryAttempts, int retryDelayMs)
    {
        if (maxRetryAttempts < 0)
            throw new ArgumentException("Max retry attempts cannot be negative", nameof(maxRetryAttempts));
        if (retryDelayMs < 0)
            throw new ArgumentException("Retry delay cannot be negative", nameof(retryDelayMs));

        _configuration.MaxRetryAttempts = maxRetryAttempts;
        _configuration.RetryDelayMilliseconds = retryDelayMs;
        return this;
    }

    /// <summary>
    /// Sets the disconnect detection timeout in milliseconds
    /// </summary>
    /// <param name="timeoutMs">Disconnect detection timeout in milliseconds</param>
    /// <returns>The builder instance</returns>
    public ITwentyFourDioPollerBuilder WithDisconnectDetectionTimeout(int timeoutMs)
    {
        if (timeoutMs <= 0)
            throw new ArgumentException("Disconnect detection timeout must be greater than zero", nameof(timeoutMs));

        _configuration.DisconnectDetectionTimeoutMilliseconds = timeoutMs;
        return this;
    }

    /// <summary>
    /// Sets the maximum invalid response tolerance
    /// </summary>
    /// <param name="maxTolerance">Maximum invalid response tolerance</param>
    /// <returns>The builder instance</returns>
    public ITwentyFourDioPollerBuilder WithInvalidResponseTolerance(int maxTolerance)
    {
        if (maxTolerance < 0)
            throw new ArgumentException("Max tolerance cannot be negative", nameof(maxTolerance));

        _configuration.MaxInvalidResponseTolerance = maxTolerance;
        return this;
    }

    /// <summary>
    /// Sets the logger instance
    /// </summary>
    /// <param name="logger">Logger instance</param>
    /// <returns>The builder instance</returns>
    public ITwentyFourDioPollerBuilder WithLogger(ILogger logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        return this;
    }

    /// <summary>
    /// Builds the TwentyFourDioPoller instance
    /// </summary>
    /// <returns>Configured TwentyFourDioPoller instance</returns>
    public ITwentyFourDioPoller Build()
    {
        // Validate configuration
        ValidateConfiguration();

        // Create logger adapter if needed
        ILogger<TwentyFourDioPoller>? typedLogger = null;
        if (_logger != null)
        {
            typedLogger = new LoggerAdapter<TwentyFourDioPoller>(_logger);
        }

        return new TwentyFourDioPoller(_configuration, typedLogger);
    }

    /// <summary>
    /// Validates the configuration before building
    /// </summary>
    private void ValidateConfiguration()
    {
        if (string.IsNullOrEmpty(_configuration.Id))
            throw new InvalidOperationException("Endpoint ID is required");

        if (string.IsNullOrEmpty(_configuration.IpAddress))
            throw new InvalidOperationException("IP address is required");

        if (_configuration.Port == 0)
            throw new InvalidOperationException("Port must be specified");

        if (_configuration.ConnectionTimeoutMilliseconds <= 0)
            throw new InvalidOperationException("Connection timeout must be greater than zero");

        if (_configuration.ReceiveTimeoutMilliseconds <= 0)
            throw new InvalidOperationException("Receive timeout must be greater than zero");

        if (_configuration.PollingIntervalMilliseconds < 0)
            throw new InvalidOperationException("Polling interval cannot be negative");

        if (_configuration.DebounceTimeMilliseconds < 0)
            throw new InvalidOperationException("Debounce time cannot be negative");

        if (_configuration.MaxRetryAttempts < 0)
            throw new InvalidOperationException("Max retry attempts cannot be negative");

        if (_configuration.RetryDelayMilliseconds < 0)
            throw new InvalidOperationException("Retry delay cannot be negative");

        if (_configuration.DisconnectDetectionTimeoutMilliseconds <= 0)
            throw new InvalidOperationException("Disconnect detection timeout must be greater than zero");

        if (_configuration.MaxInvalidResponseTolerance < 0)
            throw new InvalidOperationException("Max invalid response tolerance cannot be negative");
    }
}
