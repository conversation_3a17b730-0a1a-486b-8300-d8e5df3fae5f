using Microsoft.Extensions.Logging;
using Ngp.Communication.TwentyFourDioPoller.Connection;
using Ngp.Communication.TwentyFourDioPoller.Enums;
using Ngp.Communication.TwentyFourDioPoller.Events;
using Ngp.Communication.TwentyFourDioPoller.Models;
using System.Collections.Concurrent;

namespace Ngp.Communication.TwentyFourDioPoller.Commands;

/// <summary>
/// Processes 24Dio commands and responses
/// </summary>
public class CommandProcessor : IDisposable
{
    private readonly EndpointConfiguration _configuration;
    private readonly ConnectionManager _connectionManager;
    private readonly ILogger<CommandProcessor> _logger;
    private readonly ConcurrentDictionary<string, TaskCompletionSource<string>> _pendingCommands;
    private readonly SemaphoreSlim _commandSemaphore;
    private readonly CancellationTokenSource _cancellationTokenSource;

    private bool _disposed;



    /// <summary>
    /// Event raised when a command execution error occurs
    /// </summary>
    public event EventHandler<DioErrorEventArgs>? CommandError;

    /// <summary>
    /// Initializes a new instance of the CommandProcessor class
    /// </summary>
    /// <param name="configuration">Endpoint configuration</param>
    /// <param name="connectionManager">Connection manager instance</param>
    /// <param name="logger">Logger instance</param>
    public CommandProcessor(
        EndpointConfiguration configuration,
        ConnectionManager connectionManager,
        ILogger<CommandProcessor> logger)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        _pendingCommands = new ConcurrentDictionary<string, TaskCompletionSource<string>>();
        _commandSemaphore = new SemaphoreSlim(1, 1);
        _cancellationTokenSource = new CancellationTokenSource();

        // Subscribe to connection manager events
        _connectionManager.DataReceived += OnDataReceived;
        _connectionManager.ErrorOccurred += OnConnectionError;
    }

    /// <summary>
    /// Executes a DIO command and waits for response
    /// </summary>
    /// <param name="command">Command to execute</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Response string from the device</returns>
    public async Task<string?> ExecuteCommandAsync(DioCommand command, CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(CommandProcessor));
        if (command == null) throw new ArgumentNullException(nameof(command));

        await _commandSemaphore.WaitAsync(cancellationToken);
        try
        {
            if (!_connectionManager.IsConnected)
            {
                _logger.LogDebug("Cannot execute command - not connected to endpoint {EndpointId}", _configuration.Id);
                return null;
            }

            var commandString = command.ToAsciiString().Trim();
            var commandBytes = command.ToByteArray();

            _logger.LogDebug("Executing command for endpoint {EndpointId}: {Command}", 
                _configuration.Id, commandString);

            // For GET commands, set up response waiting
            TaskCompletionSource<string>? responseWaiter = null;
            if (command.CommandType == DioCommandType.GetDI || command.CommandType == DioCommandType.GetDO)
            {
                responseWaiter = new TaskCompletionSource<string>();
                var commandKey = GetCommandKey(command.CommandType);
                _pendingCommands[commandKey] = responseWaiter;
            }

            // Send command
            var sent = await _connectionManager.SendAsync(commandBytes, cancellationToken);
            if (!sent)
            {
                if (responseWaiter != null)
                {
                    var commandKey = GetCommandKey(command.CommandType);
                    _pendingCommands.TryRemove(commandKey, out _);
                }
                
                OnCommandError(new DioErrorEventArgs(_configuration.Id, 
                    "Failed to send command", command.CommandType));
                return null;
            }

            // For write commands (SET/CLR/PIN), complete immediately
            if (command.CommandType != DioCommandType.GetDI && command.CommandType != DioCommandType.GetDO)
            {
                _logger.LogDebug("Write command sent successfully for endpoint {EndpointId}: {Command}", 
                    _configuration.Id, commandString);
                return "OK";
            }

            // Wait for response
            if (responseWaiter != null)
            {
                try
                {
                    using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                    timeoutCts.CancelAfter(_configuration.ReceiveTimeoutMilliseconds);

                    var response = await responseWaiter.Task.WaitAsync(timeoutCts.Token);
                    
                    _logger.LogDebug("Received response for endpoint {EndpointId}: {Response}", 
                        _configuration.Id, response);
                    
                    return response;
                }
                catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
                {
                    throw;
                }
                catch (OperationCanceledException)
                {
                    var commandKey = GetCommandKey(command.CommandType);
                    _pendingCommands.TryRemove(commandKey, out _);
                    
                    OnCommandError(new DioErrorEventArgs(_configuration.Id, 
                        "Command timeout", command.CommandType));
                    return null;
                }
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing command for endpoint {EndpointId}: {Command}", 
                _configuration.Id, command.ToAsciiString());
            
            OnCommandError(new DioErrorEventArgs(_configuration.Id, 
                $"Command execution error: {ex.Message}", command.CommandType, ex));
            return null;
        }
        finally
        {
            _commandSemaphore.Release();
        }
    }

    /// <summary>
    /// Executes a GET DI command
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>DI states as binary string</returns>
    public async Task<string?> GetDIAsync(CancellationToken cancellationToken = default)
    {
        var command = DioCommand.CreateGetDI();
        var response = await ExecuteCommandAsync(command, cancellationToken);
        
        if (response != null && response.StartsWith("@DI "))
        {
            return response.Substring(4).Trim();
        }
        
        return null;
    }

    /// <summary>
    /// Executes a GET DO command
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>DO states as binary string</returns>
    public async Task<string?> GetDOAsync(CancellationToken cancellationToken = default)
    {
        var command = DioCommand.CreateGetDO();
        var response = await ExecuteCommandAsync(command, cancellationToken);
        
        if (response != null && response.StartsWith("@DO "))
        {
            return response.Substring(4).Trim();
        }
        
        return null;
    }

    /// <summary>
    /// Executes a SET DO command
    /// </summary>
    /// <param name="doStates">8-bit DO states string</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if command was sent successfully</returns>
    public async Task<bool> SetDOAsync(string doStates, CancellationToken cancellationToken = default)
    {
        try
        {
            var command = DioCommand.CreateSetDO(doStates);
            var response = await ExecuteCommandAsync(command, cancellationToken);
            return response != null;
        }
        catch (ArgumentException ex)
        {
            _logger.LogError(ex, "Invalid DO states for SET command: {DoStates}", doStates);
            OnCommandError(new DioErrorEventArgs(_configuration.Id, 
                $"Invalid DO states: {ex.Message}", DioCommandType.SetDO, ex));
            return false;
        }
    }

    /// <summary>
    /// Executes a CLR DO command
    /// </summary>
    /// <param name="doStates">8-bit DO states string</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if command was sent successfully</returns>
    public async Task<bool> ClearDOAsync(string doStates, CancellationToken cancellationToken = default)
    {
        try
        {
            var command = DioCommand.CreateClearDO(doStates);
            var response = await ExecuteCommandAsync(command, cancellationToken);
            return response != null;
        }
        catch (ArgumentException ex)
        {
            _logger.LogError(ex, "Invalid DO states for CLR command: {DoStates}", doStates);
            OnCommandError(new DioErrorEventArgs(_configuration.Id, 
                $"Invalid DO states: {ex.Message}", DioCommandType.ClearDO, ex));
            return false;
        }
    }

    /// <summary>
    /// Executes a PIN DO command
    /// </summary>
    /// <param name="doStates">8-bit DO states string</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if command was sent successfully</returns>
    public async Task<bool> PinDOAsync(string doStates, CancellationToken cancellationToken = default)
    {
        try
        {
            var command = DioCommand.CreatePinDO(doStates);
            var response = await ExecuteCommandAsync(command, cancellationToken);
            return response != null;
        }
        catch (ArgumentException ex)
        {
            _logger.LogError(ex, "Invalid DO states for PIN command: {DoStates}", doStates);
            OnCommandError(new DioErrorEventArgs(_configuration.Id,
                $"Invalid DO states: {ex.Message}", DioCommandType.PinDO, ex));
            return false;
        }
    }

    /// <summary>
    /// Handles data received from the connection manager
    /// </summary>
    /// <param name="sender">Event sender</param>
    /// <param name="data">Received data</param>
    private void OnDataReceived(object? sender, string data)
    {
        try
        {
            // Check if this is a response to a pending command
            if (data.StartsWith("@DI "))
            {
                var commandKey = GetCommandKey(DioCommandType.GetDI);
                if (_pendingCommands.TryRemove(commandKey, out var tcs))
                {
                    tcs.SetResult(data);
                }
            }
            else if (data.StartsWith("@DO "))
            {
                var commandKey = GetCommandKey(DioCommandType.GetDO);
                if (_pendingCommands.TryRemove(commandKey, out var tcs))
                {
                    tcs.SetResult(data);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing received data for endpoint {EndpointId}: {Data}",
                _configuration.Id, data);
        }
    }

    /// <summary>
    /// Handles connection errors
    /// </summary>
    /// <param name="sender">Event sender</param>
    /// <param name="e">Error event arguments</param>
    private void OnConnectionError(object? sender, DioErrorEventArgs e)
    {
        // Cancel all pending commands
        foreach (var kvp in _pendingCommands)
        {
            if (_pendingCommands.TryRemove(kvp.Key, out var tcs))
            {
                tcs.SetException(new InvalidOperationException($"Connection error: {e.ErrorMessage}"));
            }
        }

        // Forward the error
        OnCommandError(e);
    }



    /// <summary>
    /// Gets the command key for tracking pending commands
    /// </summary>
    /// <param name="commandType">Command type</param>
    /// <returns>Command key string</returns>
    private static string GetCommandKey(DioCommandType commandType)
    {
        return commandType.ToString();
    }

    /// <summary>
    /// Raises the CommandError event
    /// </summary>
    /// <param name="e">Event arguments</param>
    protected virtual void OnCommandError(DioErrorEventArgs e)
    {
        CommandError?.Invoke(this, e);
    }

    /// <summary>
    /// Disposes the command processor
    /// </summary>
    public void Dispose()
    {
        if (_disposed) return;

        _disposed = true;

        try
        {
            _cancellationTokenSource.Cancel();

            // Cancel all pending commands
            foreach (var kvp in _pendingCommands)
            {
                if (_pendingCommands.TryRemove(kvp.Key, out var tcs))
                {
                    tcs.SetCanceled();
                }
            }

            // Unsubscribe from events
            _connectionManager.DataReceived -= OnDataReceived;
            _connectionManager.ErrorOccurred -= OnConnectionError;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during disposal of CommandProcessor for endpoint {EndpointId}",
                _configuration.Id);
        }
        finally
        {
            _commandSemaphore?.Dispose();
            _cancellationTokenSource?.Dispose();
        }
    }
}
