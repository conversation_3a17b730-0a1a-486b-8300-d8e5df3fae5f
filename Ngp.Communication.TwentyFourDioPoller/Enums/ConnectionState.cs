namespace Ngp.Communication.TwentyFourDioPoller.Enums;

/// <summary>
/// Represents the connection state of a 24Dio device
/// </summary>
public enum ConnectionState
{
    /// <summary>
    /// Device is disconnected
    /// </summary>
    Disconnected,

    /// <summary>
    /// Device is connecting
    /// </summary>
    Connecting,

    /// <summary>
    /// Device is connected and operational
    /// </summary>
    Connected,

    /// <summary>
    /// Device connection is in error state
    /// </summary>
    Error,

    /// <summary>
    /// Device is reconnecting after an error
    /// </summary>
    Reconnecting
}
