namespace Ngp.Communication.TwentyFourDioPoller.Enums;

/// <summary>
/// Represents the type of 24Dio command
/// </summary>
public enum DioCommandType
{
    /// <summary>
    /// Get all DI states - @GET DI
    /// </summary>
    GetDI,

    /// <summary>
    /// Get all DO states - @GET DO
    /// </summary>
    GetDO,

    /// <summary>
    /// Set specific DO points to 1 - @SET DO
    /// </summary>
    SetDO,

    /// <summary>
    /// Clear specific DO points to 0 - @CLR DO
    /// </summary>
    ClearDO,

    /// <summary>
    /// Pin all DO points to specific states - @PIN DO
    /// </summary>
    PinDO
}
