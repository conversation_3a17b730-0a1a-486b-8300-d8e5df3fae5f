namespace Ngp.Communication.TwentyFourDioPoller.Models;

/// <summary>
/// Configuration for a 24Dio endpoint
/// </summary>
public class EndpointConfiguration
{
    /// <summary>
    /// Gets or sets the endpoint identifier
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the IP address of the 24Dio device
    /// </summary>
    public string IpAddress { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the TCP port (default: 5801)
    /// </summary>
    public ushort Port { get; set; } = 5801;

    /// <summary>
    /// Gets or sets the connection timeout in milliseconds (default: 5000)
    /// </summary>
    public int ConnectionTimeoutMilliseconds { get; set; } = 5000;

    /// <summary>
    /// Gets or sets the receive timeout in milliseconds (default: 5000)
    /// </summary>
    public int ReceiveTimeoutMilliseconds { get; set; } = 5000;

    /// <summary>
    /// Gets or sets the polling interval in milliseconds (default: 100)
    /// </summary>
    public int PollingIntervalMilliseconds { get; set; } = 100;

    /// <summary>
    /// Gets or sets the debounce time in milliseconds (default: 50)
    /// </summary>
    public int DebounceTimeMilliseconds { get; set; } = 50;

    /// <summary>
    /// Gets or sets the maximum retry attempts for connection (default: 3)
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// Gets or sets the retry delay in milliseconds (default: 1000)
    /// </summary>
    public int RetryDelayMilliseconds { get; set; } = 1000;

    /// <summary>
    /// Gets or sets the disconnect detection timeout in milliseconds (default: 5000)
    /// </summary>
    public int DisconnectDetectionTimeoutMilliseconds { get; set; } = 5000;

    /// <summary>
    /// Gets or sets the maximum invalid response tolerance before triggering reconnection (default: 5)
    /// </summary>
    public int MaxInvalidResponseTolerance { get; set; } = 5;
}
