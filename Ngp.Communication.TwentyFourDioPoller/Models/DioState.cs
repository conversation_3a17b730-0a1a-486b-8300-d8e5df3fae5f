namespace Ngp.Communication.TwentyFourDioPoller.Models;

/// <summary>
/// Represents the state of DI/DO points
/// </summary>
public class DioState
{
    /// <summary>
    /// Gets or sets the endpoint identifier
    /// </summary>
    public string EndpointId { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the DI states (24 points)
    /// </summary>
    public bool[] DigitalInputs { get; set; } = new bool[24];

    /// <summary>
    /// Gets or sets the DO states (8 points)
    /// </summary>
    public bool[] DigitalOutputs { get; set; } = new bool[8];

    /// <summary>
    /// Gets or sets the timestamp when the state was captured
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Gets the DI state as a binary string
    /// </summary>
    public string GetDIBinaryString()
    {
        return string.Join("", DigitalInputs.Select(di => di ? "1" : "0"));
    }

    /// <summary>
    /// Gets the DO state as a binary string
    /// </summary>
    public string GetDOBinaryString()
    {
        return string.Join("", DigitalOutputs.Select(dout => dout ? "1" : "0"));
    }

    /// <summary>
    /// Sets DI states from a binary string
    /// </summary>
    /// <param name="binaryString">Binary string representation</param>
    public void SetDIFromBinaryString(string binaryString)
    {
        if (string.IsNullOrEmpty(binaryString) || binaryString.Length != 24)
            throw new ArgumentException("Binary string must be exactly 24 characters long", nameof(binaryString));

        for (int i = 0; i < 24; i++)
        {
            DigitalInputs[i] = binaryString[i] == '1';
        }
        Timestamp = DateTime.UtcNow;
    }

    /// <summary>
    /// Sets DO states from a binary string
    /// </summary>
    /// <param name="binaryString">Binary string representation</param>
    public void SetDOFromBinaryString(string binaryString)
    {
        if (string.IsNullOrEmpty(binaryString) || binaryString.Length != 8)
            throw new ArgumentException("Binary string must be exactly 8 characters long", nameof(binaryString));

        for (int i = 0; i < 8; i++)
        {
            DigitalOutputs[i] = binaryString[i] == '1';
        }
        Timestamp = DateTime.UtcNow;
    }

    /// <summary>
    /// Creates a deep copy of the current state
    /// </summary>
    /// <returns>A new DioState instance with copied values</returns>
    public DioState Clone()
    {
        return new DioState
        {
            EndpointId = EndpointId,
            DigitalInputs = (bool[])DigitalInputs.Clone(),
            DigitalOutputs = (bool[])DigitalOutputs.Clone(),
            Timestamp = Timestamp
        };
    }
}
