using Ngp.Communication.TwentyFourDioPoller.Enums;

namespace Ngp.Communication.TwentyFourDioPoller.Models;

/// <summary>
/// Represents a 24Dio command
/// </summary>
public class DioCommand
{
    /// <summary>
    /// Gets or sets the command type
    /// </summary>
    public DioCommandType CommandType { get; set; }

    /// <summary>
    /// Gets or sets the DO states for SET/CLR/PIN commands (8 bits)
    /// </summary>
    public string? DoStates { get; set; }

    /// <summary>
    /// Gets or sets the completion source for async operations
    /// </summary>
    public TaskCompletionSource<bool>? CompletionSource { get; set; }

    /// <summary>
    /// Gets or sets the timestamp when the command was created
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Converts the command to its ASCII string representation
    /// </summary>
    /// <returns>ASCII command string</returns>
    public string ToAsciiString()
    {
        return CommandType switch
        {
            DioCommandType.GetDI => "@GET DI ",
            DioCommandType.GetDO => "@GET DO ",
            DioCommandType.SetDO => $"@SET DO {DoStates ?? "00000000"} ",
            DioCommandType.ClearDO => $"@CLR DO {DoStates ?? "00000000"} ",
            DioCommandType.PinDO => $"@PIN DO {DoStates ?? "00000000"} ",
            _ => throw new InvalidOperationException($"Unknown command type: {CommandType}")
        };
    }

    /// <summary>
    /// Converts the command to byte array for transmission (ASCII + CR)
    /// </summary>
    /// <returns>Byte array representation</returns>
    public byte[] ToByteArray()
    {
        var asciiString = ToAsciiString();
        var asciiBytes = System.Text.Encoding.ASCII.GetBytes(asciiString);
        
        // Add space (0x20) and CR (0x0D) at the end
        var result = new byte[asciiBytes.Length + 1];
        Array.Copy(asciiBytes, result, asciiBytes.Length);
        result[^1] = 0x0D; // CR
        
        return result;
    }

    /// <summary>
    /// Creates a GET DI command
    /// </summary>
    /// <returns>DioCommand instance</returns>
    public static DioCommand CreateGetDI()
    {
        return new DioCommand { CommandType = DioCommandType.GetDI };
    }

    /// <summary>
    /// Creates a GET DO command
    /// </summary>
    /// <returns>DioCommand instance</returns>
    public static DioCommand CreateGetDO()
    {
        return new DioCommand { CommandType = DioCommandType.GetDO };
    }

    /// <summary>
    /// Creates a SET DO command
    /// </summary>
    /// <param name="doStates">8-bit DO states string</param>
    /// <returns>DioCommand instance</returns>
    public static DioCommand CreateSetDO(string doStates)
    {
        if (string.IsNullOrEmpty(doStates) || doStates.Length != 8)
            throw new ArgumentException("DO states must be exactly 8 characters long", nameof(doStates));

        return new DioCommand 
        { 
            CommandType = DioCommandType.SetDO, 
            DoStates = doStates 
        };
    }

    /// <summary>
    /// Creates a CLR DO command
    /// </summary>
    /// <param name="doStates">8-bit DO states string</param>
    /// <returns>DioCommand instance</returns>
    public static DioCommand CreateClearDO(string doStates)
    {
        if (string.IsNullOrEmpty(doStates) || doStates.Length != 8)
            throw new ArgumentException("DO states must be exactly 8 characters long", nameof(doStates));

        return new DioCommand 
        { 
            CommandType = DioCommandType.ClearDO, 
            DoStates = doStates 
        };
    }

    /// <summary>
    /// Creates a PIN DO command
    /// </summary>
    /// <param name="doStates">8-bit DO states string</param>
    /// <returns>DioCommand instance</returns>
    public static DioCommand CreatePinDO(string doStates)
    {
        if (string.IsNullOrEmpty(doStates) || doStates.Length != 8)
            throw new ArgumentException("DO states must be exactly 8 characters long", nameof(doStates));

        return new DioCommand 
        { 
            CommandType = DioCommandType.PinDO, 
            DoStates = doStates 
        };
    }
}
