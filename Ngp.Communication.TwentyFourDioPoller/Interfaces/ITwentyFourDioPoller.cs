using Ngp.Communication.TwentyFourDioPoller.Enums;
using Ngp.Communication.TwentyFourDioPoller.Events;
using Ngp.Communication.TwentyFourDioPoller.Models;

namespace Ngp.Communication.TwentyFourDioPoller.Interfaces;

/// <summary>
/// Interface for 24Dio Poller functionality
/// </summary>
public interface ITwentyFourDioPoller : IDisposable
{
    /// <summary>
    /// Event raised when a connection state changes
    /// </summary>
    event EventHandler<ConnectionStateChangedEventArgs>? ConnectionStateChanged;

    /// <summary>
    /// Event raised when DI/DO values change
    /// </summary>
    event EventHandler<DioValueChangedEventArgs>? DioValueChanged;

    /// <summary>
    /// Event raised when an error occurs
    /// </summary>
    event EventHandler<DioErrorEventArgs>? DioError;

    /// <summary>
    /// Gets the current connection state
    /// </summary>
    ConnectionState ConnectionState { get; }

    /// <summary>
    /// Gets the endpoint configuration
    /// </summary>
    EndpointConfiguration Configuration { get; }

    /// <summary>
    /// Gets the current DIO state
    /// </summary>
    DioState? CurrentState { get; }

    /// <summary>
    /// Gets whether the poller is running
    /// </summary>
    bool IsRunning { get; }

    /// <summary>
    /// Starts the 24Dio poller
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task StartAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Stops the 24Dio poller
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task StopAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Sets specific DO points to 1
    /// </summary>
    /// <param name="doStates">8-bit DO states string</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if command was executed successfully</returns>
    Task<bool> SetDOAsync(string doStates, CancellationToken cancellationToken = default);

    /// <summary>
    /// Clears specific DO points to 0
    /// </summary>
    /// <param name="doStates">8-bit DO states string</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if command was executed successfully</returns>
    Task<bool> ClearDOAsync(string doStates, CancellationToken cancellationToken = default);

    /// <summary>
    /// Pins all DO points to specific states
    /// </summary>
    /// <param name="doStates">8-bit DO states string</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if command was executed successfully</returns>
    Task<bool> PinDOAsync(string doStates, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the current DI states
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>24-bit DI states string</returns>
    Task<string?> GetDIAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the current DO states
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>8-bit DO states string</returns>
    Task<string?> GetDOAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Forces a reconnection to the device
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task ForceReconnectAsync(CancellationToken cancellationToken = default);
}
