using Microsoft.Extensions.Logging;

namespace Ngp.Communication.TwentyFourDioPoller.Interfaces;

/// <summary>
/// Interface for building TwentyFourDioPoller instances with fluent API
/// </summary>
public interface ITwentyFourDioPollerBuilder
{
    /// <summary>
    /// Sets the connection timeout in milliseconds
    /// </summary>
    /// <param name="connectionTimeoutMs">Connection timeout in milliseconds</param>
    /// <returns>The builder instance</returns>
    ITwentyFourDioPollerBuilder WithConnectionTimeout(int connectionTimeoutMs);

    /// <summary>
    /// Sets the receive timeout in milliseconds
    /// </summary>
    /// <param name="receiveTimeoutMs">Receive timeout in milliseconds</param>
    /// <returns>The builder instance</returns>
    ITwentyFourDioPollerBuilder WithReceiveTimeout(int receiveTimeoutMs);

    /// <summary>
    /// Sets the polling interval in milliseconds
    /// </summary>
    /// <param name="pollingIntervalMs">Polling interval in milliseconds</param>
    /// <returns>The builder instance</returns>
    ITwentyFourDioPollerBuilder WithPollingInterval(int pollingIntervalMs);

    /// <summary>
    /// Sets the debounce time in milliseconds
    /// </summary>
    /// <param name="debounceTimeMs">Debounce time in milliseconds</param>
    /// <returns>The builder instance</returns>
    ITwentyFourDioPollerBuilder WithDebounceTime(int debounceTimeMs);

    /// <summary>
    /// Sets the retry policy
    /// </summary>
    /// <param name="maxRetryAttempts">Maximum retry attempts</param>
    /// <param name="retryDelayMs">Retry delay in milliseconds</param>
    /// <returns>The builder instance</returns>
    ITwentyFourDioPollerBuilder WithRetryPolicy(int maxRetryAttempts, int retryDelayMs);

    /// <summary>
    /// Sets the disconnect detection timeout in milliseconds
    /// </summary>
    /// <param name="timeoutMs">Disconnect detection timeout in milliseconds</param>
    /// <returns>The builder instance</returns>
    ITwentyFourDioPollerBuilder WithDisconnectDetectionTimeout(int timeoutMs);

    /// <summary>
    /// Sets the maximum invalid response tolerance
    /// </summary>
    /// <param name="maxTolerance">Maximum invalid response tolerance</param>
    /// <returns>The builder instance</returns>
    ITwentyFourDioPollerBuilder WithInvalidResponseTolerance(int maxTolerance);

    /// <summary>
    /// Sets the logger instance
    /// </summary>
    /// <param name="logger">Logger instance</param>
    /// <returns>The builder instance</returns>
    ITwentyFourDioPollerBuilder WithLogger(ILogger logger);

    /// <summary>
    /// Builds the TwentyFourDioPoller instance
    /// </summary>
    /// <returns>Configured TwentyFourDioPoller instance</returns>
    ITwentyFourDioPoller Build();
}
