using Microsoft.Extensions.Logging;
using Ngp.Communication.TwentyFourDioPoller.Commands;
using Ngp.Communication.TwentyFourDioPoller.Connection;
using Ngp.Communication.TwentyFourDioPoller.Enums;
using Ngp.Communication.TwentyFourDioPoller.Events;
using Ngp.Communication.TwentyFourDioPoller.Models;
using System.Collections.Concurrent;

namespace Ngp.Communication.TwentyFourDioPoller.Polling;

/// <summary>
/// Manages background polling of 24Dio device and triggers events on value changes
/// </summary>
public class PollingEngine : IDisposable
{
    private readonly EndpointConfiguration _configuration;
    private readonly CommandProcessor _commandProcessor;
    private readonly ConnectionManager _connectionManager;
    private readonly ILogger<PollingEngine> _logger;

    private readonly ConcurrentQueue<DioCommand> _writeQueue;
    private readonly ConcurrentDictionary<string, DateTime> _debounceTimestamps;
    private readonly SemaphoreSlim _pollingSemaphore;
    private readonly CancellationTokenSource _cancellationTokenSource;

    private DioState? _currentState;
    private DioState? _previousState;
    private Task? _pollingTask;
    private Task? _writeProcessingTask;
    private bool _disposed;
    private bool _isRunning;

    /// <summary>
    /// Event raised when DI/DO values change
    /// </summary>
    public event EventHandler<DioValueChangedEventArgs>? DioValueChanged;

    /// <summary>
    /// Event raised when a polling error occurs
    /// </summary>
    public event EventHandler<DioErrorEventArgs>? PollingError;

    /// <summary>
    /// Gets whether the polling engine is running
    /// </summary>
    public bool IsRunning => _isRunning;

    /// <summary>
    /// Gets the current DIO state
    /// </summary>
    public DioState? CurrentState => _currentState?.Clone();

    /// <summary>
    /// Initializes a new instance of the PollingEngine class
    /// </summary>
    /// <param name="configuration">Endpoint configuration</param>
    /// <param name="commandProcessor">Command processor instance</param>
    /// <param name="connectionManager">Connection manager instance</param>
    /// <param name="logger">Logger instance</param>
    public PollingEngine(
        EndpointConfiguration configuration,
        CommandProcessor commandProcessor,
        ConnectionManager connectionManager,
        ILogger<PollingEngine> logger)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _commandProcessor = commandProcessor ?? throw new ArgumentNullException(nameof(commandProcessor));
        _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        _writeQueue = new ConcurrentQueue<DioCommand>();
        _debounceTimestamps = new ConcurrentDictionary<string, DateTime>();
        _pollingSemaphore = new SemaphoreSlim(1, 1);
        _cancellationTokenSource = new CancellationTokenSource();

        // Initialize current state
        _currentState = new DioState { EndpointId = _configuration.Id };

        // Subscribe to command processor errors
        _commandProcessor.CommandError += OnCommandError;
    }

    /// <summary>
    /// Starts the polling engine
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(PollingEngine));

        await _pollingSemaphore.WaitAsync(cancellationToken);
        try
        {
            if (_isRunning) return;

            _logger.LogInformation("Starting polling engine for endpoint {EndpointId}", _configuration.Id);

            // Start background tasks
            _pollingTask = Task.Run(async () => await PollingLoopAsync(_cancellationTokenSource.Token), 
                _cancellationTokenSource.Token);

            _writeProcessingTask = Task.Run(async () => await WriteProcessingLoopAsync(_cancellationTokenSource.Token), 
                _cancellationTokenSource.Token);

            _isRunning = true;
            _logger.LogInformation("Polling engine started for endpoint {EndpointId}", _configuration.Id);
        }
        finally
        {
            _pollingSemaphore.Release();
        }
    }

    /// <summary>
    /// Stops the polling engine
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed || !_isRunning) return;

        await _pollingSemaphore.WaitAsync(cancellationToken);
        try
        {
            _logger.LogInformation("Stopping polling engine for endpoint {EndpointId}", _configuration.Id);

            _cancellationTokenSource.Cancel();
            _isRunning = false;

            // Wait for background tasks to complete
            var tasks = new List<Task>();
            if (_pollingTask != null) tasks.Add(_pollingTask);
            if (_writeProcessingTask != null) tasks.Add(_writeProcessingTask);

            if (tasks.Count > 0)
            {
                await Task.WhenAll(tasks);
            }

            _logger.LogInformation("Polling engine stopped for endpoint {EndpointId}", _configuration.Id);
        }
        finally
        {
            _pollingSemaphore.Release();
        }
    }

    /// <summary>
    /// Queues a write command for execution
    /// </summary>
    /// <param name="command">Command to queue</param>
    public void QueueWrite(DioCommand command)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(PollingEngine));
        if (command == null) throw new ArgumentNullException(nameof(command));

        _writeQueue.Enqueue(command);
        _logger.LogDebug("Write command queued for endpoint {EndpointId}: {Command}", 
            _configuration.Id, command.ToAsciiString());
    }

    /// <summary>
    /// Executes a SET DO command
    /// </summary>
    /// <param name="doStates">8-bit DO states string</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task<bool> SetDOAsync(string doStates, CancellationToken cancellationToken = default)
    {
        var command = DioCommand.CreateSetDO(doStates);
        command.CompletionSource = new TaskCompletionSource<bool>();
        
        QueueWrite(command);
        
        return await command.CompletionSource.Task.WaitAsync(cancellationToken);
    }

    /// <summary>
    /// Executes a CLR DO command
    /// </summary>
    /// <param name="doStates">8-bit DO states string</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task<bool> ClearDOAsync(string doStates, CancellationToken cancellationToken = default)
    {
        var command = DioCommand.CreateClearDO(doStates);
        command.CompletionSource = new TaskCompletionSource<bool>();
        
        QueueWrite(command);
        
        return await command.CompletionSource.Task.WaitAsync(cancellationToken);
    }

    /// <summary>
    /// Executes a PIN DO command
    /// </summary>
    /// <param name="doStates">8-bit DO states string</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task<bool> PinDOAsync(string doStates, CancellationToken cancellationToken = default)
    {
        var command = DioCommand.CreatePinDO(doStates);
        command.CompletionSource = new TaskCompletionSource<bool>();
        
        QueueWrite(command);
        
        return await command.CompletionSource.Task.WaitAsync(cancellationToken);
    }

    /// <summary>
    /// Main polling loop
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    private async Task PollingLoopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Polling loop started for endpoint {EndpointId}", _configuration.Id);

        try
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                // Check connection state - only poll when connected
                if (_connectionManager.State != ConnectionState.Connected)
                {
                    // Wait longer when not connected to avoid spam
                    await Task.Delay(1000, cancellationToken);
                    continue;
                }

                // Check if there are any write requests to process first
                if (!_writeQueue.IsEmpty)
                {
                    // Let write processing take priority
                    await Task.Delay(10, cancellationToken);
                    continue;
                }

                // Poll DI and DO states
                await PollDioStatesAsync(cancellationToken);

                // Wait for polling interval
                if (_configuration.PollingIntervalMilliseconds > 0)
                {
                    await Task.Delay(_configuration.PollingIntervalMilliseconds, cancellationToken);
                }
            }
        }
        catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
        {
            // Expected when stopping
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in polling loop for endpoint {EndpointId}", _configuration.Id);
            OnPollingError(new DioErrorEventArgs(_configuration.Id, 
                $"Polling loop error: {ex.Message}", exception: ex, shouldReconnect: true));
        }

        _logger.LogInformation("Polling loop stopped for endpoint {EndpointId}", _configuration.Id);
    }

    /// <summary>
    /// Write processing loop
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    private async Task WriteProcessingLoopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Write processing loop started for endpoint {EndpointId}", _configuration.Id);

        try
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                // Check connection state - only process writes when connected
                if (_connectionManager.State != ConnectionState.Connected)
                {
                    // Wait longer when not connected and fail pending commands
                    if (_writeQueue.TryDequeue(out var failedCommand))
                    {
                        failedCommand.CompletionSource?.SetException(
                            new InvalidOperationException("Device not connected"));
                    }
                    await Task.Delay(1000, cancellationToken);
                    continue;
                }

                if (_writeQueue.TryDequeue(out var command))
                {
                    await ProcessWriteCommandAsync(command, cancellationToken);
                }
                else
                {
                    await Task.Delay(10, cancellationToken);
                }
            }
        }
        catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
        {
            // Expected when stopping
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in write processing loop for endpoint {EndpointId}", _configuration.Id);
            OnPollingError(new DioErrorEventArgs(_configuration.Id,
                $"Write processing error: {ex.Message}", exception: ex));
        }

        _logger.LogInformation("Write processing loop stopped for endpoint {EndpointId}", _configuration.Id);
    }

    /// <summary>
    /// Polls DI and DO states from the device
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    private async Task PollDioStatesAsync(CancellationToken cancellationToken)
    {
        try
        {
            // Poll DI states
            var diStates = await _commandProcessor.GetDIAsync(cancellationToken);
            if (diStates != null && diStates.Length >= 24)
            {
                var newState = _currentState?.Clone() ?? new DioState { EndpointId = _configuration.Id };
                newState.SetDIFromBinaryString(diStates.Substring(0, 24));

                await ProcessStateChangeAsync(newState, true, false);
            }

            // Poll DO states
            var doStates = await _commandProcessor.GetDOAsync(cancellationToken);
            if (doStates != null && doStates.Length >= 8)
            {
                var newState = _currentState?.Clone() ?? new DioState { EndpointId = _configuration.Id };
                newState.SetDOFromBinaryString(doStates.Substring(0, 8));

                await ProcessStateChangeAsync(newState, false, true);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error polling DIO states for endpoint {EndpointId}", _configuration.Id);
            OnPollingError(new DioErrorEventArgs(_configuration.Id,
                $"Polling error: {ex.Message}", exception: ex));
        }
    }

    /// <summary>
    /// Processes a write command
    /// </summary>
    /// <param name="command">Command to process</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    private async Task ProcessWriteCommandAsync(DioCommand command, CancellationToken cancellationToken)
    {
        try
        {
            bool success = command.CommandType switch
            {
                Enums.DioCommandType.SetDO => await _commandProcessor.SetDOAsync(command.DoStates!, cancellationToken),
                Enums.DioCommandType.ClearDO => await _commandProcessor.ClearDOAsync(command.DoStates!, cancellationToken),
                Enums.DioCommandType.PinDO => await _commandProcessor.PinDOAsync(command.DoStates!, cancellationToken),
                _ => false
            };

            // Complete the task if it has a completion source
            command.CompletionSource?.SetResult(success);

            if (success)
            {
                _logger.LogDebug("Write command executed successfully for endpoint {EndpointId}: {Command}",
                    _configuration.Id, command.ToAsciiString());

                // After successful write, immediately poll to get updated state
                await Task.Delay(50, cancellationToken); // Small delay to allow device to process
                await PollDioStatesAsync(cancellationToken);
            }
            else
            {
                _logger.LogWarning("Write command failed for endpoint {EndpointId}: {Command}",
                    _configuration.Id, command.ToAsciiString());
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing write command for endpoint {EndpointId}: {Command}",
                _configuration.Id, command.ToAsciiString());

            command.CompletionSource?.SetException(ex);

            OnPollingError(new DioErrorEventArgs(_configuration.Id,
                $"Write command error: {ex.Message}", command.CommandType, ex));
        }
    }

    /// <summary>
    /// Processes state changes and applies debouncing
    /// </summary>
    /// <param name="newState">New DIO state</param>
    /// <param name="checkDI">Whether to check DI changes</param>
    /// <param name="checkDO">Whether to check DO changes</param>
    /// <returns>Task representing the async operation</returns>
    private async Task ProcessStateChangeAsync(DioState newState, bool checkDI, bool checkDO)
    {
        if (_currentState == null)
        {
            _currentState = newState;
            return;
        }

        var hasChanges = false;
        var changedPoints = new List<string>();

        // Check for DI changes with debouncing
        if (checkDI)
        {
            for (int i = 0; i < 24; i++)
            {
                if (_currentState.DigitalInputs[i] != newState.DigitalInputs[i])
                {
                    var pointKey = $"DI{i}";
                    var now = DateTime.UtcNow;

                    // Apply debouncing
                    if (_debounceTimestamps.TryGetValue(pointKey, out var lastChangeTime))
                    {
                        var timeSinceLastChange = now - lastChangeTime;
                        if (timeSinceLastChange.TotalMilliseconds < _configuration.DebounceTimeMilliseconds)
                        {
                            continue; // Skip this change due to debouncing
                        }
                    }

                    _debounceTimestamps[pointKey] = now;
                    changedPoints.Add($"DI{i}={newState.DigitalInputs[i]}");
                    hasChanges = true;
                }
            }
        }

        // Check for DO changes (no debouncing needed for outputs)
        if (checkDO)
        {
            for (int i = 0; i < 8; i++)
            {
                if (_currentState.DigitalOutputs[i] != newState.DigitalOutputs[i])
                {
                    changedPoints.Add($"DO{i}={newState.DigitalOutputs[i]}");
                    hasChanges = true;
                }
            }
        }

        // Update state and raise event if there are changes
        if (hasChanges)
        {
            _previousState = _currentState.Clone();

            // Update only the changed parts
            if (checkDI)
            {
                _currentState.DigitalInputs = (bool[])newState.DigitalInputs.Clone();
            }
            if (checkDO)
            {
                _currentState.DigitalOutputs = (bool[])newState.DigitalOutputs.Clone();
            }
            _currentState.Timestamp = DateTime.UtcNow;

            _logger.LogInformation("DIO state changed for endpoint {EndpointId}: {Changes}",
                _configuration.Id, string.Join(", ", changedPoints));

            OnDioValueChanged(new DioValueChangedEventArgs(_configuration.Id, _previousState, _currentState));
        }

        await Task.CompletedTask;
    }

    /// <summary>
    /// Handles command processor errors
    /// </summary>
    /// <param name="sender">Event sender</param>
    /// <param name="e">Error event arguments</param>
    private void OnCommandError(object? sender, DioErrorEventArgs e)
    {
        OnPollingError(e);
    }

    /// <summary>
    /// Raises the DioValueChanged event
    /// </summary>
    /// <param name="e">Event arguments</param>
    protected virtual void OnDioValueChanged(DioValueChangedEventArgs e)
    {
        DioValueChanged?.Invoke(this, e);
    }

    /// <summary>
    /// Raises the PollingError event
    /// </summary>
    /// <param name="e">Event arguments</param>
    protected virtual void OnPollingError(DioErrorEventArgs e)
    {
        PollingError?.Invoke(this, e);
    }

    /// <summary>
    /// Disposes the polling engine
    /// </summary>
    public void Dispose()
    {
        if (_disposed) return;

        _disposed = true;

        try
        {
            // Stop the engine
            StopAsync().GetAwaiter().GetResult();

            // Cancel all pending write commands
            while (_writeQueue.TryDequeue(out var command))
            {
                command.CompletionSource?.SetCanceled();
            }

            // Unsubscribe from events
            _commandProcessor.CommandError -= OnCommandError;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during disposal of PollingEngine for endpoint {EndpointId}",
                _configuration.Id);
        }
        finally
        {
            _pollingSemaphore?.Dispose();
            _cancellationTokenSource?.Dispose();
        }
    }
}
