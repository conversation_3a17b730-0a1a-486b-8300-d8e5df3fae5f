using Ngp.Communication.TwentyFourDioPoller.Models;

namespace Ngp.Communication.TwentyFourDioPoller.Events;

/// <summary>
/// Event arguments for DI/DO value changes
/// </summary>
public class DioValueChangedEventArgs : EventArgs
{
    /// <summary>
    /// Gets the endpoint identifier
    /// </summary>
    public string EndpointId { get; }

    /// <summary>
    /// Gets the previous DIO state
    /// </summary>
    public DioState PreviousState { get; }

    /// <summary>
    /// Gets the current DIO state
    /// </summary>
    public DioState CurrentState { get; }

    /// <summary>
    /// Gets the timestamp when the value change occurred
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// Gets the changed DI point indices (0-23)
    /// </summary>
    public int[] ChangedDIPoints { get; }

    /// <summary>
    /// Gets the changed DO point indices (0-7)
    /// </summary>
    public int[] ChangedDOPoints { get; }

    /// <summary>
    /// Gets whether any DI values changed
    /// </summary>
    public bool HasDIChanges => ChangedDIPoints.Length > 0;

    /// <summary>
    /// Gets whether any DO values changed
    /// </summary>
    public bool HasDOChanges => ChangedDOPoints.Length > 0;

    /// <summary>
    /// Initializes a new instance of the DioValueChangedEventArgs class
    /// </summary>
    /// <param name="endpointId">The endpoint identifier</param>
    /// <param name="previousState">The previous DIO state</param>
    /// <param name="currentState">The current DIO state</param>
    public DioValueChangedEventArgs(
        string endpointId,
        DioState previousState,
        DioState currentState)
    {
        EndpointId = endpointId;
        PreviousState = previousState;
        CurrentState = currentState;
        Timestamp = DateTime.UtcNow;

        // Calculate changed DI points
        var changedDI = new List<int>();
        for (int i = 0; i < 24; i++)
        {
            if (previousState.DigitalInputs[i] != currentState.DigitalInputs[i])
            {
                changedDI.Add(i);
            }
        }
        ChangedDIPoints = changedDI.ToArray();

        // Calculate changed DO points
        var changedDO = new List<int>();
        for (int i = 0; i < 8; i++)
        {
            if (previousState.DigitalOutputs[i] != currentState.DigitalOutputs[i])
            {
                changedDO.Add(i);
            }
        }
        ChangedDOPoints = changedDO.ToArray();
    }

    /// <summary>
    /// Gets a summary of the changes
    /// </summary>
    /// <returns>String describing the changes</returns>
    public string GetChangesSummary()
    {
        var summary = new List<string>();

        if (HasDIChanges)
        {
            summary.Add($"DI changes: {string.Join(", ", ChangedDIPoints.Select(i => $"DI{i}={CurrentState.DigitalInputs[i]}"))}");
        }

        if (HasDOChanges)
        {
            summary.Add($"DO changes: {string.Join(", ", ChangedDOPoints.Select(i => $"DO{i}={CurrentState.DigitalOutputs[i]}"))}");
        }

        return string.Join("; ", summary);
    }
}
