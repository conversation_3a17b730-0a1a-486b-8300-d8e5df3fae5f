using Ngp.Communication.TwentyFourDioPoller.Enums;

namespace Ngp.Communication.TwentyFourDioPoller.Events;

/// <summary>
/// Event arguments for 24Dio communication errors
/// </summary>
public class DioErrorEventArgs : EventArgs
{
    /// <summary>
    /// Gets the endpoint identifier
    /// </summary>
    public string EndpointId { get; }

    /// <summary>
    /// Gets the command type that caused the error
    /// </summary>
    public DioCommandType? CommandType { get; }

    /// <summary>
    /// Gets the error message
    /// </summary>
    public string ErrorMessage { get; }

    /// <summary>
    /// Gets the exception that caused the error, if any
    /// </summary>
    public Exception? Exception { get; }

    /// <summary>
    /// Gets the timestamp when the error occurred
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// Gets the request data that caused the error, if any
    /// </summary>
    public byte[]? RequestData { get; }

    /// <summary>
    /// Gets the response data that contained the error, if any
    /// </summary>
    public byte[]? ResponseData { get; }

    /// <summary>
    /// Gets whether this error should trigger a reconnection
    /// </summary>
    public bool ShouldReconnect { get; }

    /// <summary>
    /// Initializes a new instance of the DioErrorEventArgs class
    /// </summary>
    /// <param name="endpointId">The endpoint identifier</param>
    /// <param name="errorMessage">The error message</param>
    /// <param name="commandType">Optional command type that caused the error</param>
    /// <param name="exception">Optional exception</param>
    /// <param name="requestData">Optional request data</param>
    /// <param name="responseData">Optional response data</param>
    /// <param name="shouldReconnect">Whether this error should trigger a reconnection</param>
    public DioErrorEventArgs(
        string endpointId,
        string errorMessage,
        DioCommandType? commandType = null,
        Exception? exception = null,
        byte[]? requestData = null,
        byte[]? responseData = null,
        bool shouldReconnect = false)
    {
        EndpointId = endpointId;
        ErrorMessage = errorMessage;
        CommandType = commandType;
        Exception = exception;
        Timestamp = DateTime.UtcNow;
        RequestData = requestData;
        ResponseData = responseData;
        ShouldReconnect = shouldReconnect;
    }
}
