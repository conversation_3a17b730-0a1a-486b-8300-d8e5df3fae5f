using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;
using Ngp.Communication.NotifyEngine.Enums;
using Ngp.Communication.NotifyEngine.Events;
using Ngp.Communication.NotifyEngine.Interfaces;
using Ngp.Communication.NotifyEngine.Models;
using Ngp.Shared.Interfaces;

namespace Ngp.Communication.NotifyEngine.Providers;

/// <summary>
/// SIP message notification provider using dependency injection
/// </summary>
public class SipMessageNotificationProvider : INotificationProvider
{
    private readonly ILogger<SipMessageNotificationProvider>? _logger;
    private readonly ISipMessageService? _sipMessageService;
    private SemaphoreSlim _sendSemaphore;
    
    private SipMessageConfiguration? _configuration;
    private bool _disposed = false;
    private bool _isAvailable = false;

    /// <inheritdoc />
    public NotificationChannel Channel => NotificationChannel.SipMessage;

    /// <inheritdoc />
    public bool IsAvailable => _isAvailable && _sipMessageService?.IsAvailable == true;

    /// <inheritdoc />
    public int MaxRecipientsPerMessage => 1; // SIP messages are typically sent individually

    /// <inheritdoc />
    public event EventHandler<NotificationProviderStateChangedEventArgs>? StateChanged;

    /// <inheritdoc />
    public event EventHandler<NotificationStatusChangedEventArgs>? NotificationStatusChanged;

    /// <inheritdoc />
    public event EventHandler<NotificationErrorEventArgs>? NotificationError;

    /// <summary>
    /// Initializes a new instance of the SipMessageNotificationProvider class
    /// </summary>
    /// <param name="sipMessageService">SIP message service (injected)</param>
    /// <param name="logger">Optional logger</param>
    public SipMessageNotificationProvider(
        ISipMessageService? sipMessageService = null,
        ILogger<SipMessageNotificationProvider>? logger = null)
    {
        _sipMessageService = sipMessageService;
        _logger = logger;
        _sendSemaphore = new SemaphoreSlim(1, 1); // Default to single concurrent operation

        // Subscribe to SIP service availability changes
        if (_sipMessageService != null)
        {
            _sipMessageService.AvailabilityChanged += OnSipServiceAvailabilityChanged;
        }
    }

    /// <inheritdoc />
    public async Task InitializeAsync(NotificationConfiguration configuration, CancellationToken cancellationToken = default)
    {
        if (configuration is not SipMessageConfiguration sipConfig)
        {
            throw new ArgumentException("Invalid configuration type for SIP message provider", nameof(configuration));
        }

        _configuration = sipConfig;

        try
        {
            // Update semaphore for concurrency (dispose and recreate with new limit)
            _sendSemaphore.Dispose();
            _sendSemaphore = new SemaphoreSlim(_configuration.MaxConcurrency, _configuration.MaxConcurrency);

            // Check if SIP service is available
            var sipAvailable = _sipMessageService?.IsAvailable ?? false;
            _isAvailable = _configuration.Enabled && sipAvailable;

            _logger?.LogInformation("SIP message provider initialized. SIP service available: {SipAvailable}, Provider enabled: {Enabled}",
                sipAvailable, _configuration.Enabled);

            OnStateChanged(_isAvailable, sipAvailable ? "Provider initialized successfully" : "SIP service not available");
        }
        catch (Exception ex)
        {
            _isAvailable = false;
            _logger?.LogError(ex, "Failed to initialize SIP message provider");
            OnStateChanged(false, $"Initialization failed: {ex.Message}");
            throw;
        }
    }

    /// <inheritdoc />
    public async Task SendAsync(NotificationMessage message, CancellationToken cancellationToken = default)
    {
        if (!IsAvailable || _configuration == null || _sipMessageService == null)
        {
            throw new InvalidOperationException("SIP message provider is not available or not initialized");
        }

        if (message.Recipients.Count == 0)
        {
            throw new ArgumentException("No recipients specified", nameof(message));
        }

        await _sendSemaphore.WaitAsync(cancellationToken);
        
        try
        {
            OnNotificationStatusChanged(message, message.Status, NotificationStatus.Sending);
            message.Status = NotificationStatus.Sending;
            message.SentAt = DateTime.UtcNow;

            // Send SIP message to each recipient individually
            var sendTasks = message.Recipients.Select(async recipient =>
            {
                try
                {
                    var messageId = await _sipMessageService.SendMessageAsync(
                        recipient, 
                        message.Content, 
                        _configuration.ContentType, 
                        cancellationToken);
                    
                    _logger?.LogDebug("SIP message sent to {Recipient} with ID {MessageId}", recipient, messageId);
                    return messageId;
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "Failed to send SIP message to {Recipient}", recipient);
                    throw;
                }
            });

            var messageIds = await Task.WhenAll(sendTasks);

            // Store message IDs in metadata for tracking
            message.Metadata["SipMessageIds"] = messageIds.ToList();

            OnNotificationStatusChanged(message, NotificationStatus.Sending, NotificationStatus.Delivered);
            message.Status = NotificationStatus.Delivered;
            message.DeliveredAt = DateTime.UtcNow;

            _logger?.LogDebug("SIP messages sent successfully to {RecipientCount} recipients for message {MessageId}",
                message.Recipients.Count, message.MessageId);
        }
        catch (Exception ex)
        {
            var errorMessage = $"Failed to send SIP message: {ex.Message}";
            message.ErrorMessage = errorMessage;
            message.Status = NotificationStatus.Failed;

            _logger?.LogError(ex, "Failed to send SIP message for message {MessageId}", message.MessageId);
            
            OnNotificationError(message, errorMessage, ex, message.CanRetry);
            OnNotificationStatusChanged(message, NotificationStatus.Sending, NotificationStatus.Failed);
            
            throw;
        }
        finally
        {
            _sendSemaphore.Release();
        }
    }

    /// <inheritdoc />
    public bool ValidateRecipients(IEnumerable<string> recipients)
    {
        // SIP extensions are typically numeric (3-5 digits) or alphanumeric
        var extensionRegex = new Regex(@"^[a-zA-Z0-9]{3,20}$");
        return recipients.All(extension => extensionRegex.IsMatch(extension));
    }

    /// <summary>
    /// Handles SIP service availability changes
    /// </summary>
    /// <param name="sender">Event sender</param>
    /// <param name="e">Event arguments</param>
    private void OnSipServiceAvailabilityChanged(object? sender, SipServiceAvailabilityChangedEventArgs e)
    {
        var wasAvailable = _isAvailable;
        _isAvailable = _configuration?.Enabled == true && e.IsAvailable;

        if (wasAvailable != _isAvailable)
        {
            _logger?.LogInformation("SIP message provider availability changed: {IsAvailable}. Reason: {Reason}",
                _isAvailable, e.Reason);
            
            OnStateChanged(_isAvailable, e.Reason);
        }
    }

    /// <summary>
    /// Raises the StateChanged event
    /// </summary>
    /// <param name="isAvailable">Whether the provider is available</param>
    /// <param name="reason">Reason for the change</param>
    private void OnStateChanged(bool isAvailable, string? reason = null)
    {
        StateChanged?.Invoke(this, new NotificationProviderStateChangedEventArgs(Channel, isAvailable, reason));
    }

    /// <summary>
    /// Raises the NotificationStatusChanged event
    /// </summary>
    /// <param name="message">Notification message</param>
    /// <param name="previousStatus">Previous status</param>
    /// <param name="currentStatus">Current status</param>
    private void OnNotificationStatusChanged(NotificationMessage message, NotificationStatus previousStatus, NotificationStatus currentStatus)
    {
        NotificationStatusChanged?.Invoke(this, new NotificationStatusChangedEventArgs(message, previousStatus, currentStatus));
    }

    /// <summary>
    /// Raises the NotificationError event
    /// </summary>
    /// <param name="message">Notification message</param>
    /// <param name="errorMessage">Error message</param>
    /// <param name="exception">Exception</param>
    /// <param name="willRetry">Whether retry will be attempted</param>
    private void OnNotificationError(NotificationMessage message, string errorMessage, Exception? exception, bool willRetry)
    {
        NotificationError?.Invoke(this, new NotificationErrorEventArgs(message, Channel, errorMessage, exception, willRetry));
    }

    /// <inheritdoc />
    public void Dispose()
    {
        if (_disposed) return;

        // Unsubscribe from events
        if (_sipMessageService != null)
        {
            _sipMessageService.AvailabilityChanged -= OnSipServiceAvailabilityChanged;
        }

        _sendSemaphore?.Dispose();
        _disposed = true;
    }
}
