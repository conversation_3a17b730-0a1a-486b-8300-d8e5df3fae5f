using System.Net;
using System.Net.Mail;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;
using Ngp.Communication.NotifyEngine.Enums;
using Ngp.Communication.NotifyEngine.Events;
using Ngp.Communication.NotifyEngine.Interfaces;
using Ngp.Communication.NotifyEngine.Models;

namespace Ngp.Communication.NotifyEngine.Providers;

/// <summary>
/// Email notification provider using SMTP
/// </summary>
public class EmailNotificationProvider : INotificationProvider
{
    private readonly ILogger<EmailNotificationProvider>? _logger;
    private SemaphoreSlim _sendSemaphore;
    
    private EmailConfiguration? _configuration;
    private SmtpClient? _smtpClient;
    private bool _disposed = false;
    private bool _isAvailable = false;

    /// <inheritdoc />
    public NotificationChannel Channel => NotificationChannel.Email;

    /// <inheritdoc />
    public bool IsAvailable => _isAvailable && _smtpClient != null;

    /// <inheritdoc />
    public int MaxRecipientsPerMessage => 100; // Reasonable limit for email

    /// <inheritdoc />
    public event EventHandler<NotificationProviderStateChangedEventArgs>? StateChanged;

    /// <inheritdoc />
    public event EventHandler<NotificationStatusChangedEventArgs>? NotificationStatusChanged;

    /// <inheritdoc />
    public event EventHandler<NotificationErrorEventArgs>? NotificationError;

    /// <summary>
    /// Initializes a new instance of the EmailNotificationProvider class
    /// </summary>
    /// <param name="logger">Optional logger</param>
    public EmailNotificationProvider(ILogger<EmailNotificationProvider>? logger = null)
    {
        _logger = logger;
        _sendSemaphore = new SemaphoreSlim(1, 1); // Default to single concurrent operation
    }

    /// <inheritdoc />
    public async Task InitializeAsync(NotificationConfiguration configuration, CancellationToken cancellationToken = default)
    {
        if (configuration is not EmailConfiguration emailConfig)
        {
            throw new ArgumentException("Invalid configuration type for email provider", nameof(configuration));
        }

        _configuration = emailConfig;

        try
        {
            // Dispose existing client if any
            _smtpClient?.Dispose();

            // Create new SMTP client
            _smtpClient = new SmtpClient(_configuration.SmtpHost, _configuration.SmtpPort)
            {
                EnableSsl = _configuration.UseSsl,
                UseDefaultCredentials = false,
                Credentials = new NetworkCredential(_configuration.Username, _configuration.Password),
                Timeout = _configuration.TimeoutMs
            };

            // Update semaphore for concurrency (dispose and recreate with new limit)
            _sendSemaphore.Dispose();
            _sendSemaphore = new SemaphoreSlim(_configuration.MaxConcurrency, _configuration.MaxConcurrency);

            _isAvailable = true;
            _logger?.LogInformation("Email provider initialized successfully for {Host}:{Port}", 
                _configuration.SmtpHost, _configuration.SmtpPort);

            OnStateChanged(true, "Provider initialized successfully");
        }
        catch (Exception ex)
        {
            _isAvailable = false;
            _logger?.LogError(ex, "Failed to initialize email provider");
            OnStateChanged(false, $"Initialization failed: {ex.Message}");
            throw;
        }
    }

    /// <inheritdoc />
    public async Task SendAsync(NotificationMessage message, CancellationToken cancellationToken = default)
    {
        if (!IsAvailable || _configuration == null || _smtpClient == null)
        {
            throw new InvalidOperationException("Email provider is not available or not initialized");
        }

        if (message.Recipients.Count == 0)
        {
            throw new ArgumentException("No recipients specified", nameof(message));
        }

        if (message.Recipients.Count > MaxRecipientsPerMessage)
        {
            throw new ArgumentException($"Too many recipients. Maximum allowed: {MaxRecipientsPerMessage}", nameof(message));
        }

        await _sendSemaphore.WaitAsync(cancellationToken);
        
        try
        {
            OnNotificationStatusChanged(message, message.Status, NotificationStatus.Sending);
            message.Status = NotificationStatus.Sending;
            message.SentAt = DateTime.UtcNow;

            using var mailMessage = CreateMailMessage(message);
            
            await _smtpClient.SendMailAsync(mailMessage, cancellationToken);

            OnNotificationStatusChanged(message, NotificationStatus.Sending, NotificationStatus.Delivered);
            message.Status = NotificationStatus.Delivered;
            message.DeliveredAt = DateTime.UtcNow;

            _logger?.LogDebug("Email sent successfully to {RecipientCount} recipients for message {MessageId}",
                message.Recipients.Count, message.MessageId);
        }
        catch (Exception ex)
        {
            var errorMessage = $"Failed to send email: {ex.Message}";
            message.ErrorMessage = errorMessage;
            message.Status = NotificationStatus.Failed;

            _logger?.LogError(ex, "Failed to send email for message {MessageId}", message.MessageId);
            
            OnNotificationError(message, errorMessage, ex, message.CanRetry);
            OnNotificationStatusChanged(message, NotificationStatus.Sending, NotificationStatus.Failed);
            
            throw;
        }
        finally
        {
            _sendSemaphore.Release();
        }
    }

    /// <inheritdoc />
    public bool ValidateRecipients(IEnumerable<string> recipients)
    {
        var emailRegex = new Regex(@"^[^@\s]+@[^@\s]+\.[^@\s]+$", RegexOptions.IgnoreCase);
        return recipients.All(email => emailRegex.IsMatch(email));
    }

    /// <summary>
    /// Creates a mail message from notification message
    /// </summary>
    /// <param name="message">Notification message</param>
    /// <returns>Mail message</returns>
    private MailMessage CreateMailMessage(NotificationMessage message)
    {
        if (_configuration == null)
        {
            throw new InvalidOperationException("Configuration is not set");
        }

        var mailMessage = new MailMessage
        {
            From = new MailAddress(_configuration.FromEmail, _configuration.FromName),
            Subject = message.Subject ?? "Notification",
            Body = message.Content,
            IsBodyHtml = false
        };

        // Add recipients
        foreach (var recipient in message.Recipients)
        {
            mailMessage.To.Add(recipient);
        }

        return mailMessage;
    }

    /// <summary>
    /// Raises the StateChanged event
    /// </summary>
    /// <param name="isAvailable">Whether the provider is available</param>
    /// <param name="reason">Reason for the change</param>
    private void OnStateChanged(bool isAvailable, string? reason = null)
    {
        StateChanged?.Invoke(this, new NotificationProviderStateChangedEventArgs(Channel, isAvailable, reason));
    }

    /// <summary>
    /// Raises the NotificationStatusChanged event
    /// </summary>
    /// <param name="message">Notification message</param>
    /// <param name="previousStatus">Previous status</param>
    /// <param name="currentStatus">Current status</param>
    private void OnNotificationStatusChanged(NotificationMessage message, NotificationStatus previousStatus, NotificationStatus currentStatus)
    {
        NotificationStatusChanged?.Invoke(this, new NotificationStatusChangedEventArgs(message, previousStatus, currentStatus));
    }

    /// <summary>
    /// Raises the NotificationError event
    /// </summary>
    /// <param name="message">Notification message</param>
    /// <param name="errorMessage">Error message</param>
    /// <param name="exception">Exception</param>
    /// <param name="willRetry">Whether retry will be attempted</param>
    private void OnNotificationError(NotificationMessage message, string errorMessage, Exception? exception, bool willRetry)
    {
        NotificationError?.Invoke(this, new NotificationErrorEventArgs(message, Channel, errorMessage, exception, willRetry));
    }

    /// <inheritdoc />
    public void Dispose()
    {
        if (_disposed) return;

        _smtpClient?.Dispose();
        _sendSemaphore?.Dispose();
        _disposed = true;
    }
}
