using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;
using Ngp.Communication.NotifyEngine.Enums;
using Ngp.Communication.NotifyEngine.Events;
using Ngp.Communication.NotifyEngine.Interfaces;
using Ngp.Communication.NotifyEngine.Models;

namespace Ngp.Communication.NotifyEngine.Providers;

/// <summary>
/// SMS notification provider for Taiwan SMS services
/// </summary>
public class SmsNotificationProvider : INotificationProvider
{
    private readonly ILogger<SmsNotificationProvider>? _logger;
    private readonly HttpClient _httpClient;
    private SemaphoreSlim _sendSemaphore;
    
    private SmsConfiguration? _configuration;
    private bool _disposed = false;
    private bool _isAvailable = false;

    /// <inheritdoc />
    public NotificationChannel Channel => NotificationChannel.Sms;

    /// <inheritdoc />
    public bool IsAvailable => _isAvailable && _configuration != null;

    /// <inheritdoc />
    public int MaxRecipientsPerMessage => 50; // Reasonable limit for SMS

    /// <inheritdoc />
    public event EventHandler<NotificationProviderStateChangedEventArgs>? StateChanged;

    /// <inheritdoc />
    public event EventHandler<NotificationStatusChangedEventArgs>? NotificationStatusChanged;

    /// <inheritdoc />
    public event EventHandler<NotificationErrorEventArgs>? NotificationError;

    /// <summary>
    /// Initializes a new instance of the SmsNotificationProvider class
    /// </summary>
    /// <param name="logger">Optional logger</param>
    public SmsNotificationProvider(ILogger<SmsNotificationProvider>? logger = null)
    {
        _logger = logger;
        _httpClient = new HttpClient();
        _sendSemaphore = new SemaphoreSlim(1, 1); // Default to single concurrent operation
    }

    /// <inheritdoc />
    public async Task InitializeAsync(NotificationConfiguration configuration, CancellationToken cancellationToken = default)
    {
        if (configuration is not SmsConfiguration smsConfig)
        {
            throw new ArgumentException("Invalid configuration type for SMS provider", nameof(configuration));
        }

        _configuration = smsConfig;

        try
        {
            // Configure HTTP client
            _httpClient.Timeout = TimeSpan.FromMilliseconds(_configuration.TimeoutMs);
            
            // Update semaphore for concurrency (dispose and recreate with new limit)
            _sendSemaphore.Dispose();
            _sendSemaphore = new SemaphoreSlim(_configuration.MaxConcurrency, _configuration.MaxConcurrency);

            // Test connection by making a simple request (if API supports it)
            await TestConnectionAsync(cancellationToken);

            _isAvailable = true;
            _logger?.LogInformation("SMS provider initialized successfully for API: {ApiUrl}", 
                _configuration.ApiUrl);

            OnStateChanged(true, "Provider initialized successfully");
        }
        catch (Exception ex)
        {
            _isAvailable = false;
            _logger?.LogError(ex, "Failed to initialize SMS provider");
            OnStateChanged(false, $"Initialization failed: {ex.Message}");
            throw;
        }
    }

    /// <inheritdoc />
    public async Task SendAsync(NotificationMessage message, CancellationToken cancellationToken = default)
    {
        if (!IsAvailable || _configuration == null)
        {
            throw new InvalidOperationException("SMS provider is not available or not initialized");
        }

        if (message.Recipients.Count == 0)
        {
            throw new ArgumentException("No recipients specified", nameof(message));
        }

        if (message.Recipients.Count > MaxRecipientsPerMessage)
        {
            throw new ArgumentException($"Too many recipients. Maximum allowed: {MaxRecipientsPerMessage}", nameof(message));
        }

        await _sendSemaphore.WaitAsync(cancellationToken);
        
        try
        {
            OnNotificationStatusChanged(message, message.Status, NotificationStatus.Sending);
            message.Status = NotificationStatus.Sending;
            message.SentAt = DateTime.UtcNow;

            // Send SMS to each recipient (Taiwan SMS services typically require individual requests)
            var tasks = message.Recipients.Select(recipient => 
                SendSmsToRecipientAsync(recipient, message.Content, cancellationToken));
            
            await Task.WhenAll(tasks);

            OnNotificationStatusChanged(message, NotificationStatus.Sending, NotificationStatus.Delivered);
            message.Status = NotificationStatus.Delivered;
            message.DeliveredAt = DateTime.UtcNow;

            _logger?.LogDebug("SMS sent successfully to {RecipientCount} recipients for message {MessageId}",
                message.Recipients.Count, message.MessageId);
        }
        catch (Exception ex)
        {
            var errorMessage = $"Failed to send SMS: {ex.Message}";
            message.ErrorMessage = errorMessage;
            message.Status = NotificationStatus.Failed;

            _logger?.LogError(ex, "Failed to send SMS for message {MessageId}", message.MessageId);
            
            OnNotificationError(message, errorMessage, ex, message.CanRetry);
            OnNotificationStatusChanged(message, NotificationStatus.Sending, NotificationStatus.Failed);
            
            throw;
        }
        finally
        {
            _sendSemaphore.Release();
        }
    }

    /// <inheritdoc />
    public bool ValidateRecipients(IEnumerable<string> recipients)
    {
        // Taiwan mobile number format: 09xxxxxxxx (10 digits starting with 09)
        var mobileRegex = new Regex(@"^09\d{8}$");
        return recipients.All(phone => mobileRegex.IsMatch(phone.Replace("-", "").Replace(" ", "")));
    }

    /// <summary>
    /// Sends SMS to a single recipient using TWSMS API format
    /// </summary>
    /// <param name="recipient">Phone number</param>
    /// <param name="content">Message content</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    private async Task SendSmsToRecipientAsync(string recipient, string content, CancellationToken cancellationToken)
    {
        if (_configuration == null)
        {
            throw new InvalidOperationException("Configuration is not set");
        }

        // TWSMS uses GET request with URL parameters
        // Format: http://api.twsms.com/json/sms_send.php?username={username}&password={password}&mobile={mobile}&message={message}
        var encodedMessage = Uri.EscapeDataString(content);
        var apiUrl = $"{_configuration.ApiUrl}?username={_configuration.Username}&password={_configuration.Password}&mobile={recipient}&message={encodedMessage}";

        _logger?.LogDebug("Sending SMS to {Recipient} via TWSMS API", recipient);

        var response = await _httpClient.GetAsync(apiUrl, cancellationToken);

        if (!response.IsSuccessStatusCode)
        {
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            throw new HttpRequestException($"SMS API returned {response.StatusCode}: {responseContent}");
        }

        // Parse TWSMS JSON response to check for delivery status
        var responseText = await response.Content.ReadAsStringAsync(cancellationToken);
        _logger?.LogDebug("SMS API response for {Recipient}: {Response}", recipient, responseText);

        try
        {
            var responseJson = JsonSerializer.Deserialize<JsonElement>(responseText);

            if (responseJson.TryGetProperty("code", out var codeElement))
            {
                var code = codeElement.GetString();
                if (code != "00000")
                {
                    var text = responseJson.TryGetProperty("text", out var textElement)
                        ? textElement.GetString()
                        : "Unknown error";
                    throw new InvalidOperationException($"TWSMS API error - Code: {code}, Message: {text}");
                }

                _logger?.LogDebug("SMS sent successfully to {Recipient} - Code: {Code}", recipient, code);
            }
            else
            {
                _logger?.LogWarning("TWSMS API response missing 'code' field for {Recipient}: {Response}", recipient, responseText);
            }
        }
        catch (JsonException ex)
        {
            _logger?.LogWarning(ex, "Failed to parse TWSMS API response for {Recipient}: {Response}", recipient, responseText);
            // Don't throw here as the HTTP request was successful
        }
    }

    /// <summary>
    /// Tests the connection to the SMS service
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    private async Task TestConnectionAsync(CancellationToken cancellationToken)
    {
        if (_configuration == null)
        {
            throw new InvalidOperationException("Configuration is not set");
        }

        try
        {
            // Simple connectivity test - try to reach the API endpoint
            var request = new HttpRequestMessage(HttpMethod.Head, _configuration.ApiUrl);
            var response = await _httpClient.SendAsync(request, cancellationToken);
            
            _logger?.LogDebug("SMS service connectivity test completed with status: {StatusCode}", response.StatusCode);
        }
        catch (Exception ex)
        {
            _logger?.LogWarning(ex, "SMS service connectivity test failed, but provider will still be marked as available");
            // Don't throw here as some SMS services might not support HEAD requests
        }
    }

    /// <summary>
    /// Raises the StateChanged event
    /// </summary>
    /// <param name="isAvailable">Whether the provider is available</param>
    /// <param name="reason">Reason for the change</param>
    private void OnStateChanged(bool isAvailable, string? reason = null)
    {
        StateChanged?.Invoke(this, new NotificationProviderStateChangedEventArgs(Channel, isAvailable, reason));
    }

    /// <summary>
    /// Raises the NotificationStatusChanged event
    /// </summary>
    /// <param name="message">Notification message</param>
    /// <param name="previousStatus">Previous status</param>
    /// <param name="currentStatus">Current status</param>
    private void OnNotificationStatusChanged(NotificationMessage message, NotificationStatus previousStatus, NotificationStatus currentStatus)
    {
        NotificationStatusChanged?.Invoke(this, new NotificationStatusChangedEventArgs(message, previousStatus, currentStatus));
    }

    /// <summary>
    /// Raises the NotificationError event
    /// </summary>
    /// <param name="message">Notification message</param>
    /// <param name="errorMessage">Error message</param>
    /// <param name="exception">Exception</param>
    /// <param name="willRetry">Whether retry will be attempted</param>
    private void OnNotificationError(NotificationMessage message, string errorMessage, Exception? exception, bool willRetry)
    {
        NotificationError?.Invoke(this, new NotificationErrorEventArgs(message, Channel, errorMessage, exception, willRetry));
    }

    /// <inheritdoc />
    public void Dispose()
    {
        if (_disposed) return;

        _httpClient?.Dispose();
        _sendSemaphore?.Dispose();
        _disposed = true;
    }
}
