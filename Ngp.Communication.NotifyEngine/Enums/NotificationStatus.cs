namespace Ngp.Communication.NotifyEngine.Enums;

/// <summary>
/// Represents the status of a notification
/// </summary>
public enum NotificationStatus
{
    /// <summary>
    /// Notification is queued for sending
    /// </summary>
    Queued = 0,

    /// <summary>
    /// Notification is being sent
    /// </summary>
    Sending = 1,

    /// <summary>
    /// Notification was sent successfully
    /// </summary>
    Sent = 2,

    /// <summary>
    /// Notification delivery was confirmed
    /// </summary>
    Delivered = 3,

    /// <summary>
    /// Notification failed to send
    /// </summary>
    Failed = 4,

    /// <summary>
    /// Notification timed out
    /// </summary>
    Timeout = 5,

    /// <summary>
    /// Notification was rejected by the recipient
    /// </summary>
    Rejected = 6,

    /// <summary>
    /// Notification was cancelled
    /// </summary>
    Cancelled = 7
}

/// <summary>
/// Represents the type of notification channel
/// </summary>
public enum NotificationChannel
{
    /// <summary>
    /// Email notification
    /// </summary>
    Email = 0,

    /// <summary>
    /// SMS notification
    /// </summary>
    Sms = 1,

    /// <summary>
    /// SIP message notification
    /// </summary>
    SipMessage = 2,

    /// <summary>
    /// LINE message notification
    /// </summary>
    Line = 3
}

/// <summary>
/// Represents the priority of a notification
/// </summary>
public enum NotificationPriority
{
    /// <summary>
    /// Low priority
    /// </summary>
    Low = 0,

    /// <summary>
    /// Normal priority
    /// </summary>
    Normal = 1,

    /// <summary>
    /// High priority
    /// </summary>
    High = 2,

    /// <summary>
    /// Critical priority
    /// </summary>
    Critical = 3
}
