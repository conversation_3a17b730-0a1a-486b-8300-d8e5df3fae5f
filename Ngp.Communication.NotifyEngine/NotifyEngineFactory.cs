using Microsoft.Extensions.Logging;
using Ngp.Communication.NotifyEngine.Builders;
using Ngp.Communication.NotifyEngine.Interfaces;

namespace Ngp.Communication.NotifyEngine;

/// <summary>
/// Factory class for creating NotifyEngine instances using Fluent API
/// </summary>
public static class NotifyEngineFactory
{
    /// <summary>
    /// Creates a new NotifyEngine builder
    /// </summary>
    /// <param name="loggerFactory">Optional logger factory</param>
    /// <returns>A new builder instance</returns>
    public static INotifyEngineBuilder Create(ILoggerFactory? loggerFactory = null)
    {
        var builder = new NotifyEngineBuilder();
        
        if (loggerFactory != null)
        {
            builder.WithLogging(loggerFactory);
        }
        
        return builder;
    }

    /// <summary>
    /// Creates a new NotifyEngine builder with email configuration
    /// </summary>
    /// <param name="smtpHost">SMTP server host</param>
    /// <param name="smtpPort">SMTP server port</param>
    /// <param name="username">Username for authentication</param>
    /// <param name="password">Password for authentication</param>
    /// <param name="fromEmail">Sender email address</param>
    /// <param name="fromName">Sender display name</param>
    /// <param name="useSsl">Whether to use SSL/TLS</param>
    /// <param name="loggerFactory">Optional logger factory</param>
    /// <returns>A new builder instance with email configured</returns>
    public static INotifyEngineBuilder CreateWithEmail(
        string smtpHost,
        ushort smtpPort,
        string username,
        string password,
        string fromEmail,
        string fromName,
        bool useSsl = true,
        ILoggerFactory? loggerFactory = null)
    {
        var builder = Create(loggerFactory);
        return builder.WithEmail(smtpHost, smtpPort, username, password, fromEmail, fromName, useSsl);
    }

    /// <summary>
    /// Creates a new NotifyEngine builder with SMS configuration
    /// </summary>
    /// <param name="apiUrl">SMS service API URL</param>
    /// <param name="username">API username</param>
    /// <param name="password">API password</param>
    /// <param name="senderId">Sender ID</param>
    /// <param name="loggerFactory">Optional logger factory</param>
    /// <returns>A new builder instance with SMS configured</returns>
    public static INotifyEngineBuilder CreateWithSms(
        string apiUrl,
        string username,
        string password,
        string senderId,
        ILoggerFactory? loggerFactory = null)
    {
        var builder = Create(loggerFactory);
        return builder.WithSms(apiUrl, username, password, senderId);
    }

    /// <summary>
    /// Creates a new NotifyEngine builder with LINE configuration
    /// </summary>
    /// <param name="channelAccessToken">LINE channel access token</param>
    /// <param name="apiUrl">LINE API URL (optional)</param>
    /// <param name="loggerFactory">Optional logger factory</param>
    /// <returns>A new builder instance with LINE configured</returns>
    public static INotifyEngineBuilder CreateWithLine(
        string channelAccessToken,
        string? apiUrl = null,
        ILoggerFactory? loggerFactory = null)
    {
        var builder = Create(loggerFactory);
        return builder.WithLine(channelAccessToken, apiUrl);
    }
}
