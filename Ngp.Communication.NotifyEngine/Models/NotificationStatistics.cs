using Ngp.Communication.NotifyEngine.Enums;

namespace Ngp.Communication.NotifyEngine.Models;

/// <summary>
/// Represents notification statistics
/// </summary>
public class NotificationStatistics
{
    /// <summary>
    /// Gets or sets the total number of messages sent
    /// </summary>
    public long TotalMessages { get; set; }

    /// <summary>
    /// Gets or sets the number of successfully delivered messages
    /// </summary>
    public long DeliveredMessages { get; set; }

    /// <summary>
    /// Gets or sets the number of failed messages
    /// </summary>
    public long FailedMessages { get; set; }

    /// <summary>
    /// Gets or sets the number of pending messages
    /// </summary>
    public long PendingMessages { get; set; }

    /// <summary>
    /// Gets or sets statistics by channel
    /// </summary>
    public Dictionary<NotificationChannel, ChannelStatistics> ChannelStatistics { get; set; } = new();

    /// <summary>
    /// Gets the delivery rate as a percentage
    /// </summary>
    public double DeliveryRate
    {
        get
        {
            if (TotalMessages == 0) return 0;
            return (double)DeliveredMessages / TotalMessages * 100;
        }
    }

    /// <summary>
    /// Gets the failure rate as a percentage
    /// </summary>
    public double FailureRate
    {
        get
        {
            if (TotalMessages == 0) return 0;
            return (double)FailedMessages / TotalMessages * 100;
        }
    }
}

/// <summary>
/// Represents statistics for a specific notification channel
/// </summary>
public class ChannelStatistics
{
    /// <summary>
    /// Gets or sets the notification channel
    /// </summary>
    public NotificationChannel Channel { get; set; }

    /// <summary>
    /// Gets or sets the total number of messages for this channel
    /// </summary>
    public long TotalMessages { get; set; }

    /// <summary>
    /// Gets or sets the number of successfully delivered messages
    /// </summary>
    public long DeliveredMessages { get; set; }

    /// <summary>
    /// Gets or sets the number of failed messages
    /// </summary>
    public long FailedMessages { get; set; }

    /// <summary>
    /// Gets or sets the number of pending messages
    /// </summary>
    public long PendingMessages { get; set; }

    /// <summary>
    /// Gets or sets the average delivery time in milliseconds
    /// </summary>
    public double AverageDeliveryTimeMs { get; set; }

    /// <summary>
    /// Gets or sets whether the channel is currently available
    /// </summary>
    public bool IsAvailable { get; set; }

    /// <summary>
    /// Gets the delivery rate as a percentage
    /// </summary>
    public double DeliveryRate
    {
        get
        {
            if (TotalMessages == 0) return 0;
            return (double)DeliveredMessages / TotalMessages * 100;
        }
    }

    /// <summary>
    /// Gets the failure rate as a percentage
    /// </summary>
    public double FailureRate
    {
        get
        {
            if (TotalMessages == 0) return 0;
            return (double)FailedMessages / TotalMessages * 100;
        }
    }
}
