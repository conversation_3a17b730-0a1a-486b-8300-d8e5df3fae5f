using Ngp.Communication.NotifyEngine.Enums;

namespace Ngp.Communication.NotifyEngine.Models;

/// <summary>
/// Represents a notification message
/// </summary>
public class NotificationMessage
{
    /// <summary>
    /// Gets or sets the unique message identifier
    /// </summary>
    public string MessageId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// Gets or sets the notification channel
    /// </summary>
    public NotificationChannel Channel { get; set; }

    /// <summary>
    /// Gets or sets the message subject (for email)
    /// </summary>
    public string? Subject { get; set; }

    /// <summary>
    /// Gets or sets the message content
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the recipients
    /// </summary>
    public List<string> Recipients { get; set; } = new();

    /// <summary>
    /// Gets or sets the message priority
    /// </summary>
    public NotificationPriority Priority { get; set; } = NotificationPriority.Normal;

    /// <summary>
    /// Gets or sets the current message status
    /// </summary>
    public NotificationStatus Status { get; set; } = NotificationStatus.Queued;

    /// <summary>
    /// Gets or sets when the message was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Gets or sets when the message was sent
    /// </summary>
    public DateTime? SentAt { get; set; }

    /// <summary>
    /// Gets or sets when the message was delivered
    /// </summary>
    public DateTime? DeliveredAt { get; set; }

    /// <summary>
    /// Gets or sets the error message if delivery failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Gets or sets the number of retry attempts made
    /// </summary>
    public int RetryCount { get; set; } = 0;

    /// <summary>
    /// Gets or sets the maximum number of retry attempts
    /// </summary>
    public int MaxRetries { get; set; } = 3;

    /// <summary>
    /// Gets or sets additional metadata for the message
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// Gets the duration from send to delivery in milliseconds
    /// </summary>
    public double? DurationMs
    {
        get
        {
            if (SentAt.HasValue && DeliveredAt.HasValue)
            {
                return (DeliveredAt.Value - SentAt.Value).TotalMilliseconds;
            }
            return null;
        }
    }

    /// <summary>
    /// Gets whether the message was delivered successfully
    /// </summary>
    public bool IsDelivered => Status == NotificationStatus.Delivered;

    /// <summary>
    /// Gets whether the message delivery failed
    /// </summary>
    public bool IsFailed => Status == NotificationStatus.Failed || 
                           Status == NotificationStatus.Timeout || 
                           Status == NotificationStatus.Rejected;

    /// <summary>
    /// Gets whether the message can be retried
    /// </summary>
    public bool CanRetry => IsFailed && RetryCount < MaxRetries;
}
