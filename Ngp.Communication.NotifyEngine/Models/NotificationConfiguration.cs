namespace Ngp.Communication.NotifyEngine.Models;

/// <summary>
/// Base configuration for notification providers
/// </summary>
public abstract class NotificationConfiguration
{
    /// <summary>
    /// Gets or sets whether this provider is enabled
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// Gets or sets the timeout for sending notifications in milliseconds
    /// </summary>
    public int TimeoutMs { get; set; } = 30000;

    /// <summary>
    /// Gets or sets the maximum number of retry attempts
    /// </summary>
    public int MaxRetries { get; set; } = 3;

    /// <summary>
    /// Gets or sets the delay between retry attempts in milliseconds
    /// </summary>
    public int RetryDelayMs { get; set; } = 1000;

    /// <summary>
    /// Gets or sets the maximum number of concurrent operations
    /// </summary>
    public int MaxConcurrency { get; set; } = 10;
}

/// <summary>
/// Configuration for email notifications
/// </summary>
public class EmailConfiguration : NotificationConfiguration
{
    /// <summary>
    /// Gets or sets the SMTP server host
    /// </summary>
    public string SmtpHost { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the SMTP server port
    /// </summary>
    public ushort SmtpPort { get; set; } = 587;

    /// <summary>
    /// Gets or sets whether to use SSL/TLS
    /// </summary>
    public bool UseSsl { get; set; } = true;

    /// <summary>
    /// Gets or sets the username for authentication
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the password for authentication
    /// </summary>
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the sender email address
    /// </summary>
    public string FromEmail { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the sender display name
    /// </summary>
    public string FromName { get; set; } = string.Empty;
}

/// <summary>
/// Configuration for SMS notifications
/// </summary>
public class SmsConfiguration : NotificationConfiguration
{
    /// <summary>
    /// Gets or sets the SMS service API URL
    /// </summary>
    public string ApiUrl { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the API username
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the API password
    /// </summary>
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the sender ID
    /// </summary>
    public string SenderId { get; set; } = string.Empty;
}

/// <summary>
/// Configuration for LINE notifications
/// </summary>
public class LineConfiguration : NotificationConfiguration
{
    /// <summary>
    /// Gets or sets the LINE channel access token
    /// </summary>
    public string ChannelAccessToken { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the LINE API URL
    /// </summary>
    public string ApiUrl { get; set; } = "https://api.line.me/v2/bot/message/push";
}

/// <summary>
/// Configuration for SIP message notifications
/// </summary>
public class SipMessageConfiguration : NotificationConfiguration
{
    /// <summary>
    /// Gets or sets the content type for SIP messages
    /// </summary>
    public string ContentType { get; set; } = "text/plain";
}
