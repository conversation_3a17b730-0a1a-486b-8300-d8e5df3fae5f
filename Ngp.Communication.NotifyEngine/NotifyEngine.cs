using System.Collections.Concurrent;
using Microsoft.Extensions.Logging;
using Ngp.Communication.NotifyEngine.Enums;
using Ngp.Communication.NotifyEngine.Events;
using Ngp.Communication.NotifyEngine.Interfaces;
using Ngp.Communication.NotifyEngine.Models;

namespace Ngp.Communication.NotifyEngine;

/// <summary>
/// Main implementation of the notification engine
/// </summary>
public class NotifyEngine : INotifyEngine
{
    private readonly ILogger<NotifyEngine>? _logger;
    private readonly Dictionary<NotificationChannel, INotificationProvider> _providers;
    private readonly ConcurrentDictionary<string, NotificationMessage> _messages;
    private readonly SemaphoreSlim _operationSemaphore;
    private readonly CancellationTokenSource _cancellationTokenSource;
    
    private Task? _retryTask;
    private bool _disposed = false;
    private bool _isRunning = false;

    /// <inheritdoc />
    public event EventHandler<NotificationStatusChangedEventArgs>? NotificationStatusChanged;

    /// <inheritdoc />
    public event EventHandler<NotificationErrorEventArgs>? NotificationError;

    /// <inheritdoc />
    public event EventHandler<NotificationProviderStateChangedEventArgs>? ProviderStateChanged;

    /// <inheritdoc />
    public bool IsRunning => _isRunning;

    /// <inheritdoc />
    public IReadOnlyList<NotificationChannel> AvailableChannels => 
        _providers.Where(p => p.Value.IsAvailable).Select(p => p.Key).ToList();

    /// <summary>
    /// Initializes a new instance of the NotifyEngine class
    /// </summary>
    /// <param name="providers">Dictionary of notification providers</param>
    /// <param name="logger">Optional logger</param>
    public NotifyEngine(
        Dictionary<NotificationChannel, INotificationProvider> providers,
        ILogger<NotifyEngine>? logger = null)
    {
        _providers = providers ?? throw new ArgumentNullException(nameof(providers));
        _logger = logger;
        _messages = new ConcurrentDictionary<string, NotificationMessage>();
        _operationSemaphore = new SemaphoreSlim(1, 1);
        _cancellationTokenSource = new CancellationTokenSource();

        // Subscribe to provider events
        foreach (var provider in _providers.Values)
        {
            provider.StateChanged += OnProviderStateChanged;
            provider.NotificationStatusChanged += OnProviderNotificationStatusChanged;
            provider.NotificationError += OnProviderNotificationError;
        }
    }

    /// <inheritdoc />
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        await _operationSemaphore.WaitAsync(cancellationToken);
        
        try
        {
            if (_isRunning)
            {
                _logger?.LogWarning("NotifyEngine is already running");
                return;
            }

            _logger?.LogInformation("Starting NotifyEngine with {ProviderCount} providers", _providers.Count);

            // Start retry processing task
            _retryTask = ProcessRetryQueueAsync(_cancellationTokenSource.Token);

            _isRunning = true;
            _logger?.LogInformation("NotifyEngine started successfully");
        }
        finally
        {
            _operationSemaphore.Release();
        }
    }

    /// <inheritdoc />
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        await _operationSemaphore.WaitAsync(cancellationToken);
        
        try
        {
            if (!_isRunning)
            {
                _logger?.LogWarning("NotifyEngine is not running");
                return;
            }

            _logger?.LogInformation("Stopping NotifyEngine");

            // Cancel background tasks
            _cancellationTokenSource.Cancel();

            // Wait for retry task to complete
            if (_retryTask != null)
            {
                try
                {
                    await _retryTask;
                }
                catch (OperationCanceledException)
                {
                    // Expected when cancellation is requested
                }
            }

            _isRunning = false;
            _logger?.LogInformation("NotifyEngine stopped successfully");
        }
        finally
        {
            _operationSemaphore.Release();
        }
    }

    /// <inheritdoc />
    public async Task<IReadOnlyList<string>> SendNotificationAsync(
        string content,
        Dictionary<NotificationChannel, List<string>> recipients,
        string? subject = null,
        NotificationPriority priority = NotificationPriority.Normal,
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(content))
        {
            throw new ArgumentException("Content cannot be null or empty", nameof(content));
        }

        if (recipients == null || recipients.Count == 0)
        {
            throw new ArgumentException("Recipients cannot be null or empty", nameof(recipients));
        }

        var messageIds = new List<string>();
        var sendTasks = new List<Task>();

        foreach (var channelRecipients in recipients)
        {
            var channel = channelRecipients.Key;
            var channelRecipientList = channelRecipients.Value;

            if (channelRecipientList.Count == 0) continue;

            var task = SendNotificationAsync(channel, content, channelRecipientList, subject, priority, cancellationToken)
                .ContinueWith(t =>
                {
                    if (t.IsCompletedSuccessfully)
                    {
                        lock (messageIds)
                        {
                            messageIds.Add(t.Result);
                        }
                    }
                }, cancellationToken);

            sendTasks.Add(task);
        }

        await Task.WhenAll(sendTasks);
        return messageIds.AsReadOnly();
    }

    /// <inheritdoc />
    public async Task<string> SendNotificationAsync(
        NotificationChannel channel,
        string content,
        IEnumerable<string> recipients,
        string? subject = null,
        NotificationPriority priority = NotificationPriority.Normal,
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(content))
        {
            throw new ArgumentException("Content cannot be null or empty", nameof(content));
        }

        var recipientList = recipients?.ToList() ?? throw new ArgumentNullException(nameof(recipients));
        if (recipientList.Count == 0)
        {
            throw new ArgumentException("Recipients cannot be empty", nameof(recipients));
        }

        if (!_providers.TryGetValue(channel, out var provider))
        {
            throw new ArgumentException($"No provider available for channel: {channel}", nameof(channel));
        }

        if (!provider.IsAvailable)
        {
            throw new InvalidOperationException($"Provider for channel {channel} is not available");
        }

        // Validate recipients
        if (!provider.ValidateRecipients(recipientList))
        {
            throw new ArgumentException($"Invalid recipients for channel {channel}", nameof(recipients));
        }

        // Create notification message
        var message = new NotificationMessage
        {
            Channel = channel,
            Content = content,
            Subject = subject,
            Recipients = recipientList,
            Priority = priority
        };

        // Store message for tracking
        _messages.TryAdd(message.MessageId, message);

        try
        {
            await provider.SendAsync(message, cancellationToken);
            return message.MessageId;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to send notification for message {MessageId}", message.MessageId);
            
            // Schedule retry if applicable
            if (message.CanRetry)
            {
                ScheduleRetry(message);
            }
            
            throw;
        }
    }

    /// <inheritdoc />
    public NotificationMessage? GetMessageStatus(string messageId)
    {
        return _messages.TryGetValue(messageId, out var message) ? message : null;
    }

    /// <inheritdoc />
    public IReadOnlyList<NotificationMessage> GetMessages(
        NotificationChannel? channel = null,
        NotificationStatus? status = null,
        DateTime? fromDate = null,
        DateTime? toDate = null)
    {
        var query = _messages.Values.AsEnumerable();

        if (channel.HasValue)
            query = query.Where(m => m.Channel == channel.Value);

        if (status.HasValue)
            query = query.Where(m => m.Status == status.Value);

        if (fromDate.HasValue)
            query = query.Where(m => m.CreatedAt >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(m => m.CreatedAt <= toDate.Value);

        return query.OrderByDescending(m => m.CreatedAt).ToList();
    }

    /// <inheritdoc />
    public NotificationStatistics GetStatistics()
    {
        var allMessages = _messages.Values.ToList();
        
        var stats = new NotificationStatistics
        {
            TotalMessages = allMessages.Count,
            DeliveredMessages = allMessages.Count(m => m.IsDelivered),
            FailedMessages = allMessages.Count(m => m.IsFailed),
            PendingMessages = allMessages.Count(m => m.Status == NotificationStatus.Queued || m.Status == NotificationStatus.Sending)
        };

        // Calculate channel statistics
        foreach (var channel in Enum.GetValues<NotificationChannel>())
        {
            var channelMessages = allMessages.Where(m => m.Channel == channel).ToList();
            var channelStats = new ChannelStatistics
            {
                Channel = channel,
                TotalMessages = channelMessages.Count,
                DeliveredMessages = channelMessages.Count(m => m.IsDelivered),
                FailedMessages = channelMessages.Count(m => m.IsFailed),
                PendingMessages = channelMessages.Count(m => m.Status == NotificationStatus.Queued || m.Status == NotificationStatus.Sending),
                IsAvailable = IsChannelAvailable(channel),
                AverageDeliveryTimeMs = channelMessages.Where(m => m.DurationMs.HasValue).Select(m => m.DurationMs!.Value).DefaultIfEmpty(0).Average()
            };

            stats.ChannelStatistics[channel] = channelStats;
        }

        return stats;
    }

    /// <inheritdoc />
    public bool IsChannelAvailable(NotificationChannel channel)
    {
        return _providers.TryGetValue(channel, out var provider) && provider.IsAvailable;
    }

    /// <summary>
    /// Schedules a message for retry
    /// </summary>
    /// <param name="message">Message to retry</param>
    private void ScheduleRetry(NotificationMessage message)
    {
        // Simple retry scheduling - in production, consider using a more sophisticated approach
        Task.Run(async () =>
        {
            try
            {
                await Task.Delay(TimeSpan.FromMilliseconds(1000 * Math.Pow(2, message.RetryCount)), _cancellationTokenSource.Token);
                
                if (_providers.TryGetValue(message.Channel, out var provider) && provider.IsAvailable)
                {
                    message.RetryCount++;
                    await provider.SendAsync(message, _cancellationTokenSource.Token);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Retry failed for message {MessageId}", message.MessageId);
            }
        });
    }

    /// <summary>
    /// Processes the retry queue in the background
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    private async Task ProcessRetryQueueAsync(CancellationToken cancellationToken)
    {
        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                // Clean up old messages (older than 24 hours)
                var cutoffTime = DateTime.UtcNow.AddHours(-24);
                var oldMessages = _messages.Where(kvp => kvp.Value.CreatedAt < cutoffTime).ToList();
                
                foreach (var oldMessage in oldMessages)
                {
                    _messages.TryRemove(oldMessage.Key, out _);
                }

                await Task.Delay(TimeSpan.FromMinutes(5), cancellationToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error in retry queue processing");
                await Task.Delay(TimeSpan.FromMinutes(1), cancellationToken);
            }
        }
    }

    /// <summary>
    /// Handles provider state changes
    /// </summary>
    /// <param name="sender">Event sender</param>
    /// <param name="e">Event arguments</param>
    private void OnProviderStateChanged(object? sender, NotificationProviderStateChangedEventArgs e)
    {
        ProviderStateChanged?.Invoke(this, e);
    }

    /// <summary>
    /// Handles provider notification status changes
    /// </summary>
    /// <param name="sender">Event sender</param>
    /// <param name="e">Event arguments</param>
    private void OnProviderNotificationStatusChanged(object? sender, NotificationStatusChangedEventArgs e)
    {
        NotificationStatusChanged?.Invoke(this, e);
    }

    /// <summary>
    /// Handles provider notification errors
    /// </summary>
    /// <param name="sender">Event sender</param>
    /// <param name="e">Event arguments</param>
    private void OnProviderNotificationError(object? sender, NotificationErrorEventArgs e)
    {
        NotificationError?.Invoke(this, e);
    }

    /// <inheritdoc />
    public void Dispose()
    {
        if (_disposed) return;

        _cancellationTokenSource.Cancel();
        
        // Dispose providers
        foreach (var provider in _providers.Values)
        {
            provider.StateChanged -= OnProviderStateChanged;
            provider.NotificationStatusChanged -= OnProviderNotificationStatusChanged;
            provider.NotificationError -= OnProviderNotificationError;
            provider.Dispose();
        }

        _operationSemaphore?.Dispose();
        _cancellationTokenSource?.Dispose();
        _disposed = true;
    }
}
