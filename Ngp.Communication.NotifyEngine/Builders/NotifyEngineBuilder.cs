using Microsoft.Extensions.Logging;
using Ngp.Communication.NotifyEngine.Enums;
using Ngp.Communication.NotifyEngine.Interfaces;
using Ngp.Communication.NotifyEngine.Models;
using Ngp.Communication.NotifyEngine.Providers;
using Ngp.Shared.Interfaces;

namespace Ngp.Communication.NotifyEngine.Builders;

/// <summary>
/// Fluent API builder for configuring NotifyEngine
/// </summary>
public class NotifyEngineBuilder : INotifyEngineBuilder
{
    private readonly Dictionary<NotificationChannel, INotificationProvider> _providers;
    private readonly Dictionary<NotificationChannel, NotificationConfiguration> _configurations;
    private ILoggerFactory? _loggerFactory;
    private int _globalTimeoutMs = 30000;
    private int _globalMaxRetries = 3;
    private int _globalRetryDelayMs = 1000;
    private int _globalMaxConcurrency = 10;

    /// <summary>
    /// Initializes a new instance of the NotifyEngineBuilder class
    /// </summary>
    public NotifyEngineBuilder()
    {
        _providers = new Dictionary<NotificationChannel, INotificationProvider>();
        _configurations = new Dictionary<NotificationChannel, NotificationConfiguration>();
    }

    /// <inheritdoc />
    public INotifyEngineBuilder WithEmail(
        string smtpHost,
        ushort smtpPort,
        string username,
        string password,
        string fromEmail,
        string fromName,
        bool useSsl = true)
    {
        var configuration = new EmailConfiguration
        {
            SmtpHost = smtpHost,
            SmtpPort = smtpPort,
            Username = username,
            Password = password,
            FromEmail = fromEmail,
            FromName = fromName,
            UseSsl = useSsl
        };

        return WithEmail(configuration);
    }

    /// <inheritdoc />
    public INotifyEngineBuilder WithEmail(EmailConfiguration configuration)
    {
        if (configuration == null)
            throw new ArgumentNullException(nameof(configuration));

        ApplyGlobalSettings(configuration);
        _configurations[NotificationChannel.Email] = configuration;

        var logger = _loggerFactory?.CreateLogger<EmailNotificationProvider>();
        _providers[NotificationChannel.Email] = new EmailNotificationProvider(logger);

        return this;
    }

    /// <inheritdoc />
    public INotifyEngineBuilder WithSms(
        string apiUrl,
        string username,
        string password,
        string senderId)
    {
        var configuration = new SmsConfiguration
        {
            ApiUrl = apiUrl,
            Username = username,
            Password = password,
            SenderId = senderId
        };

        return WithSms(configuration);
    }

    /// <inheritdoc />
    public INotifyEngineBuilder WithSms(SmsConfiguration configuration)
    {
        if (configuration == null)
            throw new ArgumentNullException(nameof(configuration));

        ApplyGlobalSettings(configuration);
        _configurations[NotificationChannel.Sms] = configuration;

        var logger = _loggerFactory?.CreateLogger<SmsNotificationProvider>();
        _providers[NotificationChannel.Sms] = new SmsNotificationProvider(logger);

        return this;
    }

    /// <inheritdoc />
    public INotifyEngineBuilder WithLine(
        string channelAccessToken,
        string? apiUrl = null)
    {
        var configuration = new LineConfiguration
        {
            ChannelAccessToken = channelAccessToken,
            ApiUrl = apiUrl ?? "https://api.line.me/v2/bot/message/push"
        };

        return WithLine(configuration);
    }

    /// <inheritdoc />
    public INotifyEngineBuilder WithLine(LineConfiguration configuration)
    {
        if (configuration == null)
            throw new ArgumentNullException(nameof(configuration));

        ApplyGlobalSettings(configuration);
        _configurations[NotificationChannel.Line] = configuration;

        var logger = _loggerFactory?.CreateLogger<LineNotificationProvider>();
        _providers[NotificationChannel.Line] = new LineNotificationProvider(logger);

        return this;
    }

    /// <inheritdoc />
    public INotifyEngineBuilder WithSipMessage(
        ISipMessageService sipMessageService,
        string contentType = "text/plain")
    {
        var configuration = new SipMessageConfiguration
        {
            ContentType = contentType
        };

        return WithSipMessage(sipMessageService, configuration);
    }

    /// <inheritdoc />
    public INotifyEngineBuilder WithSipMessage(
        ISipMessageService sipMessageService,
        SipMessageConfiguration configuration)
    {
        if (sipMessageService == null)
            throw new ArgumentNullException(nameof(sipMessageService));
        
        if (configuration == null)
            throw new ArgumentNullException(nameof(configuration));

        ApplyGlobalSettings(configuration);
        _configurations[NotificationChannel.SipMessage] = configuration;

        var logger = _loggerFactory?.CreateLogger<SipMessageNotificationProvider>();
        _providers[NotificationChannel.SipMessage] = new SipMessageNotificationProvider(sipMessageService, logger);

        return this;
    }

    /// <inheritdoc />
    public INotifyEngineBuilder WithLogging(ILoggerFactory loggerFactory)
    {
        _loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
        return this;
    }

    /// <inheritdoc />
    public INotifyEngineBuilder WithTimeout(int timeoutMs)
    {
        if (timeoutMs <= 0)
            throw new ArgumentException("Timeout must be greater than zero", nameof(timeoutMs));

        _globalTimeoutMs = timeoutMs;
        
        // Apply to existing configurations
        foreach (var config in _configurations.Values)
        {
            config.TimeoutMs = timeoutMs;
        }

        return this;
    }

    /// <inheritdoc />
    public INotifyEngineBuilder WithRetryPolicy(int maxRetries, int retryDelayMs)
    {
        if (maxRetries < 0)
            throw new ArgumentException("Max retries cannot be negative", nameof(maxRetries));
        
        if (retryDelayMs <= 0)
            throw new ArgumentException("Retry delay must be greater than zero", nameof(retryDelayMs));

        _globalMaxRetries = maxRetries;
        _globalRetryDelayMs = retryDelayMs;
        
        // Apply to existing configurations
        foreach (var config in _configurations.Values)
        {
            config.MaxRetries = maxRetries;
            config.RetryDelayMs = retryDelayMs;
        }

        return this;
    }

    /// <inheritdoc />
    public INotifyEngineBuilder WithConcurrency(int maxConcurrency)
    {
        if (maxConcurrency <= 0)
            throw new ArgumentException("Max concurrency must be greater than zero", nameof(maxConcurrency));

        _globalMaxConcurrency = maxConcurrency;
        
        // Apply to existing configurations
        foreach (var config in _configurations.Values)
        {
            config.MaxConcurrency = maxConcurrency;
        }

        return this;
    }

    /// <inheritdoc />
    public INotifyEngine Build()
    {
        if (_providers.Count == 0)
        {
            throw new InvalidOperationException("At least one notification provider must be configured");
        }

        // Initialize all providers
        var initializationTasks = _providers.Select(async kvp =>
        {
            var channel = kvp.Key;
            var provider = kvp.Value;
            
            if (_configurations.TryGetValue(channel, out var config))
            {
                await provider.InitializeAsync(config);
            }
        });

        // Wait for all providers to initialize
        Task.WaitAll(initializationTasks.ToArray());

        var logger = _loggerFactory?.CreateLogger<NotifyEngine>();
        return new NotifyEngine(_providers, logger);
    }

    /// <summary>
    /// Applies global settings to a configuration
    /// </summary>
    /// <param name="configuration">Configuration to update</param>
    private void ApplyGlobalSettings(NotificationConfiguration configuration)
    {
        configuration.TimeoutMs = _globalTimeoutMs;
        configuration.MaxRetries = _globalMaxRetries;
        configuration.RetryDelayMs = _globalRetryDelayMs;
        configuration.MaxConcurrency = _globalMaxConcurrency;
    }
}
