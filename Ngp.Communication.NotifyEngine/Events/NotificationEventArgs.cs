using Ngp.Communication.NotifyEngine.Enums;
using Ngp.Communication.NotifyEngine.Models;

namespace Ngp.Communication.NotifyEngine.Events;

/// <summary>
/// Event arguments for notification status changes
/// </summary>
public class NotificationStatusChangedEventArgs : EventArgs
{
    /// <summary>
    /// Gets the notification message
    /// </summary>
    public NotificationMessage Message { get; }

    /// <summary>
    /// Gets the previous status
    /// </summary>
    public NotificationStatus PreviousStatus { get; }

    /// <summary>
    /// Gets the current status
    /// </summary>
    public NotificationStatus CurrentStatus { get; }

    /// <summary>
    /// Gets the timestamp when the status change occurred
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// Initializes a new instance of the NotificationStatusChangedEventArgs class
    /// </summary>
    /// <param name="message">The notification message</param>
    /// <param name="previousStatus">Previous status</param>
    /// <param name="currentStatus">Current status</param>
    public NotificationStatusChangedEventArgs(
        NotificationMessage message,
        NotificationStatus previousStatus,
        NotificationStatus currentStatus)
    {
        Message = message ?? throw new ArgumentNullException(nameof(message));
        PreviousStatus = previousStatus;
        CurrentStatus = currentStatus;
        Timestamp = DateTime.UtcNow;
    }
}

/// <summary>
/// Event arguments for notification errors
/// </summary>
public class NotificationErrorEventArgs : EventArgs
{
    /// <summary>
    /// Gets the notification message that caused the error
    /// </summary>
    public NotificationMessage Message { get; }

    /// <summary>
    /// Gets the notification channel
    /// </summary>
    public NotificationChannel Channel { get; }

    /// <summary>
    /// Gets the error message
    /// </summary>
    public string ErrorMessage { get; }

    /// <summary>
    /// Gets the exception that occurred
    /// </summary>
    public Exception? Exception { get; }

    /// <summary>
    /// Gets the timestamp when the error occurred
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// Gets whether this error will trigger a retry
    /// </summary>
    public bool WillRetry { get; }

    /// <summary>
    /// Initializes a new instance of the NotificationErrorEventArgs class
    /// </summary>
    /// <param name="message">The notification message</param>
    /// <param name="channel">The notification channel</param>
    /// <param name="errorMessage">Error message</param>
    /// <param name="exception">Exception that occurred</param>
    /// <param name="willRetry">Whether this error will trigger a retry</param>
    public NotificationErrorEventArgs(
        NotificationMessage message,
        NotificationChannel channel,
        string errorMessage,
        Exception? exception = null,
        bool willRetry = false)
    {
        Message = message ?? throw new ArgumentNullException(nameof(message));
        Channel = channel;
        ErrorMessage = errorMessage ?? throw new ArgumentNullException(nameof(errorMessage));
        Exception = exception;
        WillRetry = willRetry;
        Timestamp = DateTime.UtcNow;
    }
}

/// <summary>
/// Event arguments for notification provider state changes
/// </summary>
public class NotificationProviderStateChangedEventArgs : EventArgs
{
    /// <summary>
    /// Gets the notification channel
    /// </summary>
    public NotificationChannel Channel { get; }

    /// <summary>
    /// Gets whether the provider is available
    /// </summary>
    public bool IsAvailable { get; }

    /// <summary>
    /// Gets the reason for the state change
    /// </summary>
    public string? Reason { get; }

    /// <summary>
    /// Gets the timestamp when the state change occurred
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// Initializes a new instance of the NotificationProviderStateChangedEventArgs class
    /// </summary>
    /// <param name="channel">The notification channel</param>
    /// <param name="isAvailable">Whether the provider is available</param>
    /// <param name="reason">Reason for the state change</param>
    public NotificationProviderStateChangedEventArgs(
        NotificationChannel channel,
        bool isAvailable,
        string? reason = null)
    {
        Channel = channel;
        IsAvailable = isAvailable;
        Reason = reason;
        Timestamp = DateTime.UtcNow;
    }
}
