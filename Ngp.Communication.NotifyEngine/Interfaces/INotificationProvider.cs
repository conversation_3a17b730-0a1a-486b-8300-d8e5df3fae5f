using Ngp.Communication.NotifyEngine.Enums;
using Ngp.Communication.NotifyEngine.Events;
using Ngp.Communication.NotifyEngine.Models;

namespace Ngp.Communication.NotifyEngine.Interfaces;

/// <summary>
/// Interface for notification providers
/// </summary>
public interface INotificationProvider : IDisposable
{
    /// <summary>
    /// Gets the notification channel this provider handles
    /// </summary>
    NotificationChannel Channel { get; }

    /// <summary>
    /// Gets whether the provider is available
    /// </summary>
    bool IsAvailable { get; }

    /// <summary>
    /// Event raised when provider state changes
    /// </summary>
    event EventHandler<NotificationProviderStateChangedEventArgs>? StateChanged;

    /// <summary>
    /// Event raised when notification status changes
    /// </summary>
    event EventHandler<NotificationStatusChangedEventArgs>? NotificationStatusChanged;

    /// <summary>
    /// Event raised when notification error occurs
    /// </summary>
    event EventHandler<NotificationErrorEventArgs>? NotificationError;

    /// <summary>
    /// Sends a notification message
    /// </summary>
    /// <param name="message">The notification message to send</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task SendAsync(NotificationMessage message, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates whether the provider can send to the specified recipients
    /// </summary>
    /// <param name="recipients">List of recipients</param>
    /// <returns>True if all recipients are valid, false otherwise</returns>
    bool ValidateRecipients(IEnumerable<string> recipients);

    /// <summary>
    /// Gets the maximum number of recipients supported in a single message
    /// </summary>
    int MaxRecipientsPerMessage { get; }

    /// <summary>
    /// Initializes the provider with configuration
    /// </summary>
    /// <param name="configuration">Provider configuration</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task InitializeAsync(NotificationConfiguration configuration, CancellationToken cancellationToken = default);
}
