using Ngp.Communication.NotifyEngine.Enums;
using Ngp.Communication.NotifyEngine.Events;
using Ngp.Communication.NotifyEngine.Models;

namespace Ngp.Communication.NotifyEngine.Interfaces;

/// <summary>
/// Interface for the notification engine
/// </summary>
public interface INotifyEngine : IDisposable
{
    /// <summary>
    /// Event raised when notification status changes
    /// </summary>
    event EventHandler<NotificationStatusChangedEventArgs>? NotificationStatusChanged;

    /// <summary>
    /// Event raised when notification error occurs
    /// </summary>
    event EventHandler<NotificationErrorEventArgs>? NotificationError;

    /// <summary>
    /// Event raised when provider state changes
    /// </summary>
    event EventHandler<NotificationProviderStateChangedEventArgs>? ProviderStateChanged;

    /// <summary>
    /// Gets whether the engine is running
    /// </summary>
    bool IsRunning { get; }

    /// <summary>
    /// Gets the list of available notification channels
    /// </summary>
    IReadOnlyList<NotificationChannel> AvailableChannels { get; }

    /// <summary>
    /// Starts the notification engine
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task StartAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Stops the notification engine
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task StopAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Sends a notification to multiple channels
    /// </summary>
    /// <param name="content">Message content</param>
    /// <param name="recipients">Recipients for each channel</param>
    /// <param name="subject">Message subject (for email)</param>
    /// <param name="priority">Message priority</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task with list of message IDs</returns>
    Task<IReadOnlyList<string>> SendNotificationAsync(
        string content,
        Dictionary<NotificationChannel, List<string>> recipients,
        string? subject = null,
        NotificationPriority priority = NotificationPriority.Normal,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Sends a notification to a single channel
    /// </summary>
    /// <param name="channel">Notification channel</param>
    /// <param name="content">Message content</param>
    /// <param name="recipients">List of recipients</param>
    /// <param name="subject">Message subject (for email)</param>
    /// <param name="priority">Message priority</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task with message ID</returns>
    Task<string> SendNotificationAsync(
        NotificationChannel channel,
        string content,
        IEnumerable<string> recipients,
        string? subject = null,
        NotificationPriority priority = NotificationPriority.Normal,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the status of a specific message
    /// </summary>
    /// <param name="messageId">Message ID</param>
    /// <returns>Message status or null if not found</returns>
    NotificationMessage? GetMessageStatus(string messageId);

    /// <summary>
    /// Gets all messages with optional filtering
    /// </summary>
    /// <param name="channel">Optional channel filter</param>
    /// <param name="status">Optional status filter</param>
    /// <param name="fromDate">Optional from date filter</param>
    /// <param name="toDate">Optional to date filter</param>
    /// <returns>List of messages</returns>
    IReadOnlyList<NotificationMessage> GetMessages(
        NotificationChannel? channel = null,
        NotificationStatus? status = null,
        DateTime? fromDate = null,
        DateTime? toDate = null);

    /// <summary>
    /// Gets notification statistics
    /// </summary>
    /// <returns>Notification statistics</returns>
    NotificationStatistics GetStatistics();

    /// <summary>
    /// Checks if a specific channel is available
    /// </summary>
    /// <param name="channel">Notification channel</param>
    /// <returns>True if available, false otherwise</returns>
    bool IsChannelAvailable(NotificationChannel channel);
}
