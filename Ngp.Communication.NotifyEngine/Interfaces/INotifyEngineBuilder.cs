using Microsoft.Extensions.Logging;
using Ngp.Communication.NotifyEngine.Models;
using Ngp.Shared.Interfaces;

namespace Ngp.Communication.NotifyEngine.Interfaces;

/// <summary>
/// Interface for building NotifyEngine instances using Fluent API
/// </summary>
public interface INotifyEngineBuilder
{
    /// <summary>
    /// Configures email notifications
    /// </summary>
    /// <param name="smtpHost">SMTP server host</param>
    /// <param name="smtpPort">SMTP server port</param>
    /// <param name="username">Username for authentication</param>
    /// <param name="password">Password for authentication</param>
    /// <param name="fromEmail">Sender email address</param>
    /// <param name="fromName">Sender display name</param>
    /// <param name="useSsl">Whether to use SSL/TLS</param>
    /// <returns>Builder instance for method chaining</returns>
    INotifyEngineBuilder WithEmail(
        string smtpHost,
        ushort smtpPort,
        string username,
        string password,
        string fromEmail,
        string fromName,
        bool useSsl = true);

    /// <summary>
    /// Configures email notifications with detailed configuration
    /// </summary>
    /// <param name="configuration">Email configuration</param>
    /// <returns>Builder instance for method chaining</returns>
    INotifyEngineBuilder WithEmail(EmailConfiguration configuration);

    /// <summary>
    /// Configures SMS notifications
    /// </summary>
    /// <param name="apiUrl">SMS service API URL</param>
    /// <param name="username">API username</param>
    /// <param name="password">API password</param>
    /// <param name="senderId">Sender ID</param>
    /// <returns>Builder instance for method chaining</returns>
    INotifyEngineBuilder WithSms(
        string apiUrl,
        string username,
        string password,
        string senderId);

    /// <summary>
    /// Configures SMS notifications with detailed configuration
    /// </summary>
    /// <param name="configuration">SMS configuration</param>
    /// <returns>Builder instance for method chaining</returns>
    INotifyEngineBuilder WithSms(SmsConfiguration configuration);

    /// <summary>
    /// Configures LINE notifications
    /// </summary>
    /// <param name="channelAccessToken">LINE channel access token</param>
    /// <param name="apiUrl">LINE API URL (optional)</param>
    /// <returns>Builder instance for method chaining</returns>
    INotifyEngineBuilder WithLine(
        string channelAccessToken,
        string? apiUrl = null);

    /// <summary>
    /// Configures LINE notifications with detailed configuration
    /// </summary>
    /// <param name="configuration">LINE configuration</param>
    /// <returns>Builder instance for method chaining</returns>
    INotifyEngineBuilder WithLine(LineConfiguration configuration);

    /// <summary>
    /// Configures SIP message notifications
    /// </summary>
    /// <param name="sipMessageService">SIP message service instance</param>
    /// <param name="contentType">Content type for SIP messages</param>
    /// <returns>Builder instance for method chaining</returns>
    INotifyEngineBuilder WithSipMessage(
        ISipMessageService sipMessageService,
        string contentType = "text/plain");

    /// <summary>
    /// Configures SIP message notifications with detailed configuration
    /// </summary>
    /// <param name="sipMessageService">SIP message service instance</param>
    /// <param name="configuration">SIP message configuration</param>
    /// <returns>Builder instance for method chaining</returns>
    INotifyEngineBuilder WithSipMessage(
        ISipMessageService sipMessageService,
        SipMessageConfiguration configuration);

    /// <summary>
    /// Sets the logger factory for the engine and providers
    /// </summary>
    /// <param name="loggerFactory">Logger factory</param>
    /// <returns>Builder instance for method chaining</returns>
    INotifyEngineBuilder WithLogging(ILoggerFactory loggerFactory);

    /// <summary>
    /// Sets global timeout for all providers
    /// </summary>
    /// <param name="timeoutMs">Timeout in milliseconds</param>
    /// <returns>Builder instance for method chaining</returns>
    INotifyEngineBuilder WithTimeout(int timeoutMs);

    /// <summary>
    /// Sets global retry configuration for all providers
    /// </summary>
    /// <param name="maxRetries">Maximum number of retries</param>
    /// <param name="retryDelayMs">Delay between retries in milliseconds</param>
    /// <returns>Builder instance for method chaining</returns>
    INotifyEngineBuilder WithRetryPolicy(int maxRetries, int retryDelayMs);

    /// <summary>
    /// Sets global concurrency limit for all providers
    /// </summary>
    /// <param name="maxConcurrency">Maximum number of concurrent operations</param>
    /// <returns>Builder instance for method chaining</returns>
    INotifyEngineBuilder WithConcurrency(int maxConcurrency);

    /// <summary>
    /// Builds the NotifyEngine instance
    /// </summary>
    /// <returns>Configured NotifyEngine instance</returns>
    INotifyEngine Build();
}
