# Ngp.Calculation.LogicEngine

A high-performance, flexible calculation engine for .NET applications that supports real-time formula evaluation, input management, and event-driven computation.

## Features

- **Real-time Calculation**: Automatic recalculation when input values change
- **Flexible Data Types**: Support for Boolean, Integer, Long, Float, Double, and String types
- **Event-Driven Architecture**: Events for input changes, output changes, and calculation errors
- **Fluent API**: Easy-to-use builder pattern for configuration
- **High Performance**: Optimized for concurrent operations with configurable threading
- **Caching Support**: Built-in result caching with configurable TTL
- **Input Validation**: Support for custom validators, min/max values, and required fields
- **Scale & Offset**: Built-in support for linear transformations
- **Lookup Tables**: Support for value mapping and transformation
- **Batch Operations**: Efficient batch updates for multiple inputs
- **Error Handling**: Configurable error handling strategies
- **Comprehensive Logging**: Integration with Microsoft.Extensions.Logging

## Quick Start

### Installation

Add the project reference to your application:

```xml
<ProjectReference Include="path/to/Ngp.Calculation.LogicEngine/Ngp.Calculation.LogicEngine.csproj" />
```

### Basic Usage

```csharp
using Ngp.Calculation.LogicEngine;

// Create a simple logic engine
var engine = LogicEngineFactory.CreateSimple();

// Register inputs with clear termination
engine.RegisterInput("Temperature")
    .WithInitialValue(25.0)
    .WithUnit("°C")
    .WithMinValue(-50.0)
    .WithMaxValue(100.0)
    .Complete();  // Clear end of Temperature configuration

engine.RegisterInput("Humidity")
    .WithInitialValue(60.0)
    .WithUnit("%")
    .WithMinValue(0.0)
    .WithMaxValue(100.0)
    .Complete();  // Clear end of Humidity configuration

// Define formulas with clear termination
engine.DefineFormula("ComfortIndex")
    .WithExpression("Temperature * 0.7 + Humidity * 0.3")
    .WithDescription("Comfort index calculation")
    .Complete();  // Clear end of formula configuration

// Subscribe to events
engine.OutputChanged += (sender, e) =>
{
    Console.WriteLine($"Formula '{e.FormulaId}' changed: {e.OldValue} → {e.NewValue}");
};

// Start the engine
engine.Start();

// Update inputs
engine.UpdateInput("Temperature", 30.0);
engine.UpdateInput("Humidity", 70.0);

// Floor() function examples for integer outputs
engine.DefineFormula("TemperatureFloored")
    .WithExpression("FLOOR(Temperature)")
    .WithDescription("Temperature as integer for display")
    .Complete();

engine.DefineFormula("ComfortIndexFloored")
    .WithExpression("FLOOR(Temperature * 0.7 + Humidity * 0.3)")
    .WithDescription("Comfort index as integer percentage")
    .Complete();

// Get results
var comfortIndex = engine.GetFormulaValue("ComfortIndex");
Console.WriteLine($"Comfort Index: {comfortIndex}");
```

### Advanced Usage

```csharp
// Create high-performance engine
var engine = LogicEngineFactory.CreateHighPerformance();

// Register input with advanced configuration
engine.RegisterInput("SensorValue")
    .WithInitialValue(0)
    .WithDataType(DataType.Double)
    .WithScale(0.01)        // Convert from raw units
    .WithOffset(-273.15)    // Convert to Celsius
    .WithValidator(value => Convert.ToDouble(value) > -300)
    .WithDebounceTime(100)  // Debounce rapid changes
    .WithTags("sensor", "temperature")
    .WithMetadata(new Dictionary<string, object>
    {
        ["Location"] = "Room A",
        ["Calibration"] = DateTime.UtcNow
    })
    .Complete();  // Clear end of SensorValue configuration

// Define complex formula with error handling
engine.DefineFormula("ProcessedValue")
    .WithExpression("SensorValue * 1.8 + 32")  // Convert to Fahrenheit
    .WithOutputType(DataType.Double)
    .WithPriority(1)
    .WithTimeout(5000)
    .WithRetryCount(3)
    .WithErrorHandling(ErrorHandlingStrategy.ReturnDefault)
    .WithDefaultValue(0.0)
    .WithCacheResult(true)
    .WithCacheTtl(60000)
    .Complete();  // Clear end of ProcessedValue configuration

// Floor() examples for practical applications
engine.DefineFormula("ProcessedValueFloored")
    .WithExpression("FLOOR(SensorValue * 1.8 + 32)")  // Integer Fahrenheit
    .WithDescription("Temperature in integer Fahrenheit for display")
    .Complete();

engine.DefineFormula("AlarmLevel")
    .WithExpression("FLOOR(IIF(SensorValue > 50, 3, IIF(SensorValue > 25, 2, 1)))")
    .WithDescription("Discrete alarm level (1=Normal, 2=Warning, 3=Critical)")
    .Complete();

engine.DefineFormula("PercentageBar")
    .WithExpression("FLOOR((SensorValue / 100) * 10)")
    .WithDescription("Progress bar representation (0-10 bars)")
    .Complete();

// Batch updates for efficiency
engine.BatchUpdate(batch =>
{
    batch.SetInput("Input1", 10.0);
    batch.SetInput("Input2", 20.0);
    batch.SetInput("Input3", 30.0);
});
```

## Factory Methods

The `LogicEngineFactory` provides several pre-configured engine types:

- `CreateSimple()`: Basic engine with default settings
- `CreateHighPerformance()`: Optimized for speed with increased concurrency
- `CreateMemoryEfficient()`: Optimized for low memory usage
- `Create(...)`: Custom configuration with all parameters

## Data Types

Supported data types:
- `Boolean`: true/false values
- `Integer`: 32-bit signed integers
- `Long`: 64-bit signed integers
- `Float`: Single-precision floating point
- `Double`: Double-precision floating point
- `String`: Text values

## Events

The engine provides several events for monitoring:

- `OutputChanged`: Fired when a formula result changes
- `InputChanged`: Fired when an input value changes
- `CalculationError`: Fired when a calculation fails
- `EngineStateChanged`: Fired when the engine state changes

## Error Handling

Configure how formulas handle errors:

- `ReturnDefault`: Return the configured default value
- `ReturnLastValue`: Return the last known good value
- `ThrowException`: Throw an exception (stops calculation)
- `ReturnNull`: Return null value

## Performance Features

- **Concurrent Execution**: Configurable thread pool for parallel calculations
- **Result Caching**: Cache formula results with configurable TTL
- **Debouncing**: Prevent excessive recalculations from rapid input changes
- **Batch Updates**: Update multiple inputs atomically
- **Priority Scheduling**: Execute high-priority formulas first

## Examples

See the included example projects:
- `SimpleTest`: Basic usage demonstration
- `AdvancedTest`: Advanced features and performance testing

## Architecture

The engine consists of several key components:

- **LogicEngine**: Main orchestrator and public API
- **Input**: Represents input variables with validation and transformation
- **Formula**: Represents calculation formulas with metadata
- **Calculator**: Executes formula calculations
- **Cache**: Manages result caching
- **Events**: Provides event notifications

## Thread Safety

The LogicEngine is designed to be thread-safe and supports concurrent operations:
- Multiple threads can update inputs simultaneously
- Formula calculations can run in parallel
- Event handlers are called asynchronously

## Best Practices

1. **Use Batch Updates**: For multiple related changes, use `BatchUpdate()` for better performance
2. **Configure Debouncing**: Set appropriate debounce times for noisy inputs
3. **Monitor Events**: Subscribe to events for real-time monitoring and debugging
4. **Validate Inputs**: Use validators to ensure data quality
5. **Handle Errors**: Configure appropriate error handling strategies
6. **Cache Results**: Enable caching for expensive calculations
7. **Set Priorities**: Use formula priorities for critical calculations

## License

This project is part of the Next Generation Platform (NGP) and follows the project's licensing terms.
