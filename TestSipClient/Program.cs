using Microsoft.Extensions.Logging;
using Ngp.Communication.SipMessageProxy;
using Ngp.Communication.SipMessageProxy.Enums;
using Ngp.Communication.SipMessageProxy.Interfaces;
using Ngp.Communication.SipMessageProxy.Models;

// Create logger
using var loggerFactory = LoggerFactory.Create(builder => 
    builder.AddConsole().SetMinimumLevel(LogLevel.Debug));
var logger = loggerFactory.CreateLogger<Program>();

logger.LogInformation("Starting SIP Message Client test");

try
{
    // Create SIP client using factory with enhanced configuration
    var client = SipMessageClientFactory
        .CreateBuilder()
        .WithServer("***********", 6088)
        .WithCredentials("880001", "880001", "***********")
        .WithTransport(SipTransport.Udp)
        .WithRegistrationExpiry(120) // 2 minutes expiry
        .WithRegistrationRefreshInterval(30) // Refresh every 30 seconds for NAT traversal
        .WithRetryPolicy(3, TimeSpan.FromSeconds(5))
        .WithConnectionSettings(TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(10))
        .WithLogger(logger)
        .Build();

    // Subscribe to events
    client.RegistrationStateChanged += (sender, e) =>
    {
        logger.LogInformation("Registration: {PreviousState} -> {CurrentState} (Error: {ErrorMessage})", 
            e.PreviousState, e.CurrentState, e.ErrorMessage ?? "None");
    };

    client.ConnectionStateChanged += (sender, e) =>
    {
        logger.LogInformation("Connection: {PreviousState} -> {CurrentState} (Error: {ErrorMessage})", 
            e.PreviousState, e.CurrentState, e.ErrorMessage ?? "None");
    };

    client.MessageStatusChanged += (sender, e) =>
    {
        logger.LogInformation("Message {MessageId}: {PreviousStatus} -> {CurrentStatus}", 
            e.Message.MessageId, e.PreviousStatus, e.CurrentStatus);
    };

    // Start the client
    logger.LogInformation("Starting SIP client...");
    await client.StartAsync();

    // Wait for registration to complete
    logger.LogInformation("Waiting for registration...");
    await WaitForRegistrationAsync(client, logger);

    if (client.RegistrationState == RegistrationState.Registered)
    {
        logger.LogInformation("Registration successful! Testing message sending...");
        
        // Send a test message
        var messageId = await client.SendMessageAsync("880002", "Hello from SIP client test!");
        logger.LogInformation("Test message sent with ID: {MessageId}", messageId);

        // Wait a bit to see the message status
        await Task.Delay(5000);
    }
    else
    {
        logger.LogWarning("Registration failed or timed out");
    }

    // Stop the client
    logger.LogInformation("Stopping SIP client...");
    await client.StopAsync();
}
catch (Exception ex)
{
    logger.LogError(ex, "Error during SIP client test");
}

logger.LogInformation("SIP Message Client test completed");

static async Task WaitForRegistrationAsync(ISipMessageClient client, ILogger logger)
{
    var timeout = TimeSpan.FromSeconds(30);
    var startTime = DateTime.UtcNow;

    while (DateTime.UtcNow - startTime < timeout && 
           client.RegistrationState != RegistrationState.Registered)
    {
        if (client.RegistrationState == RegistrationState.RegistrationFailed)
        {
            logger.LogWarning("Registration failed");
            return;
        }

        await Task.Delay(500);
    }

    if (client.RegistrationState == RegistrationState.Registered)
    {
        logger.LogInformation("Registration completed successfully");
    }
    else
    {
        logger.LogWarning("Registration did not complete within timeout");
    }
}
