﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.14.36127.28
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Ngp", "Ngp\Ngp.csproj", "{599CD24F-3772-5C08-2C37-A924BF0F9054}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Ngp.AppHost", "Ngp.AppHost\Ngp.AppHost.csproj", "{64880E8A-522D-4348-BC1E-9419F481D1BC}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Ngp.ServiceDefaults", "Ngp.ServiceDefaults\Ngp.ServiceDefaults.csproj", "{3A4875ED-8733-FE16-567D-5A3BA8EC050E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Ngp.Communication.TwentyFourDioPoller", "Ngp.Communication.TwentyFourDioPoller\Ngp.Communication.TwentyFourDioPoller.csproj", "{CBA6179D-3B4D-F286-9FE7-8FCAD4C32C56}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Ngp.Communication.SipMessageProxy.TestConsole", "Ngp.Communication.SipMessageProxy.TestConsole\Ngp.Communication.SipMessageProxy.TestConsole.csproj", "{0B670E22-E4EC-FE6F-FADE-279B4772B96D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Ngp.Communication.SipMessageProxy", "Ngp.Communication.SipMessageProxy\Ngp.Communication.SipMessageProxy.csproj", "{98B087AF-7756-61A2-BE98-C3113D934F77}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Ngp.Communication.ModbusTcpMaster", "Ngp.Communication.ModbusTcpMaster\Ngp.Communication.ModbusTcpMaster.csproj", "{F22D9900-8B82-2654-7E19-60129702E706}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Ngp.Communication.IpMonitor", "Ngp.Communication.IpMonitor\Ngp.Communication.IpMonitor.csproj", "{D0AF90E1-7DFE-8890-59B3-5BF17FD9698B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Ngp.Communication.AsteriskProxy", "Ngp.Communication.AsteriskProxy\Ngp.Communication.AsteriskProxy.csproj", "{262C77F3-EB22-C296-085D-91AF2A99A9C4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Ngp.Calculation.LogicEngine", "Ngp.Calculation.LogicEngine\Ngp.Calculation.LogicEngine.csproj", "{31356BE4-CDE9-F2DD-6160-B62931C38E96}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Ngp.Communication.NotifyEngine", "Ngp.Communication.NotifyEngine\Ngp.Communication.NotifyEngine.csproj", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Ngp.Communication.NotifyEngine.TestConsole", "Ngp.Communication.NotifyEngine.TestConsole\Ngp.Communication.NotifyEngine.TestConsole.csproj", "{B2C3D4E5-F6G7-8901-BCDE-F23456789012}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{599CD24F-3772-5C08-2C37-A924BF0F9054}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{599CD24F-3772-5C08-2C37-A924BF0F9054}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{599CD24F-3772-5C08-2C37-A924BF0F9054}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{599CD24F-3772-5C08-2C37-A924BF0F9054}.Release|Any CPU.Build.0 = Release|Any CPU
		{64880E8A-522D-4348-BC1E-9419F481D1BC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{64880E8A-522D-4348-BC1E-9419F481D1BC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{64880E8A-522D-4348-BC1E-9419F481D1BC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{64880E8A-522D-4348-BC1E-9419F481D1BC}.Release|Any CPU.Build.0 = Release|Any CPU
		{3A4875ED-8733-FE16-567D-5A3BA8EC050E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3A4875ED-8733-FE16-567D-5A3BA8EC050E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3A4875ED-8733-FE16-567D-5A3BA8EC050E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3A4875ED-8733-FE16-567D-5A3BA8EC050E}.Release|Any CPU.Build.0 = Release|Any CPU
		{CBA6179D-3B4D-F286-9FE7-8FCAD4C32C56}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CBA6179D-3B4D-F286-9FE7-8FCAD4C32C56}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CBA6179D-3B4D-F286-9FE7-8FCAD4C32C56}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CBA6179D-3B4D-F286-9FE7-8FCAD4C32C56}.Release|Any CPU.Build.0 = Release|Any CPU
		{0B670E22-E4EC-FE6F-FADE-279B4772B96D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0B670E22-E4EC-FE6F-FADE-279B4772B96D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0B670E22-E4EC-FE6F-FADE-279B4772B96D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0B670E22-E4EC-FE6F-FADE-279B4772B96D}.Release|Any CPU.Build.0 = Release|Any CPU
		{98B087AF-7756-61A2-BE98-C3113D934F77}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{98B087AF-7756-61A2-BE98-C3113D934F77}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{98B087AF-7756-61A2-BE98-C3113D934F77}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{98B087AF-7756-61A2-BE98-C3113D934F77}.Release|Any CPU.Build.0 = Release|Any CPU
		{F22D9900-8B82-2654-7E19-60129702E706}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F22D9900-8B82-2654-7E19-60129702E706}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F22D9900-8B82-2654-7E19-60129702E706}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F22D9900-8B82-2654-7E19-60129702E706}.Release|Any CPU.Build.0 = Release|Any CPU
		{D0AF90E1-7DFE-8890-59B3-5BF17FD9698B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D0AF90E1-7DFE-8890-59B3-5BF17FD9698B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D0AF90E1-7DFE-8890-59B3-5BF17FD9698B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D0AF90E1-7DFE-8890-59B3-5BF17FD9698B}.Release|Any CPU.Build.0 = Release|Any CPU
		{262C77F3-EB22-C296-085D-91AF2A99A9C4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{262C77F3-EB22-C296-085D-91AF2A99A9C4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{262C77F3-EB22-C296-085D-91AF2A99A9C4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{262C77F3-EB22-C296-085D-91AF2A99A9C4}.Release|Any CPU.Build.0 = Release|Any CPU
		{31356BE4-CDE9-F2DD-6160-B62931C38E96}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{31356BE4-CDE9-F2DD-6160-B62931C38E96}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{31356BE4-CDE9-F2DD-6160-B62931C38E96}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{31356BE4-CDE9-F2DD-6160-B62931C38E96}.Release|Any CPU.Build.0 = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.Build.0 = Release|Any CPU
		{B2C3D4E5-F6G7-8901-BCDE-F23456789012}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B2C3D4E5-F6G7-8901-BCDE-F23456789012}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B2C3D4E5-F6G7-8901-BCDE-F23456789012}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B2C3D4E5-F6G7-8901-BCDE-F23456789012}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {3AD24F1F-38EC-49FA-8DE5-131EBDDE92E2}
	EndGlobalSection
EndGlobal
