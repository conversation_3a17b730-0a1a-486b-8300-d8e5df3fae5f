# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.


## MAIN REQUIREMENTS
- 你的回答及思考過程永遠都要使用正體中文，如果有什麼一般來說不會用中文表示的專有名詞，你就用英文即可
- 請一律寫英文註解，註解不要只寫一些我看命名就能看得懂的內容，如果是這樣不如不要寫
- 永遠選擇相對簡單直觀合理的Best Practice解決問題，不要複雜化問題
- 你的每一次修改，都要在深入了解Codebase的情況下才能開始，如果你不確定，請先了解了再修改
- 每次修改最後都必須要確認是否可以編譯成功，以及是否可以運行成功，如果不行，請修正到可以


## Architecture Overview

This is a **Next Generation Platform (NGP)** for Industrial IoT/SCADA applications built with .NET 9 and Microsoft .NET Aspire. The solution targets high-performance communication protocols supporting 1000+ concurrent connections with enterprise-grade stability.

### Project Structure

**Core Projects:**
- **Ngp** - Main ASP.NET Core Web API using Minimal APIs, Swagger UI at `/swagger`
- **Ngp.AppHost** - .NET Aspire orchestrator providing development dashboard and service management
- **Ngp.ServiceDefaults** - Shared library with OpenTelemetry, service discovery, HTTP resilience, and health checks

**Communication Libraries:**
- **Ngp.Communication.ModbusTcpMaster** - Custom ModbusTCP Master implementation (no third-party libs like NModbus)
- **Ngp.Communication.AsteriskProxy** - Asterisk PBX API client with extension monitoring
- **Ngp.Communication.IpMonitor** - ICMP-based IP monitoring with multiple detection modes
- **Ngp.Communication.TwentyFourDioPoller** - 24-channel Digital I/O poller
- **Ngp.Communication.SipMessageProxy** - SIP message proxy with full implementation and test console
- **Ngp.Communication.NotifyEngine** - Multi-provider notification system (Email/Gmail, SMS, SIP, LINE)
- **Ngp.Communication.SoyalProxy** - Soyal access control integration (placeholder project)

**Shared Libraries:**
- **Ngp.Shared** - Shared interfaces and models for cross-project communication

**Calculation Engine:**
- **Ngp.Calculation.LogicEngine** - High-performance formula evaluation engine with real-time calculation, caching, and event-driven architecture

## Common Development Commands

### Running the Application
```bash
# Run with Aspire orchestration (recommended for development)
cd Ngp.AppHost && dotnet run

# Run main API directly  
cd Ngp && dotnet run

# Build entire solution
dotnet build Ngp.sln
```

### Key Endpoints
- Aspire Dashboard: `https://localhost:17033` (service management, metrics, logs)
- API Swagger: Available at `/swagger` when running Ngp project
- Health Checks: `/health` (all checks) and `/alive` (liveness only)

### Testing & Development
```bash
# Run individual test consoles
cd Ngp.Communication.NotifyEngine.TestConsole && dotnet run
cd Ngp.Communication.SipMessageProxy.TestConsole && dotnet run

# Run SIP test clients
cd TestSipClient && dotnet run
cd TestEnhancedSipClient && dotnet run

# Container development
docker build -t ngp-api .
docker run -p 8080:8080 -p 8081:8081 ngp-api
```
- HTTP requests available in `Ngp/Ngp.http`
- Test documentation in `docs/` folders for each component
- Console test applications for individual component testing
- No automated test projects currently exist

## Development Patterns

### API Development
- **Minimal APIs only** - no Controllers (per project requirements)
- Endpoints organized in `Ngp/Endpoints/` folder (e.g., `ModbusEndpoints.cs`, `LogicEngineEndpoints.cs`)  
- Services in `Ngp/Services/` folder with corresponding endpoint registration
- All endpoint registration happens in `Ngp/Program.cs` via extension methods like `app.MapModbusEndpoints()`

### Service Integration Architecture
- All communication libraries follow consistent builder pattern with fluent APIs
- Event-driven architecture using `EventHandler<TEventArgs>` delegates
- Connection management with graceful shutdown and proper resource disposal
- Thread-safe operations using `SemaphoreSlim` over `lock` statements

### Aspire Integration
- Use `Ngp.ServiceDefaults` for consistent observability and resilience
- Services registered as singletons and hosted services in `Program.cs`
- Health checks automatically configured via service defaults

### Communication Library Requirements
All communication libraries must implement:
- Custom protocol engines (no third-party libraries)
- Thread-safe operations supporting 1000+ concurrent connections
- Fluent API builder pattern for configuration
- Event-driven architecture with comprehensive events
- Graceful shutdown with proper connection cleanup
- High-performance connection management
- `ILogger` usage with appropriate log levels (no `Console.WriteLine`)
- Precise numeric types (e.g., `ushort` not `int` where appropriate)

### LogicEngine Usage Patterns
- **One engine per logic point** (recommended) via `LogicPointManager`
- Multiple formulas per engine (legacy approach) via `LogicEngineServiceSimple`
- Factory methods: `CreateSimple()`, `CreateHighPerformance()`, `CreateMemoryEfficient()`
- Always call `.Complete()` to terminate fluent API chains clearly
- Use `FLOOR()` function for integer outputs in expressions

### NotifyEngine Usage Patterns
The NotifyEngine supports multiple notification providers:
- **Email/Gmail**: SMTP-based email notifications
- **SMS**: Taiwan SMS service integration
- **SIP Messages**: Integration with SipMessageProxy via dependency injection
- **LINE**: LINE Message API integration

Configuration via FluentAPI (not appsettings.json):
```csharp
notifyEngine.ConfigureEmail()
    .WithSmtpServer("smtp.gmail.com")
    .WithCredentials(username, password)
    .WithRecipients(recipients)
    .Complete();
```

### Enterprise Features
- OpenTelemetry pre-configured for metrics, tracing, and logging
- HTTP resilience with retry policies built-in
- User Secrets for sensitive configuration
- Container support via Dockerfile in Ngp project
- Service discovery and health monitoring

### Service Status & Activation
Most services are currently **commented out** in `Program.cs`. To activate services:

**Currently Active:**
- SipMessageProxyService
- NotifyEngineService

**Available but Disabled:**
- ModbusTcpService
- TwentyFourDioService  
- AsteriskProxyService
- IpMonitorService
- LogicEngineServiceSimple
- LogicPointManager

Uncomment the relevant service registration blocks in `Program.cs` to enable.

### Project Implementation Status
| Project | Status | Implementation Level |
|---------|--------|---------------------|
| ModbusTcpMaster | ✅ Complete | Full implementation |
| AsteriskProxy | ✅ Complete | Full implementation |
| IpMonitor | ✅ Complete | Full implementation |
| TwentyFourDioPoller | ✅ Complete | Full implementation |
| SipMessageProxy | ✅ Complete | Full implementation with tests |
| LogicEngine | ✅ Complete | Full implementation with docs |
| NotifyEngine | ✅ Complete | Multi-provider implementation |
| SoyalProxy | ⚠️ Placeholder | Empty project, needs implementation |

## Important Development Notes

- Start development by running `Ngp.AppHost` for full Aspire experience
- Focus on high-performance, enterprise-grade implementations for industrial environments
- All services should be observable and follow cloud-native patterns
- Most services are commented out in `Program.cs` - see Service Status section above