using Microsoft.Extensions.Logging;
using Ngp.Communication.SipMessageProxy;
using Ngp.Communication.SipMessageProxy.Enums;

// Create logger
using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Debug));
var logger = loggerFactory.CreateLogger<Program>();

logger.LogInformation("Testing Enhanced SIP Message Client with new features");

try
{
    // Create SIP client with enhanced configuration
    var client = SipMessageClientFactory
        .CreateBuilder()
        .WithServer("***********", 6088)
        .WithCredentials("880001", "880001", "***********")
        .WithTransport(SipTransport.Udp)
        .WithRegistrationExpiry(120) // 2 minutes registration expiry
        .WithRegistrationRefreshInterval(30) // Refresh every 30 seconds for NAT traversal
        .WithRetryPolicy(3, TimeSpan.FromSeconds(5)) // 3 retries with 5s delay for general operations
        .WithConnectionSettings(TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(10)) // 30s connection timeout, 10s retry interval
        .WithRequestTimeout(TimeSpan.FromSeconds(30))
        .WithAutoReregister(true)
        .WithLogger(logger)
        .Build();

    // Subscribe to events to monitor the new functionality
    client.RegistrationStateChanged += (sender, e) =>
    {
        logger.LogInformation("Registration: {PreviousState} -> {CurrentState} (Error: {ErrorMessage})", 
            e.PreviousState, e.CurrentState, e.ErrorMessage ?? "None");
    };

    client.ConnectionStateChanged += (sender, e) =>
    {
        logger.LogInformation("Connection: {PreviousState} -> {CurrentState} (Error: {ErrorMessage})", 
            e.PreviousState, e.CurrentState, e.ErrorMessage ?? "None");
    };

    client.MessageStatusChanged += (sender, e) =>
    {
        logger.LogInformation("Message {MessageId}: {PreviousStatus} -> {CurrentStatus}", 
            e.Message.MessageId, e.PreviousStatus, e.CurrentStatus);
    };

    logger.LogInformation("Starting SIP client with enhanced features...");
    logger.LogInformation("- Registration expiry: 120 seconds (2 minutes)");
    logger.LogInformation("- Registration refresh interval: 30 seconds (for NAT traversal)");
    logger.LogInformation("- Connection timeout: 30 seconds");
    logger.LogInformation("- Connection retry interval: 10 seconds");
    logger.LogInformation("- Max retries: 3 with 5 second delay");
    logger.LogInformation("- NAT traversal: Using short registration refresh intervals");

    // Start the client
    await client.StartAsync();

    logger.LogInformation("SIP client started successfully. Press any key to send a test message or 'q' to quit.");

    // Keep the client running and allow testing
    while (true)
    {
        var key = Console.ReadKey(true);
        if (key.KeyChar == 'q' || key.KeyChar == 'Q')
            break;

        try
        {
            // Send a test message
            var messageId = await client.SendMessageAsync("880002", $"Test message at {DateTime.Now:HH:mm:ss}");
            logger.LogInformation("Sent test message with ID: {MessageId}", messageId);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to send test message");
        }
    }

    logger.LogInformation("Stopping SIP client...");
    await client.StopAsync();
    logger.LogInformation("SIP client stopped successfully");
}
catch (Exception ex)
{
    logger.LogError(ex, "Error in SIP client test");
}

logger.LogInformation("Test completed. Press any key to exit.");
Console.ReadKey();
