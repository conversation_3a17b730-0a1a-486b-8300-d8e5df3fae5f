using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Ngp.Communication.SipMessageProxy.Enums;
using Ngp.Communication.SipMessageProxy.Interfaces;
using Ngp.Communication.SipMessageProxy.Models;

namespace Ngp.Communication.SipMessageProxy.Builders;

/// <summary>
/// Builder for creating SIP message client instances
/// </summary>
public class SipMessageClientBuilder : ISipMessageClientBuilder
{
    private readonly SipConfiguration _configuration;
    private ILogger _logger;

    /// <summary>
    /// Initializes a new instance of the SipMessageClientBuilder class
    /// </summary>
    public SipMessageClientBuilder()
    {
        _configuration = new SipConfiguration();
        _logger = NullLogger.Instance;
    }

    /// <summary>
    /// Creates a new builder instance
    /// </summary>
    /// <returns>New builder instance</returns>
    public static ISipMessageClientBuilder Create()
    {
        return new SipMessageClientBuilder();
    }

    /// <inheritdoc />
    public ISipMessageClientBuilder WithServer(string host, ushort port = 5060)
    {
        if (string.IsNullOrWhiteSpace(host))
            throw new ArgumentException("Host cannot be null or empty", nameof(host));
        if (port == 0)
            throw new ArgumentException("Port must be greater than 0", nameof(port));

        _configuration.ServerHost = host;
        _configuration.ServerPort = port;
        return this;
    }

    /// <inheritdoc />
    public ISipMessageClientBuilder WithCredentials(string username, string password, string domain)
    {
        if (string.IsNullOrWhiteSpace(username))
            throw new ArgumentException("Username cannot be null or empty", nameof(username));
        if (string.IsNullOrWhiteSpace(password))
            throw new ArgumentException("Password cannot be null or empty", nameof(password));
        if (string.IsNullOrWhiteSpace(domain))
            throw new ArgumentException("Domain cannot be null or empty", nameof(domain));

        _configuration.Username = username;
        _configuration.Password = password;
        _configuration.Domain = domain;
        return this;
    }

    /// <inheritdoc />
    public ISipMessageClientBuilder WithTransport(SipTransport transport)
    {
        _configuration.Transport = transport;
        return this;
    }

    /// <inheritdoc />
    public ISipMessageClientBuilder WithLocalPort(ushort port)
    {
        _configuration.LocalPort = port;
        return this;
    }

    /// <inheritdoc />
    public ISipMessageClientBuilder WithRegistrationExpiry(uint expiry)
    {
        if (expiry == 0)
            throw new ArgumentException("Expiry must be greater than 0", nameof(expiry));

        _configuration.RegistrationExpiry = expiry;
        return this;
    }

    /// <inheritdoc />
    public ISipMessageClientBuilder WithUserAgent(string userAgent)
    {
        if (string.IsNullOrWhiteSpace(userAgent))
            throw new ArgumentException("User agent cannot be null or empty", nameof(userAgent));

        _configuration.UserAgent = userAgent;
        return this;
    }

    /// <inheritdoc />
    public ISipMessageClientBuilder WithRetryPolicy(int maxRetries, TimeSpan retryDelay)
    {
        if (maxRetries < 0)
            throw new ArgumentException("Max retries cannot be negative", nameof(maxRetries));
        if (retryDelay < TimeSpan.Zero)
            throw new ArgumentException("Retry delay cannot be negative", nameof(retryDelay));

        _configuration.MaxRetries = maxRetries;
        _configuration.RetryDelay = retryDelay;
        return this;
    }

    /// <inheritdoc />
    public ISipMessageClientBuilder WithRetryPolicy(RetryPolicy retryPolicy)
    {
        if (retryPolicy == null)
            throw new ArgumentNullException(nameof(retryPolicy));

        _configuration.MaxRetries = retryPolicy.MaxRetries;
        _configuration.RetryDelay = retryPolicy.InitialDelay;
        return this;
    }

    /// <inheritdoc />
    public ISipMessageClientBuilder WithRequestTimeout(TimeSpan timeout)
    {
        if (timeout <= TimeSpan.Zero)
            throw new ArgumentException("Timeout must be greater than zero", nameof(timeout));

        _configuration.RequestTimeout = timeout;
        return this;
    }

    /// <inheritdoc />
    public ISipMessageClientBuilder WithAutoReregister(bool autoReregister)
    {
        _configuration.AutoReregister = autoReregister;
        return this;
    }

    /// <inheritdoc />
    public ISipMessageClientBuilder WithRegistrationRefreshInterval(uint refreshInterval)
    {
        _configuration.RegistrationRefreshInterval = refreshInterval;
        return this;
    }

    /// <inheritdoc />
    public ISipMessageClientBuilder WithConnectionSettings(TimeSpan connectionTimeout, TimeSpan retryInterval)
    {
        if (connectionTimeout <= TimeSpan.Zero)
            throw new ArgumentException("Connection timeout must be greater than zero", nameof(connectionTimeout));
        if (retryInterval <= TimeSpan.Zero)
            throw new ArgumentException("Retry interval must be greater than zero", nameof(retryInterval));

        _configuration.ConnectionTimeout = connectionTimeout;
        _configuration.ConnectionRetryInterval = retryInterval;
        return this;
    }

    /// <inheritdoc />
    public ISipMessageClientBuilder WithLogger(ILogger logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        return this;
    }

    /// <inheritdoc />
    public ISipMessageClient Build()
    {
        if (!_configuration.IsValid())
        {
            throw new InvalidOperationException("Configuration is not valid. Please ensure all required properties are set.");
        }

        return new SipMessageClient(_configuration, _logger);
    }
}
