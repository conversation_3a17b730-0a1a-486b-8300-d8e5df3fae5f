using Microsoft.Extensions.Logging;

namespace Ngp.Communication.SipMessageProxy.Utilities;

/// <summary>
/// Manages thread-safe operations using SemaphoreSlim to avoid deadlocks
/// </summary>
public class ThreadSafeOperationManager : IDisposable
{
    private readonly SemaphoreSlim _semaphore;
    private readonly ILogger _logger;
    private readonly string _operationName;
    private readonly TimeSpan _defaultTimeout;
    private bool _disposed;

    /// <summary>
    /// Initializes a new instance of the ThreadSafeOperationManager class
    /// </summary>
    /// <param name="operationName">Name of the operation for logging</param>
    /// <param name="maxConcurrency">Maximum number of concurrent operations</param>
    /// <param name="defaultTimeout">Default timeout for operations</param>
    /// <param name="logger">Logger instance</param>
    public ThreadSafeOperationManager(
        string operationName,
        int maxConcurrency = 1,
        TimeSpan defaultTimeout = default,
        ILogger? logger = null)
    {
        _operationName = operationName ?? throw new ArgumentNullException(nameof(operationName));
        _semaphore = new SemaphoreSlim(maxConcurrency, maxConcurrency);
        _logger = logger ?? Microsoft.Extensions.Logging.Abstractions.NullLogger.Instance;
        _defaultTimeout = defaultTimeout == default ? TimeSpan.FromSeconds(30) : defaultTimeout;
    }

    /// <summary>
    /// Executes an async operation in a thread-safe manner
    /// </summary>
    /// <param name="operation">Operation to execute</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <param name="timeout">Optional timeout override</param>
    /// <returns>Task representing the async operation</returns>
    public async Task ExecuteAsync(
        Func<CancellationToken, Task> operation,
        CancellationToken cancellationToken = default,
        TimeSpan? timeout = null)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(ThreadSafeOperationManager));

        if (operation == null)
            throw new ArgumentNullException(nameof(operation));

        var effectiveTimeout = timeout ?? _defaultTimeout;
        using var timeoutCts = new CancellationTokenSource(effectiveTimeout);
        using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token);

        var acquired = false;
        try
        {
            _logger.LogDebug("Acquiring semaphore for operation: {OperationName}", _operationName);
            
            await _semaphore.WaitAsync(combinedCts.Token);
            acquired = true;

            if (!acquired)
            {
                throw new TimeoutException($"Failed to acquire semaphore for operation '{_operationName}' within {effectiveTimeout}");
            }

            _logger.LogDebug("Semaphore acquired for operation: {OperationName}", _operationName);

            await operation(combinedCts.Token);

            _logger.LogDebug("Operation completed successfully: {OperationName}", _operationName);
        }
        catch (OperationCanceledException) when (timeoutCts.Token.IsCancellationRequested)
        {
            _logger.LogWarning("Operation timed out after {Timeout}: {OperationName}", effectiveTimeout, _operationName);
            throw new TimeoutException($"Operation '{_operationName}' timed out after {effectiveTimeout}");
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Operation cancelled: {OperationName}", _operationName);
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Operation failed: {OperationName}", _operationName);
            throw;
        }
        finally
        {
            if (acquired)
            {
                _semaphore.Release();
                _logger.LogDebug("Semaphore released for operation: {OperationName}", _operationName);
            }
        }
    }

    /// <summary>
    /// Executes an async operation with a return value in a thread-safe manner
    /// </summary>
    /// <typeparam name="T">Return type</typeparam>
    /// <param name="operation">Operation to execute</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <param name="timeout">Optional timeout override</param>
    /// <returns>Task with the operation result</returns>
    public async Task<T> ExecuteAsync<T>(
        Func<CancellationToken, Task<T>> operation,
        CancellationToken cancellationToken = default,
        TimeSpan? timeout = null)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(ThreadSafeOperationManager));

        if (operation == null)
            throw new ArgumentNullException(nameof(operation));

        var result = default(T);
        await ExecuteAsync(async ct =>
        {
            result = await operation(ct);
        }, cancellationToken, timeout);

        return result!;
    }

    /// <summary>
    /// Executes a synchronous operation in a thread-safe manner
    /// </summary>
    /// <param name="operation">Operation to execute</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <param name="timeout">Optional timeout override</param>
    /// <returns>Task representing the async operation</returns>
    public async Task ExecuteAsync(
        Action operation,
        CancellationToken cancellationToken = default,
        TimeSpan? timeout = null)
    {
        if (operation == null)
            throw new ArgumentNullException(nameof(operation));

        await ExecuteAsync(_ =>
        {
            operation();
            return Task.CompletedTask;
        }, cancellationToken, timeout);
    }

    /// <summary>
    /// Executes a synchronous operation with a return value in a thread-safe manner
    /// </summary>
    /// <typeparam name="T">Return type</typeparam>
    /// <param name="operation">Operation to execute</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <param name="timeout">Optional timeout override</param>
    /// <returns>Task with the operation result</returns>
    public async Task<T> ExecuteAsync<T>(
        Func<T> operation,
        CancellationToken cancellationToken = default,
        TimeSpan? timeout = null)
    {
        if (operation == null)
            throw new ArgumentNullException(nameof(operation));

        return await ExecuteAsync(_ => Task.FromResult(operation()), cancellationToken, timeout);
    }

    /// <summary>
    /// Gets the current count of available semaphore slots
    /// </summary>
    public int AvailableCount => _semaphore.CurrentCount;

    /// <summary>
    /// Disposes the operation manager
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;

        try
        {
            // Wait for all operations to complete with a reasonable timeout
            var timeout = TimeSpan.FromSeconds(10);
            var completed = _semaphore.Wait(timeout);
            
            if (!completed)
            {
                _logger.LogWarning("Some operations did not complete within {Timeout} during disposal of {OperationName}", 
                    timeout, _operationName);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during disposal of {OperationName}", _operationName);
        }
        finally
        {
            _semaphore.Dispose();
        }
    }
}
