using Microsoft.Extensions.Logging;
using Ngp.Communication.SipMessageProxy.Enums;
using Ngp.Communication.SipMessageProxy.Events;
using Ngp.Communication.SipMessageProxy.Interfaces;

namespace Ngp.Communication.SipMessageProxy.Examples;

/// <summary>
/// Basic usage example for SIP message client
/// </summary>
public class BasicUsageExample
{
    private readonly ILogger<BasicUsageExample> _logger;

    public BasicUsageExample(ILogger<BasicUsageExample> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Demonstrates basic SIP message client usage
    /// </summary>
    public async Task RunBasicExampleAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting SIP Message Client basic example");

        // Create SIP client using Fluent API with enhanced configuration
        var client = SipMessageClientFactory
            .CreateBuilder()
            .WithServer("***********", 5060)
            .WithCredentials("1001", "password", "asterisk.local")
            .WithTransport(SipTransport.Udp)
            .WithRegistrationExpiry(120) // 2 minutes expiry
            .WithRegistrationRefreshInterval(30) // Refresh every 30 seconds for NAT traversal
            .WithRetryPolicy(3, TimeSpan.FromSeconds(5))
            .WithConnectionSettings(TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(10)) // 30s timeout, 10s retry
            .WithRequestTimeout(TimeSpan.FromSeconds(30))
            .WithAutoReregister(true)
            .WithLogger(_logger)
            .Build();

        // Subscribe to events
        client.RegistrationStateChanged += OnRegistrationStateChanged;
        client.MessageStatusChanged += OnMessageStatusChanged;
        client.ConnectionStateChanged += OnConnectionStateChanged;

        try
        {
            // Start the client
            _logger.LogInformation("Starting SIP client...");
            await client.StartAsync(cancellationToken);

            // Wait for registration to complete
            await WaitForRegistrationAsync(client, cancellationToken);

            // Send some test messages
            await SendTestMessagesAsync(client, cancellationToken);

            // Monitor for a while
            _logger.LogInformation("Monitoring for 30 seconds...");
            await Task.Delay(30000, cancellationToken);

            // Display statistics
            DisplayStatistics(client);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Example cancelled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in basic example");
        }
        finally
        {
            // Clean shutdown
            _logger.LogInformation("Stopping SIP client...");
            await client.StopAsync(cancellationToken);
            client.Dispose();
        }

        _logger.LogInformation("SIP Message Client basic example completed");
    }

    /// <summary>
    /// Waits for SIP registration to complete
    /// </summary>
    private async Task WaitForRegistrationAsync(ISipMessageClient client, CancellationToken cancellationToken)
    {
        var timeout = TimeSpan.FromSeconds(30);
        var startTime = DateTime.UtcNow;

        while (DateTime.UtcNow - startTime < timeout && 
               client.RegistrationState != RegistrationState.Registered)
        {
            if (client.RegistrationState == RegistrationState.RegistrationFailed)
            {
                throw new InvalidOperationException("SIP registration failed");
            }

            await Task.Delay(500, cancellationToken);
        }

        if (client.RegistrationState != RegistrationState.Registered)
        {
            throw new TimeoutException("SIP registration timed out");
        }

        _logger.LogInformation("SIP registration completed successfully");
    }

    /// <summary>
    /// Sends test messages to demonstrate functionality
    /// </summary>
    private async Task SendTestMessagesAsync(ISipMessageClient client, CancellationToken cancellationToken)
    {
        var testMessages = new[]
        {
            new { Extension = "1002", Content = "Hello from SIP Message Client!" },
            new { Extension = "1003", Content = "Test message 2" },
            new { Extension = "1004", Content = "Test message 3" }
        };

        foreach (var msg in testMessages)
        {
            try
            {
                _logger.LogInformation("Sending message to {Extension}: {Content}", msg.Extension, msg.Content);
                
                var messageId = await client.SendMessageAsync(
                    msg.Extension, 
                    msg.Content, 
                    "text/plain", 
                    cancellationToken);

                _logger.LogInformation("Message queued with ID: {MessageId}", messageId);

                // Wait a bit between messages
                await Task.Delay(2000, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send message to {Extension}", msg.Extension);
            }
        }
    }

    /// <summary>
    /// Displays message statistics
    /// </summary>
    private void DisplayStatistics(ISipMessageClient client)
    {
        var stats = client.GetStatistics();
        
        _logger.LogInformation("=== Message Statistics ===");
        _logger.LogInformation("Total Sent: {TotalSent}", stats.TotalSent);
        _logger.LogInformation("Delivered: {Delivered}", stats.Delivered);
        _logger.LogInformation("Failed: {Failed}", stats.Failed);
        _logger.LogInformation("Pending: {Pending}", stats.Pending);
        _logger.LogInformation("Success Rate: {SuccessRate:F1}%", stats.SuccessRate);
        _logger.LogInformation("Average Delivery Time: {AvgTime:F1}ms", stats.AverageDeliveryTimeMs);

        // Display recent messages
        var recentMessages = client.GetMessages(fromDate: DateTime.UtcNow.AddMinutes(-5));
        _logger.LogInformation("=== Recent Messages ===");
        foreach (var message in recentMessages.Take(10))
        {
            _logger.LogInformation("To: {Extension}, Status: {Status}, Created: {Created}", 
                message.ToExtension, message.Status, message.CreatedAt);
        }
    }

    /// <summary>
    /// Handles registration state changes
    /// </summary>
    private void OnRegistrationStateChanged(object? sender, RegistrationStateChangedEventArgs e)
    {
        _logger.LogInformation("Registration state changed: {PreviousState} -> {CurrentState}", 
            e.PreviousState, e.CurrentState);

        if (!string.IsNullOrEmpty(e.ErrorMessage))
        {
            _logger.LogWarning("Registration error: {ErrorMessage}", e.ErrorMessage);
        }
    }

    /// <summary>
    /// Handles message status changes
    /// </summary>
    private void OnMessageStatusChanged(object? sender, MessageStatusChangedEventArgs e)
    {
        _logger.LogInformation("Message {MessageId} status changed: {PreviousStatus} -> {CurrentStatus}", 
            e.Message.MessageId, e.PreviousStatus, e.CurrentStatus);

        if (e.CurrentStatus == MessageStatus.Delivered)
        {
            _logger.LogInformation("Message delivered successfully to {Extension} in {Duration:F1}ms", 
                e.Message.ToExtension, e.Message.DurationMs ?? 0);
        }
        else if (e.Message.IsFailed)
        {
            _logger.LogWarning("Message delivery failed to {Extension}: {ErrorMessage}", 
                e.Message.ToExtension, e.Message.ErrorMessage);
        }
    }

    /// <summary>
    /// Handles connection state changes
    /// </summary>
    private void OnConnectionStateChanged(object? sender, ConnectionStateChangedEventArgs e)
    {
        _logger.LogInformation("Connection state changed: {PreviousState} -> {CurrentState}", 
            e.PreviousState, e.CurrentState);

        if (!string.IsNullOrEmpty(e.ErrorMessage))
        {
            _logger.LogError("Connection error: {ErrorMessage}", e.ErrorMessage);
        }
    }
}
