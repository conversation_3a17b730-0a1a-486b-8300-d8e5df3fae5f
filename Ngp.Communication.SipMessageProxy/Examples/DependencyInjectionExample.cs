using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Ngp.Communication.SipMessageProxy.Extensions;
using Ngp.Communication.SipMessageProxy.Interfaces;

namespace Ngp.Communication.SipMessageProxy.Examples;

/// <summary>
/// Example demonstrating dependency injection usage
/// </summary>
public class DependencyInjectionExample
{
    /// <summary>
    /// Demonstrates how to configure SIP message client with dependency injection
    /// </summary>
    public static async Task RunDependencyInjectionExampleAsync()
    {
        // Create host builder
        var hostBuilder = Microsoft.Extensions.Hosting.Host.CreateDefaultBuilder()
            .ConfigureServices((context, services) =>
            {
                // Add SIP message client using fluent configuration with enhanced settings
                services.AddSipMessageClient(builder =>
                    builder.WithServer("***********", 5060)
                           .WithCredentials("1001", "password", "asterisk.local")
                           .WithRegistrationExpiry(120) // 2 minutes expiry
                           .WithRegistrationRefreshInterval(30) // Refresh every 30 seconds for NAT traversal
                           .WithRetryPolicy(3, TimeSpan.FromSeconds(5))
                           .WithConnectionSettings(TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(10))
                           .WithAutoReregister(true));

                // Add the example service
                services.AddSingleton<SipMessageService>();
            })
            .ConfigureLogging(logging =>
            {
                logging.ClearProviders();
                logging.AddConsole();
                logging.SetMinimumLevel(LogLevel.Information);
            });

        // Build and run the host
        using var host = hostBuilder.Build();
        
        var logger = host.Services.GetRequiredService<ILogger<DependencyInjectionExample>>();
        logger.LogInformation("Starting dependency injection example");

        try
        {
            await host.StartAsync();

            // Get the SIP message service and run example
            var sipService = host.Services.GetRequiredService<SipMessageService>();
            await sipService.RunExampleAsync();

            // Keep running for a while
            await Task.Delay(30000);
        }
        finally
        {
            await host.StopAsync();
        }
    }
}

/// <summary>
/// Example service that uses SIP message client through dependency injection
/// </summary>
public class SipMessageService
{
    private readonly ISipMessageClient _sipClient;
    private readonly ILogger<SipMessageService> _logger;

    public SipMessageService(ISipMessageClient sipClient, ILogger<SipMessageService> logger)
    {
        _sipClient = sipClient ?? throw new ArgumentNullException(nameof(sipClient));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Runs the example using the injected SIP client
    /// </summary>
    public async Task RunExampleAsync()
    {
        _logger.LogInformation("Starting SIP message service example");

        // Subscribe to events
        _sipClient.RegistrationStateChanged += (sender, e) =>
        {
            _logger.LogInformation("Registration: {PreviousState} -> {CurrentState}", 
                e.PreviousState, e.CurrentState);
        };

        _sipClient.MessageStatusChanged += (sender, e) =>
        {
            _logger.LogInformation("Message {MessageId}: {PreviousStatus} -> {CurrentStatus}", 
                e.Message.MessageId, e.PreviousStatus, e.CurrentStatus);
        };

        try
        {
            // Start the client
            await _sipClient.StartAsync();
            _logger.LogInformation("SIP client started successfully");

            // Wait for registration
            await WaitForRegistrationAsync();

            // Send test messages
            await SendTestMessagesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SIP message service");
        }
    }

    private async Task WaitForRegistrationAsync()
    {
        var timeout = TimeSpan.FromSeconds(30);
        var startTime = DateTime.UtcNow;

        while (DateTime.UtcNow - startTime < timeout && 
               _sipClient.RegistrationState != Enums.RegistrationState.Registered)
        {
            await Task.Delay(500);
        }

        if (_sipClient.RegistrationState == Enums.RegistrationState.Registered)
        {
            _logger.LogInformation("SIP registration completed");
        }
        else
        {
            _logger.LogWarning("SIP registration did not complete within timeout");
        }
    }

    private async Task SendTestMessagesAsync()
    {
        var messages = new[]
        {
            "Hello from DI example!",
            "This is a test message",
            "SIP messaging works!"
        };

        foreach (var message in messages)
        {
            try
            {
                var messageId = await _sipClient.SendMessageAsync("1002", message);
                _logger.LogInformation("Sent message: {MessageId}", messageId);
                await Task.Delay(2000);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send message: {Message}", message);
            }
        }
    }
}
