using Microsoft.Extensions.Logging;
using Ngp.Communication.SipMessageProxy.Enums;
using Ngp.Communication.SipMessageProxy.Interfaces;

namespace Ngp.Communication.SipMessageProxy.Examples;

/// <summary>
/// Simple test to verify basic functionality
/// </summary>
public class SimpleTest
{
    /// <summary>
    /// Runs a simple test of the SIP message client
    /// </summary>
    public static async Task RunSimpleTestAsync()
    {
        // Create a simple console logger
        using var loggerFactory = LoggerFactory.Create(builder =>
            builder.AddConsole().SetMinimumLevel(LogLevel.Information));
        
        var logger = loggerFactory.CreateLogger<SimpleTest>();

        logger.LogInformation("Starting SIP Message Client simple test");

        try
        {
            // Create SIP client using factory with enhanced configuration
            var client = SipMessageClientFactory
                .CreateBuilder()
                .WithServer("***********", 6088)
                .WithCredentials("880001", "880001", "sipweema.com.tw")
                .WithTransport(SipTransport.Udp)
                .WithRegistrationExpiry(120) // 2 minutes expiry
                .WithRegistrationRefreshInterval(30) // Refresh every 30 seconds for NAT traversal
                .WithRetryPolicy(3, TimeSpan.FromSeconds(5))
                .WithConnectionSettings(TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(10))
                .WithLogger(logger)
                .Build();

            // Subscribe to events
            client.RegistrationStateChanged += (sender, e) =>
            {
                logger.LogInformation("Registration: {PreviousState} -> {CurrentState}", 
                    e.PreviousState, e.CurrentState);
            };

            client.MessageStatusChanged += (sender, e) =>
            {
                logger.LogInformation("Message {MessageId}: {PreviousStatus} -> {CurrentStatus}", 
                    e.Message.MessageId, e.PreviousStatus, e.CurrentStatus);
            };

            client.ConnectionStateChanged += (sender, e) =>
            {
                logger.LogInformation("Connection: {PreviousState} -> {CurrentState}", 
                    e.PreviousState, e.CurrentState);
            };

            // Test configuration
            logger.LogInformation("Configuration valid: {IsValid}", client.Configuration.IsValid());
            logger.LogInformation("Server: {Host}:{Port}", client.Configuration.ServerHost, client.Configuration.ServerPort);
            logger.LogInformation("User: {Username}@{Domain}", client.Configuration.Username, client.Configuration.Domain);

            // Start the client
            logger.LogInformation("Starting SIP client...");
            await client.StartAsync();

            // Wait for registration
            await WaitForRegistrationAsync(client, logger);

            // Send a test message
            if (client.RegistrationState == RegistrationState.Registered)
            {
                logger.LogInformation("Sending test message...");
                var messageId = await client.SendMessageAsync("1002", "Hello from SIP Message Client test!");
                logger.LogInformation("Message queued with ID: {MessageId}", messageId);

                // Wait a bit for message processing
                await Task.Delay(2000);

                // Check message status
                var message = client.GetMessageStatus(messageId);
                if (message != null)
                {
                    logger.LogInformation("Message status: {Status}", message.Status);
                    logger.LogInformation("Message created: {CreatedAt}", message.CreatedAt);
                    if (message.SentAt.HasValue)
                        logger.LogInformation("Message sent: {SentAt}", message.SentAt);
                    if (message.ResponseAt.HasValue)
                        logger.LogInformation("Message response: {ResponseAt}", message.ResponseAt);
                }

                // Display statistics
                var stats = client.GetStatistics();
                logger.LogInformation("Statistics - Total: {Total}, Delivered: {Delivered}, Failed: {Failed}", 
                    stats.TotalSent, stats.Delivered, stats.Failed);
            }

            // Stop the client
            logger.LogInformation("Stopping SIP client...");
            await client.StopAsync();

            client.Dispose();

            logger.LogInformation("Simple test completed successfully");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in simple test");
        }
    }

    private static async Task WaitForRegistrationAsync(ISipMessageClient client, ILogger logger)
    {
        var timeout = TimeSpan.FromSeconds(10);
        var startTime = DateTime.UtcNow;

        while (DateTime.UtcNow - startTime < timeout && 
               client.RegistrationState != RegistrationState.Registered)
        {
            if (client.RegistrationState == RegistrationState.RegistrationFailed)
            {
                logger.LogWarning("Registration failed");
                return;
            }

            await Task.Delay(500);
        }

        if (client.RegistrationState == RegistrationState.Registered)
        {
            logger.LogInformation("Registration completed successfully");
        }
        else
        {
            logger.LogWarning("Registration did not complete within timeout");
        }
    }
}
