namespace Ngp.Communication.SipMessageProxy.Enums;

/// <summary>
/// SIP registration states
/// </summary>
public enum RegistrationState
{
    /// <summary>
    /// Not registered and not attempting to register
    /// </summary>
    NotRegistered = 0,

    /// <summary>
    /// Currently attempting to register
    /// </summary>
    Registering = 1,

    /// <summary>
    /// Successfully registered
    /// </summary>
    Registered = 2,

    /// <summary>
    /// Registration failed
    /// </summary>
    RegistrationFailed = 3,

    /// <summary>
    /// Currently attempting to unregister
    /// </summary>
    Unregistering = 4,

    /// <summary>
    /// Registration expired and needs renewal
    /// </summary>
    Expired = 5,

    /// <summary>
    /// Temporary failure, will retry
    /// </summary>
    TemporaryFailure = 6
}
