namespace Ngp.Communication.SipMessageProxy.Enums;

/// <summary>
/// SIP connection states
/// </summary>
public enum ConnectionState
{
    /// <summary>
    /// Connection is disconnected
    /// </summary>
    Disconnected = 0,

    /// <summary>
    /// Connection is being established
    /// </summary>
    Connecting = 1,

    /// <summary>
    /// Connection is established and active
    /// </summary>
    Connected = 2,

    /// <summary>
    /// Connection is being closed
    /// </summary>
    Disconnecting = 3,

    /// <summary>
    /// Connection encountered an error
    /// </summary>
    Error = 4,

    /// <summary>
    /// Connection is being retried
    /// </summary>
    Retrying = 5
}
