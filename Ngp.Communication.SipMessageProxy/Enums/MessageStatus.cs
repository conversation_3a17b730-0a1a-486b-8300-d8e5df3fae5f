namespace Ngp.Communication.SipMessageProxy.Enums;

/// <summary>
/// SIP message delivery status
/// </summary>
public enum MessageStatus
{
    /// <summary>
    /// Message is queued for sending
    /// </summary>
    Queued = 0,

    /// <summary>
    /// Message is being sent
    /// </summary>
    Sending = 1,

    /// <summary>
    /// Message was sent successfully
    /// </summary>
    Sent = 2,

    /// <summary>
    /// Message was delivered successfully (received 200 OK)
    /// </summary>
    Delivered = 3,

    /// <summary>
    /// Message delivery failed
    /// </summary>
    Failed = 4,

    /// <summary>
    /// Message delivery timed out
    /// </summary>
    Timeout = 5,

    /// <summary>
    /// Message was rejected by the recipient
    /// </summary>
    Rejected = 6,

    /// <summary>
    /// Message is being retried
    /// </summary>
    Retrying = 7
}
