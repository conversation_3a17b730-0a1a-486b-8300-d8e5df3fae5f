using Microsoft.Extensions.Logging;
using Ngp.Communication.SipMessageProxy.Enums;
using Ngp.Communication.SipMessageProxy.Events;
using Ngp.Communication.SipMessageProxy.Models;
using SIPSorcery.SIP;
using System.Net;

namespace Ngp.Communication.SipMessageProxy.Services;

/// <summary>
/// Manages SIP transport connections with high performance and proper resource management
/// </summary>
public class SipConnectionManager : IDisposable
{
    private readonly SipConfiguration _configuration;
    private readonly ILogger _logger;
    private readonly SemaphoreSlim _connectionSemaphore;
    private readonly CancellationTokenSource _cancellationTokenSource;

    private SIPTransport? _sipTransport;
    private ConnectionState _currentState;
    private bool _disposed;

    /// <summary>
    /// Event raised when connection state changes
    /// </summary>
    public event EventHandler<ConnectionStateChangedEventArgs>? ConnectionStateChanged;

    /// <summary>
    /// Gets the current connection state
    /// </summary>
    public ConnectionState CurrentState => _currentState;

    /// <summary>
    /// Gets the SIP transport instance
    /// </summary>
    public SIPTransport? SipTransport => _sipTransport;

    /// <summary>
    /// Gets whether the connection manager is running
    /// </summary>
    public bool IsRunning { get; private set; }

    /// <summary>
    /// Initializes a new instance of the SipConnectionManager class
    /// </summary>
    /// <param name="configuration">SIP configuration</param>
    /// <param name="logger">Logger instance</param>
    public SipConnectionManager(SipConfiguration configuration, ILogger logger)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _connectionSemaphore = new SemaphoreSlim(1, 1);
        _cancellationTokenSource = new CancellationTokenSource();
        _currentState = ConnectionState.Disconnected;
    }

    /// <summary>
    /// Starts the connection manager and establishes SIP transport
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(SipConnectionManager));

        await _connectionSemaphore.WaitAsync(cancellationToken);
        try
        {
            if (IsRunning)
            {
                _logger.LogWarning("Connection manager is already running");
                return;
            }

            _logger.LogInformation("Starting SIP connection manager");

            ChangeState(ConnectionState.Connecting);

            // Create SIP transport with proper configuration
            _sipTransport = new SIPTransport();

            // TODO: Configure actual SIP transport when SIP Sorcery integration is complete

            IsRunning = true;
            ChangeState(ConnectionState.Connected);

            _logger.LogInformation("SIP connection manager started successfully on port {Port}",
                _configuration.LocalPort);
        }
        catch (Exception ex)
        {
            ChangeState(ConnectionState.Error, ex.Message);
            _logger.LogError(ex, "Failed to start SIP connection manager");
            throw;
        }
        finally
        {
            _connectionSemaphore.Release();
        }
    }

    /// <summary>
    /// Stops the connection manager and closes all connections
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            return;

        await _connectionSemaphore.WaitAsync(cancellationToken);
        try
        {
            if (!IsRunning)
            {
                _logger.LogWarning("Connection manager is not running");
                return;
            }

            _logger.LogInformation("Stopping SIP connection manager");

            ChangeState(ConnectionState.Disconnecting);

            // Gracefully shutdown SIP transport
            if (_sipTransport != null)
            {
                // Allow time for pending operations to complete
                await Task.Delay(1000, cancellationToken);

                _sipTransport.Shutdown();
                _sipTransport = null;
            }

            IsRunning = false;
            ChangeState(ConnectionState.Disconnected);

            _logger.LogInformation("SIP connection manager stopped successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping SIP connection manager");
            ChangeState(ConnectionState.Error, ex.Message);
        }
        finally
        {
            _connectionSemaphore.Release();
        }
    }

    /// <summary>
    /// Performs a graceful shutdown with proper resource cleanup
    /// </summary>
    /// <param name="timeout">Maximum time to wait for graceful shutdown</param>
    /// <returns>Task representing the async operation</returns>
    public async Task GracefulShutdownAsync(TimeSpan timeout = default)
    {
        if (timeout == default)
            timeout = TimeSpan.FromSeconds(10);

        _logger.LogInformation("Performing graceful shutdown with {Timeout}s timeout", timeout.TotalSeconds);

        using var cts = new CancellationTokenSource(timeout);
        
        try
        {
            await StopAsync(cts.Token);
        }
        catch (OperationCanceledException)
        {
            _logger.LogWarning("Graceful shutdown timed out, forcing shutdown");
            // Force shutdown if graceful shutdown times out
            _sipTransport?.Shutdown();
            ChangeState(ConnectionState.Disconnected);
        }
    }

    /// <summary>
    /// Changes the connection state and raises the event
    /// </summary>
    /// <param name="newState">New connection state</param>
    /// <param name="errorMessage">Optional error message</param>
    private void ChangeState(ConnectionState newState, string? errorMessage = null)
    {
        var previousState = _currentState;
        _currentState = newState;

        _logger.LogInformation("Connection state changed: {PreviousState} -> {CurrentState}", previousState, newState);

        var eventArgs = new ConnectionStateChangedEventArgs(previousState, newState, errorMessage);
        ConnectionStateChanged?.Invoke(this, eventArgs);
    }

    // TODO: Implement SIP message handlers when SIP Sorcery integration is complete

    /// <summary>
    /// Disposes the connection manager
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;

        _cancellationTokenSource.Cancel();

        try
        {
            // Perform graceful shutdown with a reasonable timeout
            GracefulShutdownAsync(TimeSpan.FromSeconds(5)).GetAwaiter().GetResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during disposal");
        }

        _connectionSemaphore.Dispose();
        _cancellationTokenSource.Dispose();
    }
}
