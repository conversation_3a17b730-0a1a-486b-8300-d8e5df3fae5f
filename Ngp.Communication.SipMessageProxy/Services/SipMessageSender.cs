using Microsoft.Extensions.Logging;
using Ngp.Communication.SipMessageProxy.Enums;
using Ngp.Communication.SipMessageProxy.Events;
using Ngp.Communication.SipMessageProxy.Models;
using SIPSorcery.SIP;
using SIPSorcery.SIP.App;
using System.Collections.Concurrent;
using System.Linq;
using System.Text;

namespace Ngp.Communication.SipMessageProxy.Services;

/// <summary>
/// Manages SIP message sending using SIP Sorcery
/// </summary>
public class SipMessageSender : IDisposable
{
    private readonly SipConfiguration _configuration;
    private readonly ILogger _logger;
    private readonly SemaphoreSlim _sendSemaphore;
    private readonly CancellationTokenSource _cancellationTokenSource;
    private readonly ConcurrentDictionary<string, SipMessage> _messages;
    private readonly ConcurrentQueue<SipMessage> _messageQueue;
    private readonly Timer _processQueueTimer;

    private SIPTransport? _sipTransport;
    private bool _disposed;
    private bool _isProcessing;
    private int _actualLocalPort;

    /// <summary>
    /// Event raised when message status changes
    /// </summary>
    public event EventHandler<MessageStatusChangedEventArgs>? MessageStatusChanged;

    /// <summary>
    /// Gets whether the message sender is running
    /// </summary>
    public bool IsRunning { get; private set; }

    /// <summary>
    /// Initializes a new instance of the SipMessageSender class
    /// </summary>
    /// <param name="configuration">SIP configuration</param>
    /// <param name="logger">Logger instance</param>
    public SipMessageSender(SipConfiguration configuration, ILogger logger)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _sendSemaphore = new SemaphoreSlim(1, 1);
        _cancellationTokenSource = new CancellationTokenSource();
        _messages = new ConcurrentDictionary<string, SipMessage>();
        _messageQueue = new ConcurrentQueue<SipMessage>();
        
        // Process queue every 100ms
        _processQueueTimer = new Timer(ProcessMessageQueue, null, TimeSpan.FromMilliseconds(100), TimeSpan.FromMilliseconds(100));
    }

    /// <summary>
    /// Starts the message sender
    /// </summary>
    /// <param name="sipTransport">SIP transport to use</param>
    /// <param name="actualLocalPort">The actual local port that the SIP transport is bound to</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task StartAsync(SIPTransport sipTransport, int actualLocalPort, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(SipMessageSender));

        await _sendSemaphore.WaitAsync(cancellationToken);
        try
        {
            if (IsRunning)
            {
                _logger.LogWarning("Message sender is already running");
                return;
            }

            _sipTransport = sipTransport ?? throw new ArgumentNullException(nameof(sipTransport));
            _actualLocalPort = actualLocalPort;
            IsRunning = true;

            _logger.LogInformation("SIP message sender started successfully using local port {ActualLocalPort}", _actualLocalPort);
        }
        finally
        {
            _sendSemaphore.Release();
        }
    }

    /// <summary>
    /// Stops the message sender
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            return;

        await _sendSemaphore.WaitAsync(cancellationToken);
        try
        {
            if (!IsRunning)
            {
                _logger.LogWarning("Message sender is not running");
                return;
            }

            _logger.LogInformation("Stopping SIP message sender");

            IsRunning = false;
            _sipTransport = null;

            // Mark all pending messages as failed
            foreach (var message in _messages.Values.Where(m => m.Status == MessageStatus.Queued || m.Status == MessageStatus.Sending))
            {
                ChangeMessageStatus(message, MessageStatus.Failed, "Service stopped");
            }

            _logger.LogInformation("SIP message sender stopped successfully");
        }
        finally
        {
            _sendSemaphore.Release();
        }
    }

    /// <summary>
    /// Queues a message for sending
    /// </summary>
    /// <param name="toExtension">Target extension</param>
    /// <param name="content">Message content</param>
    /// <param name="contentType">Content type</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Message ID for tracking</returns>
    public Task<string> QueueMessageAsync(
        string toExtension,
        string content,
        string contentType = "text/plain",
        CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(SipMessageSender));

        if (!IsRunning)
            throw new InvalidOperationException("Message sender is not running");

        if (string.IsNullOrWhiteSpace(toExtension))
            throw new ArgumentException("Target extension cannot be null or empty", nameof(toExtension));

        if (string.IsNullOrWhiteSpace(content))
            throw new ArgumentException("Content cannot be null or empty", nameof(content));

        var message = new SipMessage
        {
            ToExtension = toExtension,
            Content = content,
            ContentType = contentType,
            Status = MessageStatus.Queued
        };

        _messages.TryAdd(message.MessageId, message);
        _messageQueue.Enqueue(message);

        _logger.LogDebug("Message queued: {MessageId} to {Extension}", message.MessageId, toExtension);

        ChangeMessageStatus(message, MessageStatus.Queued);

        return Task.FromResult(message.MessageId);
    }

    /// <summary>
    /// Gets the status of a specific message
    /// </summary>
    /// <param name="messageId">Message ID</param>
    /// <returns>Message or null if not found</returns>
    public SipMessage? GetMessage(string messageId)
    {
        return _messages.TryGetValue(messageId, out var message) ? message : null;
    }

    /// <summary>
    /// Gets all messages with optional filtering
    /// </summary>
    /// <param name="status">Optional status filter</param>
    /// <param name="fromDate">Optional from date filter</param>
    /// <param name="toDate">Optional to date filter</param>
    /// <returns>List of messages</returns>
    public IReadOnlyList<SipMessage> GetMessages(
        MessageStatus? status = null,
        DateTime? fromDate = null,
        DateTime? toDate = null)
    {
        var query = _messages.Values.AsEnumerable();

        if (status.HasValue)
            query = query.Where(m => m.Status == status.Value);

        if (fromDate.HasValue)
            query = query.Where(m => m.CreatedAt >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(m => m.CreatedAt <= toDate.Value);

        return query.OrderByDescending(m => m.CreatedAt).ToList();
    }

    /// <summary>
    /// Gets message statistics
    /// </summary>
    /// <returns>Message statistics</returns>
    public MessageStatistics GetStatistics()
    {
        var messages = _messages.Values.ToList();
        var deliveredMessages = messages.Where(m => m.IsDelivered && m.DurationMs.HasValue).ToList();

        return new MessageStatistics
        {
            TotalSent = messages.Count,
            Delivered = messages.Count(m => m.Status == MessageStatus.Delivered),
            Failed = messages.Count(m => m.Status == MessageStatus.Failed),
            Pending = messages.Count(m => m.Status == MessageStatus.Queued || m.Status == MessageStatus.Sending),
            Timeout = messages.Count(m => m.Status == MessageStatus.Timeout),
            Rejected = messages.Count(m => m.Status == MessageStatus.Rejected),
            AverageDeliveryTimeMs = deliveredMessages.Any() ? deliveredMessages.Average(m => m.DurationMs!.Value) : 0,
            LastUpdated = DateTime.UtcNow
        };
    }

    /// <summary>
    /// Changes the status of a message and raises the event
    /// </summary>
    /// <param name="message">Message to update</param>
    /// <param name="newStatus">New status</param>
    /// <param name="errorMessage">Optional error message</param>
    private void ChangeMessageStatus(SipMessage message, MessageStatus newStatus, string? errorMessage = null)
    {
        var previousStatus = message.Status;
        message.Status = newStatus;

        if (!string.IsNullOrEmpty(errorMessage))
            message.ErrorMessage = errorMessage;

        if (newStatus == MessageStatus.Sending)
            message.SentAt = DateTime.UtcNow;
        else if (newStatus == MessageStatus.Delivered || newStatus == MessageStatus.Failed || 
                 newStatus == MessageStatus.Timeout || newStatus == MessageStatus.Rejected)
            message.ResponseAt = DateTime.UtcNow;

        _logger.LogDebug("Message status changed: {MessageId} {PreviousStatus} -> {CurrentStatus}", 
            message.MessageId, previousStatus, newStatus);

        var eventArgs = new MessageStatusChangedEventArgs(message, previousStatus, newStatus);
        MessageStatusChanged?.Invoke(this, eventArgs);
    }

    /// <summary>
    /// Timer callback to process the message queue
    /// </summary>
    private void ProcessMessageQueue(object? state)
    {
        if (_disposed || !IsRunning || _isProcessing || _sipTransport == null)
            return;

        _isProcessing = true;
        try
        {
            while (_messageQueue.TryDequeue(out var message))
            {
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await SendMessageAsync(message);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error sending message {MessageId}", message.MessageId);
                        ChangeMessageStatus(message, MessageStatus.Failed, ex.Message);
                    }
                });
            }
        }
        finally
        {
            _isProcessing = false;
        }
    }

    /// <summary>
    /// Sends a SIP MESSAGE request
    /// </summary>
    /// <param name="message">Message to send</param>
    /// <returns>Task representing the async operation</returns>
    private async Task SendMessageAsync(SipMessage message)
    {
        if (_sipTransport == null)
        {
            ChangeMessageStatus(message, MessageStatus.Failed, "SIP transport not available");
            return;
        }

        ChangeMessageStatus(message, MessageStatus.Sending);

        try
        {
            await SendSipRequestWithAuthenticationAsync(message);
        }
        catch (Exception ex)
        {
            ChangeMessageStatus(message, MessageStatus.Failed, ex.Message);
            _logger.LogError(ex, "Error sending message {MessageId}", message.MessageId);

            // Implement retry logic
            if (message.RetryCount < _configuration.MaxRetries)
            {
                message.RetryCount++;
                ChangeMessageStatus(message, MessageStatus.Retrying);

                await Task.Delay(_configuration.RetryDelay, _cancellationTokenSource.Token);
                _messageQueue.Enqueue(message);

                _logger.LogInformation("Retrying message {MessageId} (attempt {RetryCount}/{MaxRetries})",
                    message.MessageId, message.RetryCount, _configuration.MaxRetries);
            }
        }
    }

    /// <summary>
    /// Sends a SIP MESSAGE request and automatically handles one 401/407 authentication challenge using Digest authentication.
    /// </summary>
    private async Task SendSipRequestWithAuthenticationAsync(SipMessage message)
    {
        // Helper local function to build a MESSAGE request
        SIPRequest BuildSipMessageRequest(bool includeAuthorizationHeader, SIPAuthorisationDigest? digest)
        {
            var toUri = SIPURI.ParseSIPURI($"sip:{message.ToExtension}@{_configuration.Domain}");
            var fromUri = SIPURI.ParseSIPURI($"sip:{_configuration.Username}@{_configuration.Domain}");

            var request = new SIPRequest(SIPMethodsEnum.MESSAGE, toUri);

            request.Header = new SIPHeader
            {
                To = new SIPToHeader(null, toUri, null),
                From = new SIPFromHeader(null, fromUri, CallProperties.CreateNewTag()),
                CallId = CallProperties.CreateNewCallId(),
                CSeq = 1,
                MaxForwards = 70,
                UserAgent = _configuration.UserAgent
            };

            request.Body = message.Content;
            request.Header.ContentType = message.ContentType;
            request.Header.ContentLength = Encoding.UTF8.GetByteCount(message.Content);

            // --- 必要 SIP 標頭 ---
            // 1. Contact
            var contactUri = SIPURI.ParseSIPURI($"sip:{_configuration.Username}@{_configuration.Domain}");
            request.Header.Contact = new List<SIPContactHeader> { new SIPContactHeader(null, contactUri) };

            // 2. Via (單筆即可，SIPTransport 仍會視需要再加)
            var viaTransportEnum = _configuration.Transport == SipTransport.Udp ? SIPProtocolsEnum.udp : SIPProtocolsEnum.tcp;
            var viaHeader = new SIPViaHeader("0.0.0.0", _actualLocalPort, viaTransportEnum.ToString());
            viaHeader.Branch = CallProperties.CreateBranchId();
            request.Header.Vias = new SIPViaSet();
            request.Header.Vias.PushViaHeader(viaHeader);

            return request;
        }

        var serverEndpoint = new SIPEndPoint(SIPProtocolsEnum.udp,
            System.Net.IPAddress.Parse(_configuration.ServerHost), _configuration.ServerPort);

        // 直接送出，不等待回覆；若送出成功即視為 Sent，再標為 Delivered。
        ChangeMessageStatus(message, MessageStatus.Sending);

        var sipRequest = BuildSipMessageRequest(false, null);

        await _sipTransport!.SendRequestAsync(serverEndpoint, sipRequest);

        ChangeMessageStatus(message, MessageStatus.Delivered);

        _logger.LogDebug("MESSAGE sent (assumed delivered): {MessageId}", message.MessageId);
    }

    /// <summary>
    /// Disposes the message sender
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;

        _cancellationTokenSource.Cancel();

        try
        {
            StopAsync().GetAwaiter().GetResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during disposal");
        }

        _processQueueTimer?.Dispose();
        _sendSemaphore.Dispose();
        _cancellationTokenSource.Dispose();
    }
}
