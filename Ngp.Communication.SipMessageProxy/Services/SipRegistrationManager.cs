using Microsoft.Extensions.Logging;
using Ngp.Communication.SipMessageProxy.Enums;
using Ngp.Communication.SipMessageProxy.Events;
using Ngp.Communication.SipMessageProxy.Models;
using SIPSorcery.SIP;
using SIPSorcery.SIP.App;
using System.Net;

namespace Ngp.Communication.SipMessageProxy.Services;

/// <summary>
/// Manages SIP registration using SIP Sorcery
/// </summary>
public class SipRegistrationManager : IDisposable
{
    private readonly SipConfiguration _configuration;
    private readonly ILogger _logger;
    private readonly SemaphoreSlim _registrationSemaphore;
    private readonly CancellationTokenSource _cancellationTokenSource;

    private SIPTransport? _sipTransport;
    private SIPRegistrationUserAgent? _registrationUserAgent;
    private RegistrationState _currentState;
    private Timer? _reregistrationTimer;
    private bool _disposed;
    private int _actualLocalPort;

    /// <summary>
    /// Event raised when registration state changes
    /// </summary>
    public event EventHandler<RegistrationStateChangedEventArgs>? RegistrationStateChanged;

    /// <summary>
    /// Gets the current registration state
    /// </summary>
    public RegistrationState CurrentState => _currentState;

    /// <summary>
    /// Gets whether the registration manager is running
    /// </summary>
    public bool IsRunning { get; private set; }

    /// <summary>
    /// Gets the SIP transport instance for sharing with other components
    /// </summary>
    public SIPTransport? SipTransport => _sipTransport;

    /// <summary>
    /// Gets the actual local port that the SIP transport is bound to
    /// </summary>
    public int ActualLocalPort => _actualLocalPort;

    /// <summary>
    /// Initializes a new instance of the SipRegistrationManager class
    /// </summary>
    /// <param name="configuration">SIP configuration</param>
    /// <param name="logger">Logger instance</param>
    public SipRegistrationManager(SipConfiguration configuration, ILogger logger)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _registrationSemaphore = new SemaphoreSlim(1, 1);
        _cancellationTokenSource = new CancellationTokenSource();
        _currentState = RegistrationState.NotRegistered;
    }

    /// <summary>
    /// Starts the registration manager
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(SipRegistrationManager));

        await _registrationSemaphore.WaitAsync(cancellationToken);
        try
        {
            if (IsRunning)
            {
                _logger.LogWarning("Registration manager is already running");
                return;
            }

            _logger.LogInformation("Starting SIP registration manager");

            // Initialize SIP transport with proper listening configuration
            _sipTransport = new SIPTransport();

            // Create UDP channel with automatic port selection if LocalPort is 0
            SIPUDPChannel udpChannel;
            if (_configuration.LocalPort == 0)
            {
                // Use automatic port selection by creating channel with port 0
                var autoEndPoint = new IPEndPoint(IPAddress.Any, 0);
                udpChannel = new SIPUDPChannel(autoEndPoint);
                _sipTransport.AddSIPChannel(udpChannel);

                // Get the actual assigned port after binding
                _actualLocalPort = ((IPEndPoint)udpChannel.ListeningEndPoint).Port;
                _logger.LogInformation("SIP transport automatically bound to port {ActualPort}", _actualLocalPort);
            }
            else
            {
                // Use the configured local port
                var localEndPoint = new SIPEndPoint(SIPProtocolsEnum.udp,
                    System.Net.IPAddress.Any, _configuration.LocalPort);
                udpChannel = new SIPUDPChannel(localEndPoint.GetIPEndPoint());
                _sipTransport.AddSIPChannel(udpChannel);
                _actualLocalPort = _configuration.LocalPort;
                _logger.LogInformation("SIP transport bound to configured port {ConfiguredPort}", _configuration.LocalPort);
            }

            // Enable SIP transport logging for debugging
            _sipTransport.SIPRequestInTraceEvent += (localEndPoint, remoteEndPoint, request) =>
            {
                _logger.LogDebug("SIP Request IN from {RemoteEndPoint}: {Request}", remoteEndPoint, request.ToString());
            };

            _sipTransport.SIPRequestOutTraceEvent += (localEndPoint, remoteEndPoint, request) =>
            {
                _logger.LogDebug("SIP Request OUT to {RemoteEndPoint}: {Request}", remoteEndPoint, request.ToString());
            };

            _sipTransport.SIPResponseInTraceEvent += (localEndPoint, remoteEndPoint, response) =>
            {
                _logger.LogDebug("SIP Response IN from {RemoteEndPoint}: {Response}", remoteEndPoint, response.ToString());
            };

            _sipTransport.SIPResponseOutTraceEvent += (localEndPoint, remoteEndPoint, response) =>
            {
                _logger.LogDebug("SIP Response OUT to {RemoteEndPoint}: {Response}", remoteEndPoint, response.ToString());
            };

            // Handle incoming SIP requests (especially OPTIONS)
            _sipTransport.SIPTransportRequestReceived += OnSipRequestReceived;

            // Create SIP registration user agent
            // Combine server address and port for the registrar domain
            var registrarDomain = $"{_configuration.ServerHost}:{_configuration.ServerPort}";
            _registrationUserAgent = new SIPRegistrationUserAgent(
                _sipTransport,
                _configuration.Username,
                _configuration.Password,
                registrarDomain,
                (int)_configuration.RegistrationExpiry);

            // Set up event handlers
            _registrationUserAgent.RegistrationFailed += (uri, response, err) =>
            {
                _logger.LogError("SIP Registration failed for {Uri}: {Error} (Status: {StatusCode})", uri, err, response?.StatusCode);
                ChangeState(RegistrationState.RegistrationFailed, err);
            };

            _registrationUserAgent.RegistrationTemporaryFailure += (uri, response, msg) =>
            {
                _logger.LogWarning("SIP Registration temporary failure for {Uri}: {Message} (Status: {StatusCode})", uri, msg, response?.StatusCode);
                // For 401 Unauthorized, this is normal authentication challenge, keep trying
                if (response?.StatusCode == (int)SIPSorcery.SIP.SIPResponseStatusCodesEnum.Unauthorised)
                {
                    _logger.LogDebug("Received 401 Unauthorized - authentication challenge, continuing registration process");
                    // Don't change state to failed, let SIPRegistrationUserAgent handle the challenge
                    return;
                }
                ChangeState(RegistrationState.Registering, msg);
            };

            _registrationUserAgent.RegistrationRemoved += (uri, response) =>
            {
                _logger.LogInformation("SIP Registration removed for {Uri}", uri);
                ChangeState(RegistrationState.NotRegistered);
            };

            _registrationUserAgent.RegistrationSuccessful += (uri, response) =>
            {
                _logger.LogInformation("SIP Registration successful for {Uri}", uri);
                ChangeState(RegistrationState.Registered);

                // Start registration refresh timer if auto-reregister is enabled
                if (_configuration.AutoReregister)
                {
                    StartRegistrationRefreshTimer();
                }
            };

            IsRunning = true;
            ChangeState(RegistrationState.Registering);

            // Start the registration process
            _registrationUserAgent.Start();

            _logger.LogInformation("SIP registration manager started successfully");
        }
        finally
        {
            _registrationSemaphore.Release();
        }
    }

    /// <summary>
    /// Stops the registration manager
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            return;

        await _registrationSemaphore.WaitAsync(cancellationToken);
        try
        {
            if (!IsRunning)
            {
                _logger.LogWarning("Registration manager is not running");
                return;
            }

            _logger.LogInformation("Stopping SIP registration manager");

            ChangeState(RegistrationState.Unregistering);

            // Stop re-registration timer
            _reregistrationTimer?.Dispose();
            _reregistrationTimer = null;

            // Stop registration user agent
            _registrationUserAgent?.Stop();
            _registrationUserAgent = null;

            // Close SIP transport
            _sipTransport?.Shutdown();
            _sipTransport = null;

            IsRunning = false;
            ChangeState(RegistrationState.NotRegistered);

            _logger.LogInformation("SIP registration manager stopped successfully");
        }
        finally
        {
            _registrationSemaphore.Release();
        }
    }

    /// <summary>
    /// Forces a re-registration attempt
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task ForceReregisterAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(SipRegistrationManager));

        if (!IsRunning)
        {
            _logger.LogWarning("Cannot force re-registration: manager is not running");
            return;
        }

        await _registrationSemaphore.WaitAsync(cancellationToken);
        try
        {
            _logger.LogInformation("Forcing re-registration");
            ChangeState(RegistrationState.Registering);

            // Force re-registration using the registration user agent
            if (_registrationUserAgent != null)
            {
                _registrationUserAgent.Stop();
                _registrationUserAgent.Start();
            }
            else
            {
                _logger.LogWarning("Cannot force re-registration: registration user agent is not available");
                ChangeState(RegistrationState.RegistrationFailed, "Registration user agent not available");
            }
        }
        finally
        {
            _registrationSemaphore.Release();
        }
    }

    /// <summary>
    /// Changes the registration state and raises the event
    /// </summary>
    /// <param name="newState">New registration state</param>
    /// <param name="errorMessage">Optional error message</param>
    /// <param name="statusCode">Optional SIP status code</param>
    /// <param name="reasonPhrase">Optional SIP reason phrase</param>
    private void ChangeState(RegistrationState newState, string? errorMessage = null, ushort? statusCode = null, string? reasonPhrase = null)
    {
        var previousState = _currentState;
        _currentState = newState;

        _logger.LogInformation("Registration state changed: {PreviousState} -> {CurrentState}", previousState, newState);

        var eventArgs = new RegistrationStateChangedEventArgs(previousState, newState, errorMessage, statusCode, reasonPhrase);
        RegistrationStateChanged?.Invoke(this, eventArgs);
    }

    /// <summary>
    /// Handles incoming SIP requests, particularly OPTIONS requests for keep-alive
    /// </summary>
    private async Task OnSipRequestReceived(SIPEndPoint localEndPoint, SIPEndPoint remoteEndPoint, SIPRequest sipRequest)
    {
        try
        {
            _logger.LogDebug("Received SIP request {Method} from {RemoteEndPoint}", sipRequest.Method, remoteEndPoint);

            if (sipRequest.Method == SIPMethodsEnum.OPTIONS)
            {
                // Respond to OPTIONS request to maintain registration
                var optionsResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.Ok, null);

                // Add Allow header to indicate supported methods
                optionsResponse.Header.Allow = "INVITE, ACK, CANCEL, BYE, NOTIFY, REFER, MESSAGE, OPTIONS, INFO, SUBSCRIBE";

                // Add Accept header for supported content types
                optionsResponse.Header.Accept = "application/sdp, application/dtmf-relay";

                // Send the response
                if (_sipTransport != null)
                {
                    await _sipTransport.SendResponseAsync(optionsResponse);
                    _logger.LogDebug("Sent 200 OK response to OPTIONS request from {RemoteEndPoint}", remoteEndPoint);
                }
            }
            else
            {
                // For other methods, send 405 Method Not Allowed
                var response = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.MethodNotAllowed, null);
                response.Header.Allow = "INVITE, ACK, CANCEL, BYE, NOTIFY, REFER, MESSAGE, OPTIONS, INFO, SUBSCRIBE";

                if (_sipTransport != null)
                {
                    await _sipTransport.SendResponseAsync(response);
                    _logger.LogDebug("Sent 405 Method Not Allowed response to {Method} request from {RemoteEndPoint}",
                        sipRequest.Method, remoteEndPoint);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling SIP request {Method} from {RemoteEndPoint}",
                sipRequest.Method, remoteEndPoint);
        }
    }

    /// <summary>
    /// Starts the registration refresh timer
    /// </summary>
    private void StartRegistrationRefreshTimer()
    {
        // Stop existing timer
        _reregistrationTimer?.Dispose();

        var refreshInterval = TimeSpan.FromSeconds(_configuration.EffectiveRegistrationRefreshInterval);
        _logger.LogInformation("Starting registration refresh timer with interval {RefreshInterval}", refreshInterval);

        _reregistrationTimer = new Timer(OnReregistrationTimer, null, refreshInterval, refreshInterval);
    }



    /// <summary>
    /// Timer callback for re-registration
    /// </summary>
    private void OnReregistrationTimer(object? state)
    {
        if (!IsRunning || _disposed)
            return;

        _ = Task.Run(async () =>
        {
            try
            {
                await ForceReregisterAsync(_cancellationTokenSource.Token);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during automatic re-registration");
            }
        });
    }





    /// <summary>
    /// Disposes the registration manager
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;

        _cancellationTokenSource.Cancel();

        try
        {
            StopAsync().GetAwaiter().GetResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during disposal");
        }

        _reregistrationTimer?.Dispose();
        _registrationSemaphore.Dispose();
        _cancellationTokenSource.Dispose();
    }
}
