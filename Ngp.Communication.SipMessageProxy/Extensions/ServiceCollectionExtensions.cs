using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Ngp.Communication.SipMessageProxy.Interfaces;
using Ngp.Communication.SipMessageProxy.Models;

namespace Ngp.Communication.SipMessageProxy.Extensions;

/// <summary>
/// Extension methods for registering SIP message client services
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adds SIP message client services to the dependency injection container
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configureOptions">Configuration action</param>
    /// <returns>Service collection for method chaining</returns>
    public static IServiceCollection AddSipMessageClient(
        this IServiceCollection services,
        Action<SipConfiguration> configureOptions)
    {
        if (services == null)
            throw new ArgumentNullException(nameof(services));
        if (configureOptions == null)
            throw new ArgumentNullException(nameof(configureOptions));

        var configuration = new SipConfiguration();
        configureOptions(configuration);

        if (!configuration.IsValid())
            throw new InvalidOperationException("SIP configuration is not valid");

        services.AddSingleton(configuration);
        services.AddSingleton<ISipMessageClient>(provider =>
        {
            var logger = provider.GetService<ILogger<SipMessageClient>>();
            return new SipMessageClient(configuration, logger ?? Microsoft.Extensions.Logging.Abstractions.NullLogger<SipMessageClient>.Instance);
        });

        return services;
    }

    /// <summary>
    /// Adds SIP message client services with fluent configuration
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configureBuilder">Builder configuration action</param>
    /// <returns>Service collection for method chaining</returns>
    public static IServiceCollection AddSipMessageClient(
        this IServiceCollection services,
        Action<ISipMessageClientBuilder> configureBuilder)
    {
        if (services == null)
            throw new ArgumentNullException(nameof(services));
        if (configureBuilder == null)
            throw new ArgumentNullException(nameof(configureBuilder));

        services.AddSingleton<ISipMessageClient>(provider =>
        {
            var logger = provider.GetService<ILogger<SipMessageClient>>();
            var builder = SipMessageClientFactory.CreateBuilder();
            
            if (logger != null)
                builder.WithLogger(logger);
                
            configureBuilder(builder);
            return builder.Build();
        });

        return services;
    }

    /// <summary>
    /// Adds SIP message client services with basic configuration
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="serverHost">SIP server host</param>
    /// <param name="username">SIP username</param>
    /// <param name="password">SIP password</param>
    /// <param name="domain">SIP domain</param>
    /// <returns>Service collection for method chaining</returns>
    public static IServiceCollection AddSipMessageClient(
        this IServiceCollection services,
        string serverHost,
        string username,
        string password,
        string domain)
    {
        return services.AddSipMessageClient(builder =>
            builder.WithServer(serverHost)
                   .WithCredentials(username, password, domain));
    }

    /// <summary>
    /// Adds SIP message client services with full configuration
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="serverHost">SIP server host</param>
    /// <param name="serverPort">SIP server port</param>
    /// <param name="username">SIP username</param>
    /// <param name="password">SIP password</param>
    /// <param name="domain">SIP domain</param>
    /// <returns>Service collection for method chaining</returns>
    public static IServiceCollection AddSipMessageClient(
        this IServiceCollection services,
        string serverHost,
        ushort serverPort,
        string username,
        string password,
        string domain)
    {
        return services.AddSipMessageClient(builder =>
            builder.WithServer(serverHost, serverPort)
                   .WithCredentials(username, password, domain));
    }
}
