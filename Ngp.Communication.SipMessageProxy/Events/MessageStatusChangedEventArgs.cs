using Ngp.Communication.SipMessageProxy.Enums;
using Ngp.Communication.SipMessageProxy.Models;

namespace Ngp.Communication.SipMessageProxy.Events;

/// <summary>
/// Event arguments for message status changes
/// </summary>
public class MessageStatusChangedEventArgs : EventArgs
{
    /// <summary>
    /// Gets the message that changed status
    /// </summary>
    public SipMessage Message { get; }

    /// <summary>
    /// Gets the previous message status
    /// </summary>
    public MessageStatus PreviousStatus { get; }

    /// <summary>
    /// Gets the current message status
    /// </summary>
    public MessageStatus CurrentStatus { get; }

    /// <summary>
    /// Gets the timestamp when the status change occurred
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// Initializes a new instance of the MessageStatusChangedEventArgs class
    /// </summary>
    /// <param name="message">The message that changed status</param>
    /// <param name="previousStatus">Previous message status</param>
    /// <param name="currentStatus">Current message status</param>
    public MessageStatusChangedEventArgs(
        SipMessage message,
        MessageStatus previousStatus,
        MessageStatus currentStatus)
    {
        Message = message ?? throw new ArgumentNullException(nameof(message));
        PreviousStatus = previousStatus;
        CurrentStatus = currentStatus;
        Timestamp = DateTime.UtcNow;
    }
}
