using Ngp.Communication.SipMessageProxy.Enums;

namespace Ngp.Communication.SipMessageProxy.Events;

/// <summary>
/// Event arguments for connection state changes
/// </summary>
public class ConnectionStateChangedEventArgs : EventArgs
{
    /// <summary>
    /// Gets the previous connection state
    /// </summary>
    public ConnectionState PreviousState { get; }

    /// <summary>
    /// Gets the current connection state
    /// </summary>
    public ConnectionState CurrentState { get; }

    /// <summary>
    /// Gets the error message if the state change was due to an error
    /// </summary>
    public string? ErrorMessage { get; }

    /// <summary>
    /// Gets the timestamp when the state change occurred
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// Initializes a new instance of the ConnectionStateChangedEventArgs class
    /// </summary>
    /// <param name="previousState">Previous connection state</param>
    /// <param name="currentState">Current connection state</param>
    /// <param name="errorMessage">Optional error message</param>
    public ConnectionStateChangedEventArgs(
        ConnectionState previousState,
        ConnectionState currentState,
        string? errorMessage = null)
    {
        PreviousState = previousState;
        CurrentState = currentState;
        ErrorMessage = errorMessage;
        Timestamp = DateTime.UtcNow;
    }
}
