using Ngp.Communication.SipMessageProxy.Enums;

namespace Ngp.Communication.SipMessageProxy.Events;

/// <summary>
/// Event arguments for registration state changes
/// </summary>
public class RegistrationStateChangedEventArgs : EventArgs
{
    /// <summary>
    /// Gets the previous registration state
    /// </summary>
    public RegistrationState PreviousState { get; }

    /// <summary>
    /// Gets the current registration state
    /// </summary>
    public RegistrationState CurrentState { get; }

    /// <summary>
    /// Gets the error message if the state change was due to an error
    /// </summary>
    public string? ErrorMessage { get; }

    /// <summary>
    /// Gets the SIP response status code if applicable
    /// </summary>
    public ushort? StatusCode { get; }

    /// <summary>
    /// Gets the SIP response reason phrase if applicable
    /// </summary>
    public string? ReasonPhrase { get; }

    /// <summary>
    /// Gets the timestamp when the state change occurred
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// Initializes a new instance of the RegistrationStateChangedEventArgs class
    /// </summary>
    /// <param name="previousState">Previous registration state</param>
    /// <param name="currentState">Current registration state</param>
    /// <param name="errorMessage">Optional error message</param>
    /// <param name="statusCode">Optional SIP status code</param>
    /// <param name="reasonPhrase">Optional SIP reason phrase</param>
    public RegistrationStateChangedEventArgs(
        RegistrationState previousState,
        RegistrationState currentState,
        string? errorMessage = null,
        ushort? statusCode = null,
        string? reasonPhrase = null)
    {
        PreviousState = previousState;
        CurrentState = currentState;
        ErrorMessage = errorMessage;
        StatusCode = statusCode;
        ReasonPhrase = reasonPhrase;
        Timestamp = DateTime.UtcNow;
    }
}
