using Ngp.Communication.SipMessageProxy.Enums;
using Ngp.Communication.SipMessageProxy.Events;
using Ngp.Communication.SipMessageProxy.Models;

namespace Ngp.Communication.SipMessageProxy.Interfaces;

/// <summary>
/// Interface for SIP message client functionality
/// </summary>
public interface ISipMessageClient : IDisposable
{
    /// <summary>
    /// Event raised when registration state changes
    /// </summary>
    event EventHandler<RegistrationStateChangedEventArgs>? RegistrationStateChanged;

    /// <summary>
    /// Event raised when message status changes
    /// </summary>
    event EventHandler<MessageStatusChangedEventArgs>? MessageStatusChanged;

    /// <summary>
    /// Event raised when connection state changes
    /// </summary>
    event EventHandler<ConnectionStateChangedEventArgs>? ConnectionStateChanged;

    /// <summary>
    /// Gets the current registration state
    /// </summary>
    RegistrationState RegistrationState { get; }

    /// <summary>
    /// Gets the current connection state
    /// </summary>
    ConnectionState ConnectionState { get; }

    /// <summary>
    /// Gets the SIP configuration
    /// </summary>
    SipConfiguration Configuration { get; }

    /// <summary>
    /// Gets whether the client is currently running
    /// </summary>
    bool IsRunning { get; }

    /// <summary>
    /// Starts the SIP client and begins registration
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task StartAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Stops the SIP client and unregisters
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task StopAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Sends a SIP MESSAGE to the specified extension
    /// </summary>
    /// <param name="toExtension">Target extension</param>
    /// <param name="content">Message content</param>
    /// <param name="contentType">Content type (default: text/plain)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task with the message ID for tracking</returns>
    Task<string> SendMessageAsync(
        string toExtension,
        string content,
        string contentType = "text/plain",
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the status of a specific message
    /// </summary>
    /// <param name="messageId">Message ID</param>
    /// <returns>Message status or null if not found</returns>
    SipMessage? GetMessageStatus(string messageId);

    /// <summary>
    /// Gets all messages with optional filtering
    /// </summary>
    /// <param name="status">Optional status filter</param>
    /// <param name="fromDate">Optional from date filter</param>
    /// <param name="toDate">Optional to date filter</param>
    /// <returns>List of messages</returns>
    IReadOnlyList<SipMessage> GetMessages(
        MessageStatus? status = null,
        DateTime? fromDate = null,
        DateTime? toDate = null);

    /// <summary>
    /// Gets message statistics
    /// </summary>
    /// <returns>Message statistics</returns>
    MessageStatistics GetStatistics();

    /// <summary>
    /// Forces a re-registration attempt
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task ForceReregisterAsync(CancellationToken cancellationToken = default);
}
