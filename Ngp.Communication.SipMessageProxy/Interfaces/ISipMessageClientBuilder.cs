using Microsoft.Extensions.Logging;
using Ngp.Communication.SipMessageProxy.Enums;
using Ngp.Communication.SipMessageProxy.Models;

namespace Ngp.Communication.SipMessageProxy.Interfaces;

/// <summary>
/// Interface for building SIP message client instances
/// </summary>
public interface ISipMessageClientBuilder
{
    /// <summary>
    /// Configures the SIP server connection
    /// </summary>
    /// <param name="host">Server hostname or IP address</param>
    /// <param name="port">Server port (default: 5060)</param>
    /// <returns>Builder instance for method chaining</returns>
    ISipMessageClientBuilder WithServer(string host, ushort port = 5060);

    /// <summary>
    /// Configures the SIP credentials
    /// </summary>
    /// <param name="username">SIP username/extension</param>
    /// <param name="password">SIP password</param>
    /// <param name="domain">SIP domain</param>
    /// <returns>Builder instance for method chaining</returns>
    ISipMessageClientBuilder WithCredentials(string username, string password, string domain);

    /// <summary>
    /// Configures the transport protocol
    /// </summary>
    /// <param name="transport">Transport protocol</param>
    /// <returns>Builder instance for method chaining</returns>
    ISipMessageClientBuilder WithTransport(SipTransport transport);

    /// <summary>
    /// Configures the local port for SIP communication
    /// </summary>
    /// <param name="port">Local port (0 = auto)</param>
    /// <returns>Builder instance for method chaining</returns>
    ISipMessageClientBuilder WithLocalPort(ushort port);

    /// <summary>
    /// Configures the registration expiry time
    /// </summary>
    /// <param name="expiry">Expiry time in seconds</param>
    /// <returns>Builder instance for method chaining</returns>
    ISipMessageClientBuilder WithRegistrationExpiry(uint expiry);

    /// <summary>
    /// Configures the User-Agent string
    /// </summary>
    /// <param name="userAgent">User-Agent string</param>
    /// <returns>Builder instance for method chaining</returns>
    ISipMessageClientBuilder WithUserAgent(string userAgent);

    /// <summary>
    /// Configures the retry policy
    /// </summary>
    /// <param name="maxRetries">Maximum number of retries</param>
    /// <param name="retryDelay">Delay between retries</param>
    /// <returns>Builder instance for method chaining</returns>
    ISipMessageClientBuilder WithRetryPolicy(int maxRetries, TimeSpan retryDelay);

    /// <summary>
    /// Configures the retry policy
    /// </summary>
    /// <param name="retryPolicy">Retry policy configuration</param>
    /// <returns>Builder instance for method chaining</returns>
    ISipMessageClientBuilder WithRetryPolicy(RetryPolicy retryPolicy);

    /// <summary>
    /// Configures the request timeout
    /// </summary>
    /// <param name="timeout">Request timeout</param>
    /// <returns>Builder instance for method chaining</returns>
    ISipMessageClientBuilder WithRequestTimeout(TimeSpan timeout);

    /// <summary>
    /// Configures whether to automatically re-register
    /// </summary>
    /// <param name="autoReregister">Whether to auto re-register</param>
    /// <returns>Builder instance for method chaining</returns>
    ISipMessageClientBuilder WithAutoReregister(bool autoReregister);

    /// <summary>
    /// Configures the registration refresh interval
    /// </summary>
    /// <param name="refreshInterval">Registration refresh interval in seconds (0 = auto-calculate as 60% of expiry)</param>
    /// <returns>Builder instance for method chaining</returns>
    ISipMessageClientBuilder WithRegistrationRefreshInterval(uint refreshInterval);

    /// <summary>
    /// Configures connection timeout and retry settings
    /// </summary>
    /// <param name="connectionTimeout">Timeout for initial connection attempts</param>
    /// <param name="retryInterval">Interval between connection retry attempts</param>
    /// <returns>Builder instance for method chaining</returns>
    ISipMessageClientBuilder WithConnectionSettings(TimeSpan connectionTimeout, TimeSpan retryInterval);

    /// <summary>
    /// Configures the logger
    /// </summary>
    /// <param name="logger">Logger instance</param>
    /// <returns>Builder instance for method chaining</returns>
    ISipMessageClientBuilder WithLogger(ILogger logger);

    /// <summary>
    /// Builds the SIP message client instance
    /// </summary>
    /// <returns>Configured SIP message client</returns>
    ISipMessageClient Build();
}
