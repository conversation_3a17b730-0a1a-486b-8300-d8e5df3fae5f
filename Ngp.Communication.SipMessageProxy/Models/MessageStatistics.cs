namespace Ngp.Communication.SipMessageProxy.Models;

/// <summary>
/// Message statistics
/// </summary>
public class MessageStatistics
{
    /// <summary>
    /// Gets or sets the total number of messages sent
    /// </summary>
    public int TotalSent { get; set; }

    /// <summary>
    /// Gets or sets the number of messages delivered successfully
    /// </summary>
    public int Delivered { get; set; }

    /// <summary>
    /// Gets or sets the number of messages that failed
    /// </summary>
    public int Failed { get; set; }

    /// <summary>
    /// Gets or sets the number of messages currently pending
    /// </summary>
    public int Pending { get; set; }

    /// <summary>
    /// Gets or sets the number of messages that timed out
    /// </summary>
    public int Timeout { get; set; }

    /// <summary>
    /// Gets or sets the number of messages that were rejected
    /// </summary>
    public int Rejected { get; set; }

    /// <summary>
    /// Gets or sets the average delivery time in milliseconds
    /// </summary>
    public double AverageDeliveryTimeMs { get; set; }

    /// <summary>
    /// Gets or sets the success rate as a percentage (0-100)
    /// </summary>
    public double SuccessRate
    {
        get
        {
            if (TotalSent == 0) return 0;
            return (double)Delivered / TotalSent * 100;
        }
    }

    /// <summary>
    /// Gets or sets the failure rate as a percentage (0-100)
    /// </summary>
    public double FailureRate
    {
        get
        {
            if (TotalSent == 0) return 0;
            return (double)Failed / TotalSent * 100;
        }
    }

    /// <summary>
    /// Gets or sets when the statistics were last updated
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}
