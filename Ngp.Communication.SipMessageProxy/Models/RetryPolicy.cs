namespace Ngp.Communication.SipMessageProxy.Models;

/// <summary>
/// Retry policy configuration
/// </summary>
public class RetryPolicy
{
    /// <summary>
    /// Gets or sets the maximum number of retry attempts
    /// </summary>
    public int MaxRetries { get; set; } = 3;

    /// <summary>
    /// Gets or sets the initial retry delay
    /// </summary>
    public TimeSpan InitialDelay { get; set; } = TimeSpan.FromSeconds(1);

    /// <summary>
    /// Gets or sets the maximum retry delay
    /// </summary>
    public TimeSpan MaxDelay { get; set; } = TimeSpan.FromSeconds(30);

    /// <summary>
    /// Gets or sets the backoff multiplier (for exponential backoff)
    /// </summary>
    public double BackoffMultiplier { get; set; } = 2.0;

    /// <summary>
    /// Gets or sets whether to use exponential backoff
    /// </summary>
    public bool UseExponentialBackoff { get; set; } = true;

    /// <summary>
    /// Gets or sets whether to add jitter to retry delays
    /// </summary>
    public bool UseJitter { get; set; } = true;

    /// <summary>
    /// Calculates the delay for a specific retry attempt
    /// </summary>
    /// <param name="attemptNumber">The retry attempt number (0-based)</param>
    /// <returns>The delay to wait before the retry</returns>
    public TimeSpan GetDelay(int attemptNumber)
    {
        if (attemptNumber < 0)
            return TimeSpan.Zero;

        TimeSpan delay;

        if (UseExponentialBackoff)
        {
            var exponentialDelay = TimeSpan.FromMilliseconds(
                InitialDelay.TotalMilliseconds * Math.Pow(BackoffMultiplier, attemptNumber));
            delay = exponentialDelay > MaxDelay ? MaxDelay : exponentialDelay;
        }
        else
        {
            delay = InitialDelay;
        }

        if (UseJitter)
        {
            var random = new Random();
            var jitterMs = random.Next(0, (int)(delay.TotalMilliseconds * 0.1));
            delay = delay.Add(TimeSpan.FromMilliseconds(jitterMs));
        }

        return delay;
    }

    /// <summary>
    /// Creates a default retry policy
    /// </summary>
    /// <returns>Default retry policy</returns>
    public static RetryPolicy Default => new();

    /// <summary>
    /// Creates a retry policy with no retries
    /// </summary>
    /// <returns>No retry policy</returns>
    public static RetryPolicy None => new() { MaxRetries = 0 };
}
