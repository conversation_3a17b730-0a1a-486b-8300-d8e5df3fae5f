using Ngp.Communication.SipMessageProxy.Enums;

namespace Ngp.Communication.SipMessageProxy.Models;

/// <summary>
/// SIP client configuration
/// </summary>
public class SipConfiguration
{
    /// <summary>
    /// Gets or sets the SIP server hostname or IP address
    /// </summary>
    public string ServerHost { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the SIP server port (default: 5060)
    /// </summary>
    public ushort ServerPort { get; set; } = 5060;

    /// <summary>
    /// Gets or sets the SIP domain
    /// </summary>
    public string Domain { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the SIP username/extension
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the SIP password
    /// </summary>
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the local port for SIP communication (0 = auto)
    /// </summary>
    public ushort LocalPort { get; set; } = 0;

    /// <summary>
    /// Gets or sets the transport protocol
    /// </summary>
    public SipTransport Transport { get; set; } = SipTransport.Udp;

    /// <summary>
    /// Gets or sets the registration expiry time in seconds (default: 300)
    /// </summary>
    public uint RegistrationExpiry { get; set; } = 300;

    /// <summary>
    /// Gets or sets the User-Agent string
    /// </summary>
    public string UserAgent { get; set; } = "Ngp.Communication.SipMessageProxy/1.0";

    /// <summary>
    /// Gets or sets the maximum number of retry attempts
    /// </summary>
    public int MaxRetries { get; set; } = 3;

    /// <summary>
    /// Gets or sets the retry delay
    /// </summary>
    public TimeSpan RetryDelay { get; set; } = TimeSpan.FromSeconds(5);

    /// <summary>
    /// Gets or sets the request timeout
    /// </summary>
    public TimeSpan RequestTimeout { get; set; } = TimeSpan.FromSeconds(30);

    /// <summary>
    /// Gets or sets whether to automatically re-register when registration expires
    /// </summary>
    public bool AutoReregister { get; set; } = true;

    /// <summary>
    /// Gets or sets the registration refresh interval in seconds (default: 60% of RegistrationExpiry)
    /// This determines how often to refresh the registration before it expires
    /// </summary>
    public uint RegistrationRefreshInterval { get; set; } = 0; // 0 means auto-calculate as 60% of RegistrationExpiry

    /// <summary>
    /// Gets or sets the connection timeout for initial connection attempts
    /// </summary>
    public TimeSpan ConnectionTimeout { get; set; } = TimeSpan.FromSeconds(30);

    /// <summary>
    /// Gets or sets the retry interval for connection attempts when connection fails
    /// </summary>
    public TimeSpan ConnectionRetryInterval { get; set; } = TimeSpan.FromSeconds(10);

    /// <summary>
    /// Gets the effective registration refresh interval
    /// </summary>
    public uint EffectiveRegistrationRefreshInterval =>
        RegistrationRefreshInterval > 0 ? RegistrationRefreshInterval : (uint)(RegistrationExpiry * 0.6);

    /// <summary>
    /// Validates the configuration
    /// </summary>
    /// <returns>True if configuration is valid</returns>
    public bool IsValid()
    {
        return !string.IsNullOrWhiteSpace(ServerHost) &&
               ServerPort > 0 &&
               !string.IsNullOrWhiteSpace(Domain) &&
               !string.IsNullOrWhiteSpace(Username) &&
               !string.IsNullOrWhiteSpace(Password) &&
               RegistrationExpiry > 0 &&
               MaxRetries >= 0 &&
               RetryDelay >= TimeSpan.Zero &&
               RequestTimeout > TimeSpan.Zero &&
               ConnectionTimeout > TimeSpan.Zero &&
               ConnectionRetryInterval > TimeSpan.Zero;
    }
}
