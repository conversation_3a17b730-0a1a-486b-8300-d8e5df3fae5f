using Ngp.Communication.SipMessageProxy.Enums;

namespace Ngp.Communication.SipMessageProxy.Models;

/// <summary>
/// Represents a SIP message to be sent
/// </summary>
public class SipMessage
{
    /// <summary>
    /// Gets or sets the unique message identifier
    /// </summary>
    public string MessageId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// Gets or sets the target extension
    /// </summary>
    public string ToExtension { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the message content
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the content type (default: text/plain)
    /// </summary>
    public string ContentType { get; set; } = "text/plain";

    /// <summary>
    /// Gets or sets the current message status
    /// </summary>
    public MessageStatus Status { get; set; } = MessageStatus.Queued;

    /// <summary>
    /// Gets or sets when the message was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Gets or sets when the message was sent
    /// </summary>
    public DateTime? SentAt { get; set; }

    /// <summary>
    /// Gets or sets when the response was received
    /// </summary>
    public DateTime? ResponseAt { get; set; }

    /// <summary>
    /// Gets or sets the SIP response status code
    /// </summary>
    public ushort? ResponseStatusCode { get; set; }

    /// <summary>
    /// Gets or sets the SIP response reason phrase
    /// </summary>
    public string? ResponseReasonPhrase { get; set; }

    /// <summary>
    /// Gets or sets the error message if delivery failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Gets or sets the number of retry attempts made
    /// </summary>
    public int RetryCount { get; set; } = 0;

    /// <summary>
    /// Gets the duration from send to response in milliseconds
    /// </summary>
    public double? DurationMs
    {
        get
        {
            if (SentAt.HasValue && ResponseAt.HasValue)
            {
                return (ResponseAt.Value - SentAt.Value).TotalMilliseconds;
            }
            return null;
        }
    }

    /// <summary>
    /// Gets whether the message was delivered successfully
    /// </summary>
    public bool IsDelivered => Status == MessageStatus.Delivered;

    /// <summary>
    /// Gets whether the message delivery failed
    /// </summary>
    public bool IsFailed => Status == MessageStatus.Failed || Status == MessageStatus.Timeout || Status == MessageStatus.Rejected;
}
