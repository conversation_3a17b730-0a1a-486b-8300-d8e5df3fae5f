using Microsoft.Extensions.Logging;
using Ngp.Communication.SipMessageProxy.Enums;
using Ngp.Communication.SipMessageProxy.Events;
using Ngp.Communication.SipMessageProxy.Interfaces;
using Ngp.Communication.SipMessageProxy.Models;
using Ngp.Communication.SipMessageProxy.Services;

namespace Ngp.Communication.SipMessageProxy;

/// <summary>
/// Main SIP message client implementation
/// </summary>
public class SipMessageClient : ISipMessageClient
{
    private readonly SipConfiguration _configuration;
    private readonly ILogger _logger;
    private readonly SipRegistrationManager _registrationManager;
    private readonly SipMessageSender _messageSender;
    private readonly SemaphoreSlim _operationSemaphore;

    private bool _disposed;

    /// <inheritdoc />
    public event EventHandler<RegistrationStateChangedEventArgs>? RegistrationStateChanged;

    /// <inheritdoc />
    public event EventHandler<MessageStatusChangedEventArgs>? MessageStatusChanged;

    /// <inheritdoc />
    public event EventHandler<ConnectionStateChangedEventArgs>? ConnectionStateChanged;

    /// <inheritdoc />
    public RegistrationState RegistrationState => _registrationManager.CurrentState;

    /// <inheritdoc />
    public ConnectionState ConnectionState { get; private set; } = ConnectionState.Disconnected;

    /// <inheritdoc />
    public SipConfiguration Configuration => _configuration;

    /// <inheritdoc />
    public bool IsRunning => _registrationManager.IsRunning && _messageSender.IsRunning;

    /// <summary>
    /// Initializes a new instance of the SipMessageClient class
    /// </summary>
    /// <param name="configuration">SIP configuration</param>
    /// <param name="logger">Logger instance</param>
    internal SipMessageClient(SipConfiguration configuration, ILogger logger)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        _registrationManager = new SipRegistrationManager(configuration, logger);
        _messageSender = new SipMessageSender(configuration, logger);
        _operationSemaphore = new SemaphoreSlim(1, 1);

        // Subscribe to events
        _registrationManager.RegistrationStateChanged += OnRegistrationStateChanged;
        _messageSender.MessageStatusChanged += OnMessageStatusChanged;
    }

    /// <inheritdoc />
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(SipMessageClient));

        await _operationSemaphore.WaitAsync(cancellationToken);
        try
        {
            if (IsRunning)
            {
                _logger.LogWarning("SIP message client is already running");
                return;
            }

            _logger.LogInformation("Starting SIP message client for {Username}@{Domain}:{Port}",
                _configuration.Username, _configuration.Domain, _configuration.ServerPort);

            ChangeConnectionState(ConnectionState.Connecting);

            // Attempt connection with retry logic
            await StartWithRetryAsync(cancellationToken);

            _logger.LogInformation("SIP message client started successfully");
        }
        catch (Exception ex)
        {
            ChangeConnectionState(ConnectionState.Error, ex.Message);
            _logger.LogError(ex, "Failed to start SIP message client");
            throw;
        }
        finally
        {
            _operationSemaphore.Release();
        }
    }

    /// <summary>
    /// Starts the SIP client with retry logic
    /// </summary>
    private async Task StartWithRetryAsync(CancellationToken cancellationToken)
    {
        var maxRetries = _configuration.MaxRetries;
        var retryDelay = _configuration.ConnectionRetryInterval;
        Exception? lastException = null;

        for (int attempt = 0; attempt <= maxRetries; attempt++)
        {
            try
            {
                if (attempt > 0)
                {
                    _logger.LogInformation("Connection attempt {Attempt} of {MaxAttempts} after {RetryDelay}",
                        attempt + 1, maxRetries + 1, retryDelay);
                    await Task.Delay(retryDelay, cancellationToken);
                }

                // Start registration manager
                await _registrationManager.StartAsync(cancellationToken);

                // Wait for registration to complete or fail using configured connection timeout
                var registrationTimeout = _configuration.ConnectionTimeout;
                var startTime = DateTime.UtcNow;

                while (DateTime.UtcNow - startTime < registrationTimeout &&
                       _registrationManager.CurrentState == RegistrationState.Registering)
                {
                    await Task.Delay(100, cancellationToken);
                }

                if (_registrationManager.CurrentState != RegistrationState.Registered)
                {
                    var errorMessage = $"Failed to register with SIP server within {registrationTimeout.TotalSeconds} seconds";
                    throw new InvalidOperationException(errorMessage);
                }

                // Start message sender with the shared SIP transport from registration manager
                var sipTransport = _registrationManager.SipTransport;
                if (sipTransport == null)
                {
                    throw new InvalidOperationException("SIP transport not available from registration manager");
                }

                await _messageSender.StartAsync(sipTransport, _registrationManager.ActualLocalPort, cancellationToken);

                ChangeConnectionState(ConnectionState.Connected);
                return; // Success, exit retry loop
            }
            catch (Exception ex)
            {
                lastException = ex;
                _logger.LogWarning(ex, "Connection attempt {Attempt} failed: {Error}", attempt + 1, ex.Message);

                // Clean up failed attempt
                try
                {
                    await _registrationManager.StopAsync(cancellationToken);
                }
                catch (Exception cleanupEx)
                {
                    _logger.LogDebug(cleanupEx, "Error during cleanup of failed connection attempt");
                }

                // If this was the last attempt, don't wait
                if (attempt == maxRetries)
                    break;
            }
        }

        // All attempts failed
        var finalError = $"Failed to connect after {maxRetries + 1} attempts. Last error: {lastException?.Message}";
        throw new InvalidOperationException(finalError, lastException);
    }

    /// <inheritdoc />
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            return;

        await _operationSemaphore.WaitAsync(cancellationToken);
        try
        {
            if (!IsRunning)
            {
                _logger.LogWarning("SIP message client is not running");
                return;
            }

            _logger.LogInformation("Stopping SIP message client");

            ChangeConnectionState(ConnectionState.Disconnecting);

            // Stop message sender first
            await _messageSender.StopAsync(cancellationToken);

            // Stop registration manager (this will send unregister)
            await _registrationManager.StopAsync(cancellationToken);

            ChangeConnectionState(ConnectionState.Disconnected);

            _logger.LogInformation("SIP message client stopped successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping SIP message client");
            ChangeConnectionState(ConnectionState.Error, ex.Message);
        }
        finally
        {
            _operationSemaphore.Release();
        }
    }

    /// <inheritdoc />
    public async Task<string> SendMessageAsync(
        string toExtension,
        string content,
        string contentType = "text/plain",
        CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(SipMessageClient));

        if (!IsRunning)
            throw new InvalidOperationException("SIP message client is not running");

        if (_registrationManager.CurrentState != RegistrationState.Registered)
            throw new InvalidOperationException("SIP client is not registered");

        return await _messageSender.QueueMessageAsync(toExtension, content, contentType, cancellationToken);
    }

    /// <inheritdoc />
    public SipMessage? GetMessageStatus(string messageId)
    {
        return _messageSender.GetMessage(messageId);
    }

    /// <inheritdoc />
    public IReadOnlyList<SipMessage> GetMessages(
        MessageStatus? status = null,
        DateTime? fromDate = null,
        DateTime? toDate = null)
    {
        return _messageSender.GetMessages(status, fromDate, toDate);
    }

    /// <inheritdoc />
    public MessageStatistics GetStatistics()
    {
        return _messageSender.GetStatistics();
    }

    /// <inheritdoc />
    public async Task ForceReregisterAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(SipMessageClient));

        await _registrationManager.ForceReregisterAsync(cancellationToken);
    }

    /// <summary>
    /// Handles registration state changes
    /// </summary>
    private void OnRegistrationStateChanged(object? sender, RegistrationStateChangedEventArgs e)
    {
        _logger.LogInformation("Registration state changed: {PreviousState} -> {CurrentState}", 
            e.PreviousState, e.CurrentState);

        // Update connection state based on registration state
        switch (e.CurrentState)
        {
            case RegistrationState.Registered:
                if (ConnectionState == ConnectionState.Connecting)
                    ChangeConnectionState(ConnectionState.Connected);
                break;
            case RegistrationState.RegistrationFailed:
                ChangeConnectionState(ConnectionState.Error, e.ErrorMessage);
                break;
            case RegistrationState.NotRegistered when e.PreviousState == RegistrationState.Registered:
                ChangeConnectionState(ConnectionState.Disconnected);
                break;
        }

        RegistrationStateChanged?.Invoke(this, e);
    }

    /// <summary>
    /// Handles message status changes
    /// </summary>
    private void OnMessageStatusChanged(object? sender, MessageStatusChangedEventArgs e)
    {
        MessageStatusChanged?.Invoke(this, e);
    }

    /// <summary>
    /// Changes the connection state and raises the event
    /// </summary>
    /// <param name="newState">New connection state</param>
    /// <param name="errorMessage">Optional error message</param>
    private void ChangeConnectionState(ConnectionState newState, string? errorMessage = null)
    {
        var previousState = ConnectionState;
        ConnectionState = newState;

        _logger.LogDebug("Connection state changed: {PreviousState} -> {CurrentState}", previousState, newState);

        var eventArgs = new ConnectionStateChangedEventArgs(previousState, newState, errorMessage);
        ConnectionStateChanged?.Invoke(this, eventArgs);
    }

    /// <inheritdoc />
    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;

        try
        {
            StopAsync().GetAwaiter().GetResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during disposal");
        }

        _registrationManager?.Dispose();
        _messageSender?.Dispose();
        _operationSemaphore?.Dispose();
    }
}
