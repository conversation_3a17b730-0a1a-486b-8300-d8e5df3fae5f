using Microsoft.Extensions.Logging;
using Ngp.Communication.SipMessageProxy.Builders;
using Ngp.Communication.SipMessageProxy.Interfaces;

namespace Ngp.Communication.SipMessageProxy;

/// <summary>
/// Factory for creating SIP message client instances
/// </summary>
public static class SipMessageClientFactory
{
    /// <summary>
    /// Creates a new SIP message client builder
    /// </summary>
    /// <returns>New builder instance</returns>
    public static ISipMessageClientBuilder CreateBuilder()
    {
        return SipMessageClientBuilder.Create();
    }

    /// <summary>
    /// Creates a SIP message client with basic configuration
    /// </summary>
    /// <param name="serverHost">SIP server host</param>
    /// <param name="username">SIP username</param>
    /// <param name="password">SIP password</param>
    /// <param name="domain">SIP domain</param>
    /// <param name="logger">Optional logger</param>
    /// <returns>Configured SIP message client</returns>
    public static ISipMessageClient Create(
        string serverHost,
        string username,
        string password,
        string domain,
        ILogger? logger = null)
    {
        var builder = CreateBuilder()
            .WithServer(serverHost)
            .WithCredentials(username, password, domain);

        if (logger != null)
            builder.WithLogger(logger);

        return builder.Build();
    }

    /// <summary>
    /// Creates a SIP message client with full configuration
    /// </summary>
    /// <param name="serverHost">SIP server host</param>
    /// <param name="serverPort">SIP server port</param>
    /// <param name="username">SIP username</param>
    /// <param name="password">SIP password</param>
    /// <param name="domain">SIP domain</param>
    /// <param name="logger">Optional logger</param>
    /// <returns>Configured SIP message client</returns>
    public static ISipMessageClient Create(
        string serverHost,
        ushort serverPort,
        string username,
        string password,
        string domain,
        ILogger? logger = null)
    {
        var builder = CreateBuilder()
            .WithServer(serverHost, serverPort)
            .WithCredentials(username, password, domain);

        if (logger != null)
            builder.WithLogger(logger);

        return builder.Build();
    }
}
