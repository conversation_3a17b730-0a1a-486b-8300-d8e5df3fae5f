using Ngp.Calculation.LogicEngine.Enums;
using Ngp.Calculation.LogicEngine.Interfaces;

namespace Ngp.Calculation.LogicEngine.Models;

/// <summary>
/// Represents an input variable
/// </summary>
public class Input : IInput
{
    private object? _rawValue;

    /// <summary>
    /// Gets the input identifier
    /// </summary>
    public string Id { get; }

    /// <summary>
    /// Gets or sets the data type
    /// </summary>
    public DataType DataType { get; set; } = DataType.Double;

    /// <summary>
    /// Gets or sets the raw value
    /// </summary>
    public object? RawValue
    {
        get => _rawValue;
        set
        {
            _rawValue = value;
            LastUpdateTime = DateTime.UtcNow;
        }
    }

    /// <summary>
    /// Gets the scaled value (after applying scale and offset)
    /// </summary>
    public object? ScaledValue
    {
        get
        {
            if (RawValue == null)
                return DefaultValue;

            try
            {
                // Apply lookup table first if available
                if (LookupTable != null)
                {
                    return LookupTable.Lookup(RawValue);
                }

                // Convert to double for scaling
                var doubleValue = Convert.ToDouble(RawValue);
                var scaledValue = (doubleValue * Scale) + Offset;

                // Convert to target data type
                return DataType switch
                {
                    DataType.Boolean => Convert.ToBoolean(scaledValue),
                    DataType.Integer => Convert.ToInt32(scaledValue),
                    DataType.Long => Convert.ToInt64(scaledValue),
                    DataType.Float => Convert.ToSingle(scaledValue),
                    DataType.Double => scaledValue,
                    DataType.String => scaledValue.ToString(),
                    _ => scaledValue
                };
            }
            catch
            {
                return DefaultValue;
            }
        }
    }

    /// <summary>
    /// Gets or sets the scale factor
    /// </summary>
    public double Scale { get; set; } = 1.0;

    /// <summary>
    /// Gets or sets the offset
    /// </summary>
    public double Offset { get; set; } = 0.0;

    /// <summary>
    /// Gets or sets the lookup table
    /// </summary>
    public ILookupTable? LookupTable { get; set; }

    /// <summary>
    /// Gets or sets the description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Gets or sets the unit
    /// </summary>
    public string? Unit { get; set; }

    /// <summary>
    /// Gets or sets the minimum value
    /// </summary>
    public double? MinValue { get; set; }

    /// <summary>
    /// Gets or sets the maximum value
    /// </summary>
    public double? MaxValue { get; set; }

    /// <summary>
    /// Gets or sets the default value
    /// </summary>
    public object? DefaultValue { get; set; }

    /// <summary>
    /// Gets or sets whether the input is required
    /// </summary>
    public bool IsRequired { get; set; }

    /// <summary>
    /// Gets or sets the validation function
    /// </summary>
    public Func<object, bool>? Validator { get; set; }

    /// <summary>
    /// Gets or sets the debounce time in milliseconds
    /// </summary>
    public int DebounceTimeMs { get; set; } = 0;

    /// <summary>
    /// Gets or sets the tags
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// Gets or sets the metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// Gets the last update time
    /// </summary>
    public DateTime? LastUpdateTime { get; private set; }

    /// <summary>
    /// Initializes a new instance of the Input class
    /// </summary>
    /// <param name="id">Input identifier</param>
    public Input(string id)
    {
        if (string.IsNullOrWhiteSpace(id))
            throw new ArgumentException("Input ID cannot be null or empty", nameof(id));

        Id = id;
    }

    /// <summary>
    /// Validates the current value
    /// </summary>
    /// <returns>True if valid, false otherwise</returns>
    public bool IsValid()
    {
        if (IsRequired && RawValue == null)
            return false;

        if (Validator != null && RawValue != null)
            return Validator(RawValue);

        // Check min/max values for numeric types
        if (MinValue.HasValue || MaxValue.HasValue)
        {
            try
            {
                var numericValue = Convert.ToDouble(ScaledValue);
                if (MinValue.HasValue && numericValue < MinValue.Value)
                    return false;
                if (MaxValue.HasValue && numericValue > MaxValue.Value)
                    return false;
            }
            catch
            {
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// Returns a string representation of the input
    /// </summary>
    /// <returns>String representation</returns>
    public override string ToString()
    {
        return $"{Id}: {ScaledValue} ({DataType})";
    }
}
