using Ngp.Calculation.LogicEngine.Enums;
using Ngp.Calculation.LogicEngine.Interfaces;

namespace Ngp.Calculation.LogicEngine.Models;

/// <summary>
/// Information about a formula
/// </summary>
public class FormulaInfo : IFormulaInfo
{
    /// <summary>
    /// Gets the formula identifier
    /// </summary>
    public string Id { get; }

    /// <summary>
    /// Gets the formula expression
    /// </summary>
    public string Expression { get; }

    /// <summary>
    /// Gets the output data type
    /// </summary>
    public DataType OutputType { get; }

    /// <summary>
    /// Gets the description
    /// </summary>
    public string? Description { get; }

    /// <summary>
    /// Gets the unit
    /// </summary>
    public string? Unit { get; }

    /// <summary>
    /// Gets whether the formula is enabled
    /// </summary>
    public bool IsEnabled { get; }

    /// <summary>
    /// Gets the priority
    /// </summary>
    public int Priority { get; }

    /// <summary>
    /// Gets the last calculated value
    /// </summary>
    public object? LastValue { get; }

    /// <summary>
    /// Gets the last calculation time
    /// </summary>
    public DateTime? LastCalculationTime { get; }

    /// <summary>
    /// Gets the last execution time in milliseconds
    /// </summary>
    public double LastExecutionTimeMs { get; }

    /// <summary>
    /// Gets the calculation count
    /// </summary>
    public long CalculationCount { get; }

    /// <summary>
    /// Gets the error count
    /// </summary>
    public long ErrorCount { get; }

    /// <summary>
    /// Gets the last error message
    /// </summary>
    public string? LastError { get; }

    /// <summary>
    /// Gets the dependencies
    /// </summary>
    public IReadOnlyList<string> Dependencies { get; }

    /// <summary>
    /// Gets the tags
    /// </summary>
    public IReadOnlyList<string> Tags { get; }

    /// <summary>
    /// Gets the metadata
    /// </summary>
    public IReadOnlyDictionary<string, object> Metadata { get; }

    /// <summary>
    /// Initializes a new instance of the FormulaInfo class
    /// </summary>
    /// <param name="formula">Formula to get information from</param>
    public FormulaInfo(Formula formula)
    {
        if (formula == null)
            throw new ArgumentNullException(nameof(formula));

        Id = formula.Id;
        Expression = formula.Expression;
        OutputType = formula.OutputType;
        Description = formula.Description;
        Unit = formula.Unit;
        IsEnabled = formula.IsEnabled;
        Priority = formula.Priority;
        LastValue = formula.LastValue;
        LastCalculationTime = formula.LastCalculationTime;
        LastExecutionTimeMs = formula.LastExecutionTimeMs;
        CalculationCount = formula.CalculationCount;
        ErrorCount = formula.ErrorCount;
        LastError = formula.LastError;
        Dependencies = formula.Dependencies;
        Tags = formula.Tags;
        Metadata = formula.Metadata;
    }
}
