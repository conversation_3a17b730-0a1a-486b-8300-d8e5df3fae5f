using Ngp.Calculation.LogicEngine.Enums;
using Ngp.Calculation.LogicEngine.Interfaces;

namespace Ngp.Calculation.LogicEngine.Models;

/// <summary>
/// Represents a calculation formula
/// </summary>
public class Formula : IFormula
{
    /// <summary>
    /// Gets the formula identifier
    /// </summary>
    public string Id { get; }

    /// <summary>
    /// Gets or sets the mathematical expression
    /// </summary>
    public string Expression { get; set; }

    /// <summary>
    /// Gets or sets the output data type
    /// </summary>
    public DataType OutputType { get; set; } = DataType.Double;

    /// <summary>
    /// Gets or sets the description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Gets or sets the unit
    /// </summary>
    public string? Unit { get; set; }

    /// <summary>
    /// Gets or sets whether the formula is enabled
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// Gets or sets the priority
    /// </summary>
    public int Priority { get; set; } = 0;

    /// <summary>
    /// Gets or sets the timeout in milliseconds
    /// </summary>
    public int TimeoutMs { get; set; } = 5000;

    /// <summary>
    /// Gets or sets the retry count
    /// </summary>
    public int RetryCount { get; set; } = 0;

    /// <summary>
    /// Gets or sets whether to cache the result
    /// </summary>
    public bool CacheResult { get; set; } = true;

    /// <summary>
    /// Gets or sets the cache time-to-live in milliseconds
    /// </summary>
    public int CacheTtlMs { get; set; } = 60000;

    /// <summary>
    /// Gets or sets the tags
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// Gets or sets the metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// Gets or sets the dependencies
    /// </summary>
    public List<string> Dependencies { get; set; } = new();

    /// <summary>
    /// Gets or sets the error handling strategy
    /// </summary>
    public ErrorHandlingStrategy ErrorHandling { get; set; } = ErrorHandlingStrategy.ReturnDefault;

    /// <summary>
    /// Gets or sets the default value
    /// </summary>
    public object? DefaultValue { get; set; }

    /// <summary>
    /// Gets or sets the last calculated value
    /// </summary>
    public object? LastValue { get; set; }

    /// <summary>
    /// Gets or sets the calculation count
    /// </summary>
    public long CalculationCount { get; set; }

    /// <summary>
    /// Gets or sets the error count
    /// </summary>
    public long ErrorCount { get; set; }

    /// <summary>
    /// Gets or sets the last error message
    /// </summary>
    public string? LastError { get; set; }

    /// <summary>
    /// Gets or sets the last calculation time
    /// </summary>
    public DateTime? LastCalculationTime { get; set; }

    /// <summary>
    /// Gets or sets the last execution time in milliseconds
    /// </summary>
    public double LastExecutionTimeMs { get; set; }

    /// <summary>
    /// Initializes a new instance of the Formula class
    /// </summary>
    /// <param name="id">Formula identifier</param>
    /// <param name="expression">Mathematical expression</param>
    public Formula(string id, string expression)
    {
        if (string.IsNullOrWhiteSpace(id))
            throw new ArgumentException("Formula ID cannot be null or empty", nameof(id));
        if (string.IsNullOrWhiteSpace(expression))
            throw new ArgumentException("Expression cannot be null or empty", nameof(expression));

        Id = id;
        Expression = expression;
    }

    /// <summary>
    /// Updates the formula result
    /// </summary>
    /// <param name="value">New value</param>
    /// <param name="executionTimeMs">Execution time in milliseconds</param>
    public void UpdateResult(object? value, double executionTimeMs)
    {
        LastValue = value;
        LastExecutionTimeMs = executionTimeMs;
        LastCalculationTime = DateTime.UtcNow;
        CalculationCount++;
    }

    /// <summary>
    /// Records an error
    /// </summary>
    /// <param name="error">Error message</param>
    public void RecordError(string error)
    {
        LastError = error;
        ErrorCount++;
    }

    /// <summary>
    /// Returns a string representation of the formula
    /// </summary>
    /// <returns>String representation</returns>
    public override string ToString()
    {
        return $"{Id}: {Expression}";
    }
}
