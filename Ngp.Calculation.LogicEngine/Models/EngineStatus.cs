using Ngp.Calculation.LogicEngine.Enums;
using Ngp.Calculation.LogicEngine.Interfaces;

namespace Ngp.Calculation.LogicEngine.Models;

/// <summary>
/// Engine status information
/// </summary>
public class EngineStatus : IEngineStatus
{
    /// <summary>
    /// Gets or sets the current engine state
    /// </summary>
    public EngineState State { get; set; }

    /// <summary>
    /// Gets or sets the number of active formulas
    /// </summary>
    public int ActiveFormulas { get; set; }

    /// <summary>
    /// Gets or sets the number of registered inputs
    /// </summary>
    public int RegisteredInputs { get; set; }

    /// <summary>
    /// Gets or sets the error count
    /// </summary>
    public long ErrorCount { get; set; }

    /// <summary>
    /// Gets or sets the total calculation count
    /// </summary>
    public long TotalCalculations { get; set; }

    /// <summary>
    /// Gets or sets the last update time
    /// </summary>
    public DateTime LastUpdateTime { get; set; }

    /// <summary>
    /// Gets or sets the engine uptime
    /// </summary>
    public TimeSpan Uptime { get; set; }

    /// <summary>
    /// Gets or sets the calculations per second
    /// </summary>
    public double CalculationsPerSecond { get; set; }

    /// <summary>
    /// Gets or sets the memory usage in bytes
    /// </summary>
    public long MemoryUsageBytes { get; set; }

    /// <summary>
    /// Gets or sets the number of cached results
    /// </summary>
    public int CachedResults { get; set; }

    /// <summary>
    /// Gets or sets the cache hit ratio
    /// </summary>
    public double CacheHitRatio { get; set; }
}
