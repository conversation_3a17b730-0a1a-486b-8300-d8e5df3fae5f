using System.Text.RegularExpressions;

namespace Ngp.Calculation.LogicEngine.Security;

/// <summary>
/// Validates mathematical expressions for security and safety
/// </summary>
public static class ExpressionValidator
{
    /// <summary>
    /// Dangerous keywords that should not be allowed in expressions
    /// </summary>
    private static readonly string[] DangerousKeywords = 
    {
        "EXEC", "EXECUTE", "SP_", "XP_", "OPENROWSET", 
        "OPENDATASOURCE", "B<PERSON><PERSON><PERSON>", "SYSTEM", "<PERSON><PERSON><PERSON>",
        "C<PERSON>", "COMMAND", "EVAL", "SCRIPT", "IMPORT",
        "INCLUDE", "REQUIRE", "LOAD", "FILE", "READ",
        "WRITE", "DELETE", "DROP", "CREATE", "ALTER",
        "INSERT", "UPDATE", "SELECT", "FROM", "WHERE"
    };
    
    /// <summary>
    /// Pattern for allowed characters in expressions
    /// Allows: letters, numbers, underscore, basic math operators, parentheses, spaces, dots, commas
    /// </summary>
    private static readonly Regex AllowedPattern = new Regex(
        @"^[a-zA-Z0-9_+\-*/().,<>=\s&|!%^]+$", 
        RegexOptions.Compiled | RegexOptions.IgnoreCase
    );
    
    /// <summary>
    /// Pattern for valid function names
    /// </summary>
    private static readonly Regex FunctionPattern = new Regex(
        @"[A-Z]+\s*\(",
        RegexOptions.Compiled | RegexOptions.IgnoreCase
    );
    
    /// <summary>
    /// Allowed mathematical and utility functions
    /// </summary>
    private static readonly HashSet<string> AllowedFunctions = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
    {
        // Basic Math Functions
        "ABS", "SQRT", "CBRT", "POWER", "POW", "EXP", "LOG", "LOG10", "LOG2",
        "ROUND", "FLOOR", "CEILING", "CEIL", "TRUNCATE", "TRUNC",

        // Trigonometric Functions
        "SIN", "COS", "TAN", "ASIN", "ACOS", "ATAN", "ATAN2",
        "SINH", "COSH", "TANH", "ASINH", "ACOSH", "ATANH",

        // Statistical Functions
        "MIN", "MAX", "SIGN", "CLAMP",

        // Bitwise Functions
        "BITAND", "BITOR", "BITXOR", "BITNOT", "BITLSHIFT", "BITRSHIFT",
        "BITCOUNT", "BITSET", "BITCLEAR", "BITFLIP", "BITGET",

        // Type Conversion Functions
        "TOINT", "TOINT8", "TOINT16", "TOINT32", "TOINT64",
        "TOUINT", "TOUINT8", "TOUINT16", "TOUINT32", "TOUINT64",
        "TOFLOAT", "TODOUBLE", "TODECIMAL", "TOBOOL", "TOSTRING",
        "CONVERT", "CAST",

        // BitConverter Functions
        "TOBYTES", "FROMBYTES", "GETBYTES",
        "TOINT32FROMBYTES", "TOINT64FROMBYTES", "TOFLOATFROMBYTES", "TODOUBLFROMBYTES",

        // String Functions (limited safe ones)
        "LEN", "LENGTH", "SUBSTRING", "TRIM", "UPPER", "LOWER", "REPLACE",

        // Conditional Functions
        "IIF", "ISNULL", "COALESCE", "NULLIF",

        // Constants
        "PI", "E", "TAU"
    };
    
    /// <summary>
    /// Maximum allowed expression length
    /// </summary>
    public const int MaxExpressionLength = 1000;
    
    /// <summary>
    /// Maximum allowed nesting depth for parentheses
    /// </summary>
    public const int MaxNestingDepth = 10;
    
    /// <summary>
    /// Validates if an expression is safe to evaluate
    /// </summary>
    /// <param name="expression">Expression to validate</param>
    /// <returns>Validation result</returns>
    public static ExpressionValidationResult ValidateExpression(string expression)
    {
        if (string.IsNullOrWhiteSpace(expression))
        {
            return new ExpressionValidationResult(false, "Expression cannot be null or empty");
        }
        
        // Check length limit
        if (expression.Length > MaxExpressionLength)
        {
            return new ExpressionValidationResult(false, $"Expression exceeds maximum length of {MaxExpressionLength} characters");
        }
        
        // Check for dangerous keywords
        var upperExpression = expression.ToUpperInvariant();
        foreach (var keyword in DangerousKeywords)
        {
            if (upperExpression.Contains(keyword))
            {
                return new ExpressionValidationResult(false, $"Expression contains dangerous keyword: {keyword}");
            }
        }
        
        // Check allowed characters
        if (!AllowedPattern.IsMatch(expression))
        {
            return new ExpressionValidationResult(false, "Expression contains invalid characters");
        }
        
        // Check function names
        var functionMatches = FunctionPattern.Matches(expression);
        foreach (Match match in functionMatches)
        {
            var functionName = match.Value.Replace("(", "").Trim();
            if (!AllowedFunctions.Contains(functionName))
            {
                return new ExpressionValidationResult(false, $"Function '{functionName}' is not allowed");
            }
        }
        
        // Check parentheses balance and nesting depth
        var nestingResult = ValidateParenthesesNesting(expression);
        if (!nestingResult.IsValid)
        {
            return nestingResult;
        }
        
        // Check for potential infinite loops or resource exhaustion patterns
        var resourceResult = ValidateResourceUsage(expression);
        if (!resourceResult.IsValid)
        {
            return resourceResult;
        }
        
        return new ExpressionValidationResult(true, "Expression is valid");
    }
    
    /// <summary>
    /// Validates parentheses balance and nesting depth
    /// </summary>
    /// <param name="expression">Expression to validate</param>
    /// <returns>Validation result</returns>
    private static ExpressionValidationResult ValidateParenthesesNesting(string expression)
    {
        int depth = 0;
        int maxDepth = 0;
        
        foreach (char c in expression)
        {
            if (c == '(')
            {
                depth++;
                maxDepth = Math.Max(maxDepth, depth);
                
                if (maxDepth > MaxNestingDepth)
                {
                    return new ExpressionValidationResult(false, $"Expression exceeds maximum nesting depth of {MaxNestingDepth}");
                }
            }
            else if (c == ')')
            {
                depth--;
                
                if (depth < 0)
                {
                    return new ExpressionValidationResult(false, "Unbalanced parentheses: closing parenthesis without opening");
                }
            }
        }
        
        if (depth != 0)
        {
            return new ExpressionValidationResult(false, "Unbalanced parentheses: missing closing parenthesis");
        }
        
        return new ExpressionValidationResult(true, "Parentheses are balanced");
    }
    
    /// <summary>
    /// Validates potential resource exhaustion patterns
    /// </summary>
    /// <param name="expression">Expression to validate</param>
    /// <returns>Validation result</returns>
    private static ExpressionValidationResult ValidateResourceUsage(string expression)
    {
        var upperExpression = expression.ToUpperInvariant();
        
        // Check for potentially dangerous patterns
        var dangerousPatterns = new[]
        {
            @"POWER\s*\(\s*\d{4,}", // Large power operations
            @"POW\s*\(\s*\d{4,}",   // Large power operations
            @"\d{10,}",             // Very large numbers
            @"REPLICATE\s*\(",      // String replication
            @"REPEAT\s*\(",         // String repetition
        };
        
        foreach (var pattern in dangerousPatterns)
        {
            if (Regex.IsMatch(upperExpression, pattern))
            {
                return new ExpressionValidationResult(false, $"Expression contains potentially dangerous pattern: {pattern}");
            }
        }
        
        // Check for division by zero patterns
        if (Regex.IsMatch(expression, @"/\s*0\s*[^.]") || expression.Contains("/0"))
        {
            return new ExpressionValidationResult(false, "Expression contains potential division by zero");
        }
        
        return new ExpressionValidationResult(true, "No resource exhaustion patterns detected");
    }
    
    /// <summary>
    /// Sanitizes an expression by removing potentially dangerous elements
    /// </summary>
    /// <param name="expression">Expression to sanitize</param>
    /// <returns>Sanitized expression</returns>
    public static string SanitizeExpression(string expression)
    {
        if (string.IsNullOrWhiteSpace(expression))
            return string.Empty;
        
        // Remove multiple spaces
        expression = Regex.Replace(expression, @"\s+", " ");
        
        // Trim whitespace
        expression = expression.Trim();
        
        // Remove comments (if any)
        expression = Regex.Replace(expression, @"--.*$", "", RegexOptions.Multiline);
        expression = Regex.Replace(expression, @"/\*.*?\*/", "", RegexOptions.Singleline);
        
        return expression;
    }
}

/// <summary>
/// Result of expression validation
/// </summary>
public class ExpressionValidationResult
{
    /// <summary>
    /// Gets whether the expression is valid
    /// </summary>
    public bool IsValid { get; }
    
    /// <summary>
    /// Gets the validation message
    /// </summary>
    public string Message { get; }
    
    /// <summary>
    /// Initializes a new instance of the ExpressionValidationResult class
    /// </summary>
    /// <param name="isValid">Whether the expression is valid</param>
    /// <param name="message">Validation message</param>
    public ExpressionValidationResult(bool isValid, string message)
    {
        IsValid = isValid;
        Message = message ?? throw new ArgumentNullException(nameof(message));
    }
}
