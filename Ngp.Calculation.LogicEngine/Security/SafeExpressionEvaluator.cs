using Microsoft.Extensions.Logging;
using System.Data;

namespace Ngp.Calculation.LogicEngine.Security;

/// <summary>
/// Safe expression evaluator with timeout and validation
/// </summary>
public class SafeExpressionEvaluator
{
    private readonly int _timeoutMs;
    private readonly ILogger? _logger;
    private readonly Dictionary<string, Func<object[], object>> _customFunctions;

    /// <summary>
    /// Initializes a new instance of the SafeExpressionEvaluator class
    /// </summary>
    /// <param name="timeoutMs">Evaluation timeout in milliseconds</param>
    /// <param name="logger">Logger instance</param>
    public SafeExpressionEvaluator(int timeoutMs = 5000, ILogger? logger = null)
    {
        _timeoutMs = timeoutMs;
        _logger = logger;
        _customFunctions = InitializeCustomFunctions();
    }

    /// <summary>
    /// Evaluates an expression safely with validation and timeout
    /// </summary>
    /// <param name="expression">Expression to evaluate</param>
    /// <param name="variables">Variables to substitute in the expression</param>
    /// <returns>Evaluation result</returns>
    public object EvaluateExpression(string expression, Dictionary<string, object>? variables = null)
    {
        // Validate expression first
        var validationResult = ExpressionValidator.ValidateExpression(expression);
        if (!validationResult.IsValid)
        {
            throw new InvalidOperationException($"Expression validation failed: {validationResult.Message}");
        }

        // Sanitize expression
        expression = ExpressionValidator.SanitizeExpression(expression);

        // Replace variables
        if (variables != null)
        {
            expression = ReplaceVariables(expression, variables);
        }

        // Replace custom functions
        expression = ReplaceCustomFunctions(expression);

        try
        {
            // Evaluate with timeout
            using var cts = new CancellationTokenSource(_timeoutMs);
            
            var task = Task.Run(() =>
            {
                var table = new DataTable();
                return table.Compute(expression, null);
            }, cts.Token);

            if (!task.Wait(_timeoutMs))
            {
                throw new TimeoutException($"Expression evaluation timed out after {_timeoutMs}ms");
            }

            var result = task.Result;
            _logger?.LogDebug("Expression '{Expression}' evaluated to '{Result}'", expression, result);
            
            return result ?? 0.0;
        }
        catch (Exception ex) when (!(ex is TimeoutException))
        {
            _logger?.LogError(ex, "Error evaluating expression: {Expression}", expression);
            throw new InvalidOperationException($"Expression evaluation failed: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// Replaces variables in the expression with their values
    /// </summary>
    /// <param name="expression">Expression with variables</param>
    /// <param name="variables">Variable values</param>
    /// <returns>Expression with variables replaced</returns>
    private string ReplaceVariables(string expression, Dictionary<string, object> variables)
    {
        foreach (var variable in variables)
        {
            var value = ConvertToExpressionValue(variable.Value);
            expression = expression.Replace(variable.Key, value);
        }
        return expression;
    }

    /// <summary>
    /// Converts a value to a string representation suitable for expressions
    /// </summary>
    /// <param name="value">Value to convert</param>
    /// <returns>String representation</returns>
    private string ConvertToExpressionValue(object value)
    {
        return value switch
        {
            null => "0",
            bool b => b ? "1" : "0",
            string s => $"'{s.Replace("'", "''")}'",
            DateTime dt => $"'{dt:yyyy-MM-dd HH:mm:ss}'",
            _ => value.ToString() ?? "0"
        };
    }

    /// <summary>
    /// Replaces custom functions with their implementations
    /// </summary>
    /// <param name="expression">Expression with custom functions</param>
    /// <returns>Expression with custom functions replaced</returns>
    private string ReplaceCustomFunctions(string expression)
    {
        // For now, we'll implement basic replacements
        // In a full implementation, this would parse and replace function calls
        
        // Replace constants
        expression = expression.Replace("PI()", Math.PI.ToString());
        expression = expression.Replace("E()", Math.E.ToString());
        expression = expression.Replace("TAU()", (2 * Math.PI).ToString());
        
        // Replace some basic functions that DataTable.Compute doesn't support
        expression = ReplaceFunction(expression, "CBRT", args => Math.Pow(args[0], 1.0/3.0));
        expression = ReplaceFunction(expression, "LOG2", args => Math.Log2(args[0]));
        expression = ReplaceFunction(expression, "SIGN", args => Math.Sign(args[0]));
        expression = ReplaceFunction(expression, "TRUNC", args => Math.Truncate(args[0]));
        expression = ReplaceFunction(expression, "CEIL", args => Math.Ceiling(args[0]));
        
        return expression;
    }

    /// <summary>
    /// Replaces a specific function in the expression
    /// </summary>
    /// <param name="expression">Expression</param>
    /// <param name="functionName">Function name</param>
    /// <param name="implementation">Function implementation</param>
    /// <returns>Expression with function replaced</returns>
    private string ReplaceFunction(string expression, string functionName, Func<double[], double> implementation)
    {
        // Simple implementation - in production, use a proper expression parser
        var pattern = $@"{functionName}\s*\(\s*([^)]+)\s*\)";
        var regex = new System.Text.RegularExpressions.Regex(pattern, System.Text.RegularExpressions.RegexOptions.IgnoreCase);
        
        return regex.Replace(expression, match =>
        {
            var argsString = match.Groups[1].Value;
            var args = argsString.Split(',').Select(arg => 
            {
                if (double.TryParse(arg.Trim(), out var value))
                    return value;
                return 0.0;
            }).ToArray();
            
            var result = implementation(args);
            return result.ToString();
        });
    }

    /// <summary>
    /// Initializes custom function implementations
    /// </summary>
    /// <returns>Dictionary of custom functions</returns>
    private Dictionary<string, Func<object[], object>> InitializeCustomFunctions()
    {
        return new Dictionary<string, Func<object[], object>>(StringComparer.OrdinalIgnoreCase)
        {
            // Bitwise operations
            ["BITAND"] = args => Convert.ToInt64(args[0]) & Convert.ToInt64(args[1]),
            ["BITOR"] = args => Convert.ToInt64(args[0]) | Convert.ToInt64(args[1]),
            ["BITXOR"] = args => Convert.ToInt64(args[0]) ^ Convert.ToInt64(args[1]),
            ["BITNOT"] = args => ~Convert.ToInt64(args[0]),
            ["BITLSHIFT"] = args => Convert.ToInt64(args[0]) << Convert.ToInt32(args[1]),
            ["BITRSHIFT"] = args => Convert.ToInt64(args[0]) >> Convert.ToInt32(args[1]),
            
            // Type conversions
            ["TOINT"] = args => Convert.ToInt32(args[0]),
            ["TOINT8"] = args => Convert.ToSByte(args[0]),
            ["TOINT16"] = args => Convert.ToInt16(args[0]),
            ["TOINT32"] = args => Convert.ToInt32(args[0]),
            ["TOINT64"] = args => Convert.ToInt64(args[0]),
            ["TOUINT8"] = args => Convert.ToByte(args[0]),
            ["TOUINT16"] = args => Convert.ToUInt16(args[0]),
            ["TOUINT32"] = args => Convert.ToUInt32(args[0]),
            ["TOUINT64"] = args => Convert.ToUInt64(args[0]),
            ["TOFLOAT"] = args => Convert.ToSingle(args[0]),
            ["TODOUBLE"] = args => Convert.ToDouble(args[0]),
            ["TOBOOL"] = args => Convert.ToBoolean(args[0]),
            ["TOSTRING"] = args => args[0]?.ToString() ?? string.Empty,
            
            // BitConverter operations
            ["TOBYTES"] = args => BitConverter.GetBytes(Convert.ToDouble(args[0])),
            ["TOINT32FROMBYTES"] = args => 
            {
                var bytes = args[0] as byte[] ?? throw new ArgumentException("Expected byte array");
                return BitConverter.ToInt32(bytes, 0);
            },
            ["TOINT64FROMBYTES"] = args => 
            {
                var bytes = args[0] as byte[] ?? throw new ArgumentException("Expected byte array");
                return BitConverter.ToInt64(bytes, 0);
            },
            ["TOFLOATFROMBYTES"] = args => 
            {
                var bytes = args[0] as byte[] ?? throw new ArgumentException("Expected byte array");
                return BitConverter.ToSingle(bytes, 0);
            },
            ["TODOUBLFROMBYTES"] = args => 
            {
                var bytes = args[0] as byte[] ?? throw new ArgumentException("Expected byte array");
                return BitConverter.ToDouble(bytes, 0);
            },
            
            // Additional math functions
            ["CLAMP"] = args => 
            {
                var value = Convert.ToDouble(args[0]);
                var min = Convert.ToDouble(args[1]);
                var max = Convert.ToDouble(args[2]);
                return Math.Max(min, Math.Min(max, value));
            },
            
            // Bit manipulation
            ["BITCOUNT"] = args => 
            {
                var value = Convert.ToUInt64(args[0]);
                int count = 0;
                while (value != 0)
                {
                    count += (int)(value & 1);
                    value >>= 1;
                }
                return count;
            },
            
            ["BITSET"] = args => 
            {
                var value = Convert.ToInt64(args[0]);
                var position = Convert.ToInt32(args[1]);
                return value | (1L << position);
            },
            
            ["BITCLEAR"] = args => 
            {
                var value = Convert.ToInt64(args[0]);
                var position = Convert.ToInt32(args[1]);
                return value & ~(1L << position);
            },
            
            ["BITFLIP"] = args => 
            {
                var value = Convert.ToInt64(args[0]);
                var position = Convert.ToInt32(args[1]);
                return value ^ (1L << position);
            },
            
            ["BITGET"] = args => 
            {
                var value = Convert.ToInt64(args[0]);
                var position = Convert.ToInt32(args[1]);
                return (value >> position) & 1;
            }
        };
    }

    /// <summary>
    /// Gets information about available custom functions
    /// </summary>
    /// <returns>Dictionary of function categories and descriptions</returns>
    public Dictionary<string, List<(string Name, string Description)>> GetAvailableFunctions()
    {
        return new Dictionary<string, List<(string, string)>>
        {
            ["Bitwise Operations"] = new List<(string, string)>
            {
                ("BITAND(a, b)", "Bitwise AND operation"),
                ("BITOR(a, b)", "Bitwise OR operation"),
                ("BITXOR(a, b)", "Bitwise XOR operation"),
                ("BITNOT(a)", "Bitwise NOT operation"),
                ("BITLSHIFT(a, n)", "Left shift by n positions"),
                ("BITRSHIFT(a, n)", "Right shift by n positions"),
                ("BITCOUNT(a)", "Count number of set bits"),
                ("BITSET(a, pos)", "Set bit at position"),
                ("BITCLEAR(a, pos)", "Clear bit at position"),
                ("BITFLIP(a, pos)", "Flip bit at position"),
                ("BITGET(a, pos)", "Get bit at position")
            },
            ["Type Conversions"] = new List<(string, string)>
            {
                ("TOINT(a)", "Convert to 32-bit integer"),
                ("TOINT8(a)", "Convert to 8-bit signed integer"),
                ("TOINT16(a)", "Convert to 16-bit integer"),
                ("TOINT32(a)", "Convert to 32-bit integer"),
                ("TOINT64(a)", "Convert to 64-bit integer"),
                ("TOUINT8(a)", "Convert to 8-bit unsigned integer"),
                ("TOUINT16(a)", "Convert to 16-bit unsigned integer"),
                ("TOUINT32(a)", "Convert to 32-bit unsigned integer"),
                ("TOUINT64(a)", "Convert to 64-bit unsigned integer"),
                ("TOFLOAT(a)", "Convert to single-precision float"),
                ("TODOUBLE(a)", "Convert to double-precision float"),
                ("TOBOOL(a)", "Convert to boolean"),
                ("TOSTRING(a)", "Convert to string")
            },
            ["BitConverter"] = new List<(string, string)>
            {
                ("TOBYTES(a)", "Convert number to byte array"),
                ("TOINT32FROMBYTES(bytes)", "Convert byte array to 32-bit integer"),
                ("TOINT64FROMBYTES(bytes)", "Convert byte array to 64-bit integer"),
                ("TOFLOATFROMBYTES(bytes)", "Convert byte array to float"),
                ("TODOUBLFROMBYTES(bytes)", "Convert byte array to double")
            }
        };
    }
}
