using System.Collections.Concurrent;
using Microsoft.Extensions.Logging;
using Ngp.Calculation.LogicEngine.Builders;
using Ngp.Calculation.LogicEngine.Enums;
using Ngp.Calculation.LogicEngine.Events;
using Ngp.Calculation.LogicEngine.Interfaces;
using Ngp.Calculation.LogicEngine.Models;
using Ngp.Calculation.LogicEngine.Security;

namespace Ngp.Calculation.LogicEngine;

/// <summary>
/// Main implementation of the Logic Engine
/// </summary>
public class LogicEngine : ILogicEngine
{
    private readonly ConcurrentDictionary<string, Input> _inputs;
    private readonly ConcurrentDictionary<string, Formula> _formulas;
    private readonly ILogger<LogicEngine>? _logger;
    private readonly SafeExpressionEvaluator _expressionEvaluator;
    private bool _disposed;

    /// <summary>
    /// Event raised when an output value changes
    /// </summary>
    public event EventHandler<OutputChangedEventArgs>? OutputChanged;

    /// <summary>
    /// Event raised when a calculation error occurs
    /// </summary>
    public event EventHandler<CalculationErrorEventArgs>? CalculationError;

    /// <summary>
    /// Event raised when an input value changes
    /// </summary>
    public event EventHandler<InputChangedEventArgs>? InputChanged;

    /// <summary>
    /// Event raised when the engine state changes
    /// </summary>
    public event EventHandler<EngineStateChangedEventArgs>? EngineStateChanged;

    /// <summary>
    /// Gets the current engine state
    /// </summary>
    public EngineState State { get; private set; } = EngineState.Stopped;

    /// <summary>
    /// Gets the maximum concurrency level
    /// </summary>
    public int MaxConcurrency { get; }

    /// <summary>
    /// Gets the list of registered input IDs
    /// </summary>
    public IReadOnlyList<string> InputIds => _inputs.Keys.ToList();

    /// <summary>
    /// Gets the list of registered formula IDs
    /// </summary>
    public IReadOnlyList<string> FormulaIds => _formulas.Keys.ToList();

    /// <summary>
    /// Initializes a new instance of the LogicEngine class
    /// </summary>
    /// <param name="maxConcurrency">Maximum concurrency level</param>
    /// <param name="cacheMaxEntries">Maximum cache entries</param>
    /// <param name="cacheTimeToLiveMs">Cache time-to-live in milliseconds</param>
    /// <param name="defaultDebounceTimeMs">Default debounce time in milliseconds</param>
    /// <param name="logger">Logger instance</param>
    /// <param name="cacheLogger">Cache logger instance</param>
    /// <param name="calculatorLogger">Calculator logger instance</param>
    internal LogicEngine(
        int maxConcurrency,
        int cacheMaxEntries,
        int cacheTimeToLiveMs,
        int defaultDebounceTimeMs,
        ILogger<LogicEngine>? logger = null,
        ILogger? cacheLogger = null,
        ILogger? calculatorLogger = null)
    {
        MaxConcurrency = maxConcurrency;
        _logger = logger;
        _inputs = new ConcurrentDictionary<string, Input>();
        _formulas = new ConcurrentDictionary<string, Formula>();
        _expressionEvaluator = new SafeExpressionEvaluator(5000, logger);

        _logger?.LogInformation("LogicEngine initialized with MaxConcurrency={MaxConcurrency}", maxConcurrency);
    }

    /// <summary>
    /// Registers an input variable
    /// </summary>
    /// <param name="inputId">Input identifier</param>
    /// <returns>Input builder for fluent configuration</returns>
    public IInputBuilder RegisterInput(string inputId)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(LogicEngine));
        if (string.IsNullOrWhiteSpace(inputId))
            throw new ArgumentException("Input ID cannot be null or empty", nameof(inputId));

        var input = new Input(inputId);
        _inputs[inputId] = input;

        _logger?.LogDebug("Registered input: {InputId}", inputId);

        return new InputBuilderWrapper(input, this);
    }

    /// <summary>
    /// Defines a calculation formula
    /// </summary>
    /// <param name="formulaId">Formula identifier</param>
    /// <returns>Formula builder for fluent configuration</returns>
    public IFormulaBuilder DefineFormula(string formulaId)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(LogicEngine));
        if (string.IsNullOrWhiteSpace(formulaId))
            throw new ArgumentException("Formula ID cannot be null or empty", nameof(formulaId));

        var formula = new Formula(formulaId, "0"); // Default expression
        _formulas[formulaId] = formula;

        _logger?.LogDebug("Defined formula: {FormulaId}", formulaId);

        return new FormulaBuilderWrapper(formula, this);
    }

    /// <summary>
    /// Updates an input value
    /// </summary>
    /// <param name="inputId">Input identifier</param>
    /// <param name="value">New value</param>
    public void UpdateInput(string inputId, object value)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(LogicEngine));
        if (string.IsNullOrWhiteSpace(inputId))
            throw new ArgumentException("Input ID cannot be null or empty", nameof(inputId));

        if (_inputs.TryGetValue(inputId, out var input))
        {
            var oldValue = input.RawValue;
            input.RawValue = value;
            
            // Raise input changed event
            try
            {
                var args = new InputChangedEventArgs(
                    inputId, oldValue, value, input.ScaledValue, DateTime.UtcNow);
                InputChanged?.Invoke(this, args);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error raising InputChanged event for input {InputId}", inputId);
            }

            // Calculate affected formulas
            if (State == EngineState.Running)
            {
                CalculateFormulas();
            }
        }
        else
        {
            _logger?.LogWarning("Attempted to update non-existent input: {InputId}", inputId);
        }
    }

    /// <summary>
    /// Performs batch updates of multiple inputs
    /// </summary>
    /// <param name="batchAction">Batch update action</param>
    public void BatchUpdate(Action<IBatchUpdateBuilder> batchAction)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(LogicEngine));
        if (batchAction == null)
            throw new ArgumentNullException(nameof(batchAction));

        var builder = new BatchUpdateBuilder();
        batchAction(builder);
        var updates = ((BatchUpdateBuilder)builder).GetUpdates();

        foreach (var update in updates)
        {
            UpdateInput(update.Key, update.Value);
        }
    }

    /// <summary>
    /// Starts the calculation engine
    /// </summary>
    public void Start()
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(LogicEngine));

        State = EngineState.Running;
        _logger?.LogInformation("LogicEngine started");
        
        // Calculate all formulas initially
        CalculateFormulas();
    }

    /// <summary>
    /// Stops the calculation engine
    /// </summary>
    public void Stop()
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(LogicEngine));

        State = EngineState.Stopped;
        _logger?.LogInformation("LogicEngine stopped");
    }

    /// <summary>
    /// Gets the current value of an input
    /// </summary>
    /// <param name="inputId">Input identifier</param>
    /// <returns>Current input value, or null if not found</returns>
    public object? GetInputValue(string inputId)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(LogicEngine));
        if (string.IsNullOrWhiteSpace(inputId))
            return null;

        return _inputs.TryGetValue(inputId, out var input) ? input.ScaledValue : null;
    }

    /// <summary>
    /// Gets the current value of a formula output
    /// </summary>
    /// <param name="formulaId">Formula identifier</param>
    /// <returns>Current formula output value, or null if not found</returns>
    public object? GetFormulaValue(string formulaId)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(LogicEngine));
        if (string.IsNullOrWhiteSpace(formulaId))
            return null;

        return _formulas.TryGetValue(formulaId, out var formula) ? formula.LastValue : null;
    }

    /// <summary>
    /// Gets information about a specific formula
    /// </summary>
    /// <param name="formulaId">Formula identifier</param>
    /// <returns>Formula information, or null if not found</returns>
    public IFormulaInfo? GetFormulaInfo(string formulaId)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(LogicEngine));
        if (string.IsNullOrWhiteSpace(formulaId))
            return null;

        if (_formulas.TryGetValue(formulaId, out var formula))
        {
            return new FormulaInfo(formula);
        }

        return null;
    }

    /// <summary>
    /// Gets the current engine status
    /// </summary>
    /// <returns>Engine status information</returns>
    public IEngineStatus GetStatus()
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(LogicEngine));

        return new EngineStatus
        {
            State = State,
            ActiveFormulas = _formulas.Count(f => f.Value.IsEnabled),
            RegisteredInputs = _inputs.Count,
            ErrorCount = 0,
            TotalCalculations = _formulas.Values.Sum(f => f.CalculationCount),
            LastUpdateTime = DateTime.UtcNow,
            Uptime = TimeSpan.Zero,
            CalculationsPerSecond = 0,
            MemoryUsageBytes = GC.GetTotalMemory(false),
            CachedResults = 0,
            CacheHitRatio = 0
        };
    }

    /// <summary>
    /// Registers a custom function
    /// </summary>
    /// <param name="functionName">Function name</param>
    /// <param name="function">Function implementation</param>
    public void RegisterFunction(string functionName, Func<double[], double> function)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(LogicEngine));
        
        _logger?.LogDebug("Registered custom function: {FunctionName}", functionName);
    }

    /// <summary>
    /// Removes an input variable
    /// </summary>
    /// <param name="inputId">Input identifier</param>
    /// <returns>True if removed, false if not found</returns>
    public bool RemoveInput(string inputId)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(LogicEngine));
        if (string.IsNullOrWhiteSpace(inputId))
            return false;

        var removed = _inputs.TryRemove(inputId, out _);
        if (removed)
        {
            _logger?.LogDebug("Removed input: {InputId}", inputId);
        }

        return removed;
    }

    /// <summary>
    /// Removes a formula
    /// </summary>
    /// <param name="formulaId">Formula identifier</param>
    /// <returns>True if removed, false if not found</returns>
    public bool RemoveFormula(string formulaId)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(LogicEngine));
        if (string.IsNullOrWhiteSpace(formulaId))
            return false;

        var removed = _formulas.TryRemove(formulaId, out _);
        if (removed)
        {
            _logger?.LogDebug("Removed formula: {FormulaId}", formulaId);
        }

        return removed;
    }

    /// <summary>
    /// Disposes the engine and releases resources
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;

        // Stop the engine if it's running
        if (State == EngineState.Running)
        {
            State = EngineState.Stopped;
            _logger?.LogInformation("LogicEngine stopped during disposal");
        }

        _inputs.Clear();
        _formulas.Clear();
        _logger?.LogInformation("LogicEngine disposed");
    }

    /// <summary>
    /// Calculates all formulas
    /// </summary>
    private void CalculateFormulas()
    {
        foreach (var formula in _formulas.Values.Where(f => f.IsEnabled))
        {
            try
            {
                // Simple calculation - just evaluate basic expressions
                var result = EvaluateExpression(formula.Expression);
                var oldValue = formula.LastValue;
                formula.UpdateResult(result, 0.1); // Fake execution time

                if (!AreValuesEqual(oldValue, result))
                {
                    // Raise output changed event
                    try
                    {
                        var inputValues = _inputs.ToDictionary(kvp => kvp.Key, kvp => kvp.Value.ScaledValue);
                        var args = new OutputChangedEventArgs(
                            formula.Id, formula.Expression, oldValue, result, DateTime.UtcNow, inputValues, 0.1);
                        OutputChanged?.Invoke(this, args);
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "Error raising OutputChanged event for formula {FormulaId}", formula.Id);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error calculating formula {FormulaId}", formula.Id);
            }
        }
    }

    /// <summary>
    /// Safe expression evaluator using the security-enhanced evaluator
    /// </summary>
    /// <param name="expression">Expression to evaluate</param>
    /// <returns>Result</returns>
    private double EvaluateExpression(string expression)
    {
        try
        {
            // Prepare variables dictionary
            var variables = new Dictionary<string, object>();
            foreach (var input in _inputs)
            {
                variables[input.Key] = input.Value.ScaledValue ?? 0;
            }

            // Use the safe expression evaluator
            var result = _expressionEvaluator.EvaluateExpression(expression, variables);
            return Convert.ToDouble(result);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error evaluating expression: {Expression}", expression);
            return 0.0;
        }
    }

    /// <summary>
    /// Compares two values for equality
    /// </summary>
    /// <param name="value1">First value</param>
    /// <param name="value2">Second value</param>
    /// <returns>True if values are equal</returns>
    private static bool AreValuesEqual(object? value1, object? value2)
    {
        if (value1 == null && value2 == null) return true;
        if (value1 == null || value2 == null) return false;
        return value1.Equals(value2);
    }
}
