namespace Ngp.Calculation.LogicEngine.Enums;

/// <summary>
/// Supported data types for inputs and outputs
/// </summary>
public enum DataType
{
    /// <summary>
    /// Boolean value (true/false)
    /// </summary>
    Boolean,

    /// <summary>
    /// 32-bit integer
    /// </summary>
    Integer,

    /// <summary>
    /// 64-bit integer
    /// </summary>
    Long,

    /// <summary>
    /// Single-precision floating point
    /// </summary>
    Float,

    /// <summary>
    /// Double-precision floating point
    /// </summary>
    Double,

    /// <summary>
    /// String value
    /// </summary>
    String
}
