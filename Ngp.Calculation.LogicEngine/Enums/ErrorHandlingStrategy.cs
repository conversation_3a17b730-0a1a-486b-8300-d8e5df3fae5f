namespace Ngp.Calculation.LogicEngine.Enums;

/// <summary>
/// Error handling strategy for formulas
/// </summary>
public enum ErrorHandlingStrategy
{
    /// <summary>
    /// Return the default value when an error occurs
    /// </summary>
    ReturnDefault,

    /// <summary>
    /// Return the last known good value when an error occurs
    /// </summary>
    ReturnLastValue,

    /// <summary>
    /// Throw an exception when an error occurs
    /// </summary>
    ThrowException,

    /// <summary>
    /// Return null when an error occurs
    /// </summary>
    ReturnNull
}
