namespace Ngp.Calculation.LogicEngine.Enums;

/// <summary>
/// Engine state enumeration
/// </summary>
public enum EngineState
{
    /// <summary>
    /// Engine is stopped
    /// </summary>
    Stopped,

    /// <summary>
    /// Engine is starting
    /// </summary>
    Starting,

    /// <summary>
    /// Engine is running
    /// </summary>
    Running,

    /// <summary>
    /// Engine is stopping
    /// </summary>
    Stopping,

    /// <summary>
    /// Engine has encountered an error
    /// </summary>
    Error
}
