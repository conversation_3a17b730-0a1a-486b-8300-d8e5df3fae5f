using Ngp.Calculation.LogicEngine.Enums;
using Ngp.Calculation.LogicEngine.Events;

namespace Ngp.Calculation.LogicEngine.Interfaces;

/// <summary>
/// Main interface for the Logic Engine
/// </summary>
public interface ILogicEngine : IDisposable
{
    /// <summary>
    /// Event raised when an output value changes
    /// </summary>
    event EventHandler<OutputChangedEventArgs>? OutputChanged;

    /// <summary>
    /// Event raised when a calculation error occurs
    /// </summary>
    event EventHandler<CalculationErrorEventArgs>? CalculationError;

    /// <summary>
    /// Event raised when an input value changes
    /// </summary>
    event EventHandler<InputChangedEventArgs>? InputChanged;

    /// <summary>
    /// Event raised when the engine state changes
    /// </summary>
    event EventHandler<EngineStateChangedEventArgs>? EngineStateChanged;

    /// <summary>
    /// Gets the current engine state
    /// </summary>
    EngineState State { get; }

    /// <summary>
    /// Gets the maximum concurrency level
    /// </summary>
    int MaxConcurrency { get; }

    /// <summary>
    /// Gets the list of registered input IDs
    /// </summary>
    IReadOnlyList<string> InputIds { get; }

    /// <summary>
    /// Gets the list of registered formula IDs
    /// </summary>
    IReadOnlyList<string> FormulaIds { get; }

    /// <summary>
    /// Registers an input variable
    /// </summary>
    /// <param name="inputId">Input identifier</param>
    /// <returns>Input builder for fluent configuration</returns>
    IInputBuilder RegisterInput(string inputId);

    /// <summary>
    /// Defines a calculation formula
    /// </summary>
    /// <param name="formulaId">Formula identifier</param>
    /// <returns>Formula builder for fluent configuration</returns>
    IFormulaBuilder DefineFormula(string formulaId);

    /// <summary>
    /// Updates an input value
    /// </summary>
    /// <param name="inputId">Input identifier</param>
    /// <param name="value">New value</param>
    void UpdateInput(string inputId, object value);

    /// <summary>
    /// Performs batch updates of multiple inputs
    /// </summary>
    /// <param name="batchAction">Batch update action</param>
    void BatchUpdate(Action<IBatchUpdateBuilder> batchAction);

    /// <summary>
    /// Starts the calculation engine
    /// </summary>
    void Start();

    /// <summary>
    /// Stops the calculation engine
    /// </summary>
    void Stop();

    /// <summary>
    /// Gets the current value of an input
    /// </summary>
    /// <param name="inputId">Input identifier</param>
    /// <returns>Current input value, or null if not found</returns>
    object? GetInputValue(string inputId);

    /// <summary>
    /// Gets the current value of a formula output
    /// </summary>
    /// <param name="formulaId">Formula identifier</param>
    /// <returns>Current formula output value, or null if not found</returns>
    object? GetFormulaValue(string formulaId);

    /// <summary>
    /// Gets information about a specific formula
    /// </summary>
    /// <param name="formulaId">Formula identifier</param>
    /// <returns>Formula information, or null if not found</returns>
    IFormulaInfo? GetFormulaInfo(string formulaId);

    /// <summary>
    /// Gets the current engine status
    /// </summary>
    /// <returns>Engine status information</returns>
    IEngineStatus GetStatus();

    /// <summary>
    /// Registers a custom function
    /// </summary>
    /// <param name="functionName">Function name</param>
    /// <param name="function">Function implementation</param>
    void RegisterFunction(string functionName, Func<double[], double> function);

    /// <summary>
    /// Removes an input variable
    /// </summary>
    /// <param name="inputId">Input identifier</param>
    /// <returns>True if removed, false if not found</returns>
    bool RemoveInput(string inputId);

    /// <summary>
    /// Removes a formula
    /// </summary>
    /// <param name="formulaId">Formula identifier</param>
    /// <returns>True if removed, false if not found</returns>
    bool RemoveFormula(string formulaId);
}
