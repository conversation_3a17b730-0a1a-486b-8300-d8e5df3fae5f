using Ngp.Calculation.LogicEngine.Enums;

namespace Ngp.Calculation.LogicEngine.Interfaces;

/// <summary>
/// Builder interface for configuring formulas
/// </summary>
public interface IFormulaBuilder
{
    /// <summary>
    /// Sets the expression for the formula
    /// </summary>
    /// <param name="expression">Mathematical expression</param>
    /// <returns>This builder for fluent configuration</returns>
    IFormulaBuilder WithExpression(string expression);

    /// <summary>
    /// Sets the output data type for the formula
    /// </summary>
    /// <param name="dataType">Output data type</param>
    /// <returns>This builder for fluent configuration</returns>
    IFormulaBuilder WithOutputType(DataType dataType);

    /// <summary>
    /// Sets the description for the formula
    /// </summary>
    /// <param name="description">Description</param>
    /// <returns>This builder for fluent configuration</returns>
    IFormulaBuilder WithDescription(string description);

    /// <summary>
    /// Sets the unit for the formula output
    /// </summary>
    /// <param name="unit">Unit</param>
    /// <returns>This builder for fluent configuration</returns>
    IFormulaBuilder WithUnit(string unit);

    /// <summary>
    /// Sets the priority for the formula
    /// </summary>
    /// <param name="priority">Priority level</param>
    /// <returns>This builder for fluent configuration</returns>
    IFormulaBuilder WithPriority(int priority);

    /// <summary>
    /// Sets whether the formula is enabled
    /// </summary>
    /// <param name="enabled">True if enabled</param>
    /// <returns>This builder for fluent configuration</returns>
    IFormulaBuilder WithEnabled(bool enabled);

    /// <summary>
    /// Sets the timeout for formula calculation
    /// </summary>
    /// <param name="timeoutMs">Timeout in milliseconds</param>
    /// <returns>This builder for fluent configuration</returns>
    IFormulaBuilder WithTimeout(int timeoutMs);

    /// <summary>
    /// Sets the retry count for failed calculations
    /// </summary>
    /// <param name="retryCount">Number of retries</param>
    /// <returns>This builder for fluent configuration</returns>
    IFormulaBuilder WithRetryCount(int retryCount);

    /// <summary>
    /// Sets whether to cache the formula result
    /// </summary>
    /// <param name="cacheResult">True to cache result</param>
    /// <returns>This builder for fluent configuration</returns>
    IFormulaBuilder WithCacheResult(bool cacheResult);

    /// <summary>
    /// Sets the cache time-to-live for the formula result
    /// </summary>
    /// <param name="cacheTtlMs">Cache TTL in milliseconds</param>
    /// <returns>This builder for fluent configuration</returns>
    IFormulaBuilder WithCacheTtl(int cacheTtlMs);

    /// <summary>
    /// Sets tags for the formula
    /// </summary>
    /// <param name="tags">Tags</param>
    /// <returns>This builder for fluent configuration</returns>
    IFormulaBuilder WithTags(params string[] tags);

    /// <summary>
    /// Sets metadata for the formula
    /// </summary>
    /// <param name="metadata">Metadata dictionary</param>
    /// <returns>This builder for fluent configuration</returns>
    IFormulaBuilder WithMetadata(Dictionary<string, object> metadata);

    /// <summary>
    /// Sets dependencies for the formula
    /// </summary>
    /// <param name="dependencies">List of dependency IDs</param>
    /// <returns>This builder for fluent configuration</returns>
    IFormulaBuilder WithDependencies(params string[] dependencies);

    /// <summary>
    /// Sets the error handling strategy for the formula
    /// </summary>
    /// <param name="strategy">Error handling strategy</param>
    /// <returns>This builder for fluent configuration</returns>
    IFormulaBuilder WithErrorHandling(ErrorHandlingStrategy strategy);

    /// <summary>
    /// Sets the default value to use when calculation fails
    /// </summary>
    /// <param name="defaultValue">Default value</param>
    /// <returns>This builder for fluent configuration</returns>
    IFormulaBuilder WithDefaultValue(object defaultValue);

    /// <summary>
    /// Builds and returns the configured formula
    /// </summary>
    /// <returns>Configured formula</returns>
    IFormula Build();

    /// <summary>
    /// Completes the formula configuration and returns the engine for further configuration
    /// </summary>
    /// <returns>The logic engine for method chaining</returns>
    ILogicEngine Complete();
}
