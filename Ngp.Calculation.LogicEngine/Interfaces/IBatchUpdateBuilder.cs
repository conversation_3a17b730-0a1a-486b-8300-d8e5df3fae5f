namespace Ngp.Calculation.LogicEngine.Interfaces;

/// <summary>
/// Builder interface for batch updates
/// </summary>
public interface IBatchUpdateBuilder
{
    /// <summary>
    /// Sets an input value in the batch
    /// </summary>
    /// <param name="inputId">Input identifier</param>
    /// <param name="value">New value</param>
    /// <returns>This builder for fluent configuration</returns>
    IBatchUpdateBuilder SetInput(string inputId, object value);
}
