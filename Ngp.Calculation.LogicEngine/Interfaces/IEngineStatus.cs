using Ngp.Calculation.LogicEngine.Enums;

namespace Ngp.Calculation.LogicEngine.Interfaces;

/// <summary>
/// Interface for engine status information
/// </summary>
public interface IEngineStatus
{
    /// <summary>
    /// Gets the current engine state
    /// </summary>
    EngineState State { get; }

    /// <summary>
    /// Gets the number of active formulas
    /// </summary>
    int ActiveFormulas { get; }

    /// <summary>
    /// Gets the number of registered inputs
    /// </summary>
    int RegisteredInputs { get; }

    /// <summary>
    /// Gets the error count
    /// </summary>
    long ErrorCount { get; }

    /// <summary>
    /// Gets the total calculation count
    /// </summary>
    long TotalCalculations { get; }

    /// <summary>
    /// Gets the last update time
    /// </summary>
    DateTime LastUpdateTime { get; }

    /// <summary>
    /// Gets the engine uptime
    /// </summary>
    TimeSpan Uptime { get; }

    /// <summary>
    /// Gets the calculations per second
    /// </summary>
    double CalculationsPerSecond { get; }

    /// <summary>
    /// Gets the memory usage in bytes
    /// </summary>
    long MemoryUsageBytes { get; }

    /// <summary>
    /// Gets the number of cached results
    /// </summary>
    int CachedResults { get; }

    /// <summary>
    /// Gets the cache hit ratio
    /// </summary>
    double CacheHitRatio { get; }
}
