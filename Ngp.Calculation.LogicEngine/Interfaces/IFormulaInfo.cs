using Ngp.Calculation.LogicEngine.Enums;

namespace Ngp.Calculation.LogicEngine.Interfaces;

/// <summary>
/// Interface for formula information
/// </summary>
public interface IFormulaInfo
{
    /// <summary>
    /// Gets the formula identifier
    /// </summary>
    string Id { get; }

    /// <summary>
    /// Gets the formula expression
    /// </summary>
    string Expression { get; }

    /// <summary>
    /// Gets the output data type
    /// </summary>
    DataType OutputType { get; }

    /// <summary>
    /// Gets the description
    /// </summary>
    string? Description { get; }

    /// <summary>
    /// Gets the unit
    /// </summary>
    string? Unit { get; }

    /// <summary>
    /// Gets whether the formula is enabled
    /// </summary>
    bool IsEnabled { get; }

    /// <summary>
    /// Gets the priority
    /// </summary>
    int Priority { get; }

    /// <summary>
    /// Gets the last calculated value
    /// </summary>
    object? LastValue { get; }

    /// <summary>
    /// Gets the last calculation time
    /// </summary>
    DateTime? LastCalculationTime { get; }

    /// <summary>
    /// Gets the last execution time in milliseconds
    /// </summary>
    double LastExecutionTimeMs { get; }

    /// <summary>
    /// Gets the calculation count
    /// </summary>
    long CalculationCount { get; }

    /// <summary>
    /// Gets the error count
    /// </summary>
    long ErrorCount { get; }

    /// <summary>
    /// Gets the last error message
    /// </summary>
    string? LastError { get; }

    /// <summary>
    /// Gets the dependencies
    /// </summary>
    IReadOnlyList<string> Dependencies { get; }

    /// <summary>
    /// Gets the tags
    /// </summary>
    IReadOnlyList<string> Tags { get; }

    /// <summary>
    /// Gets the metadata
    /// </summary>
    IReadOnlyDictionary<string, object> Metadata { get; }
}
