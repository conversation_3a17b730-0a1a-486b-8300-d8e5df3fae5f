using Ngp.Calculation.LogicEngine.Enums;

namespace Ngp.Calculation.LogicEngine.Interfaces;

/// <summary>
/// Interface for calculation formulas
/// </summary>
public interface IFormula
{
    /// <summary>
    /// Gets the formula identifier
    /// </summary>
    string Id { get; }

    /// <summary>
    /// Gets or sets the mathematical expression
    /// </summary>
    string Expression { get; set; }

    /// <summary>
    /// Gets or sets the output data type
    /// </summary>
    DataType OutputType { get; set; }

    /// <summary>
    /// Gets or sets the description
    /// </summary>
    string? Description { get; set; }

    /// <summary>
    /// Gets or sets the unit
    /// </summary>
    string? Unit { get; set; }

    /// <summary>
    /// Gets or sets whether the formula is enabled
    /// </summary>
    bool IsEnabled { get; set; }

    /// <summary>
    /// Gets or sets the priority
    /// </summary>
    int Priority { get; set; }

    /// <summary>
    /// Gets or sets the timeout in milliseconds
    /// </summary>
    int TimeoutMs { get; set; }

    /// <summary>
    /// Gets or sets the retry count
    /// </summary>
    int RetryCount { get; set; }

    /// <summary>
    /// Gets or sets whether to cache the result
    /// </summary>
    bool CacheResult { get; set; }

    /// <summary>
    /// Gets or sets the cache time-to-live in milliseconds
    /// </summary>
    int CacheTtlMs { get; set; }

    /// <summary>
    /// Gets or sets the tags
    /// </summary>
    List<string> Tags { get; set; }

    /// <summary>
    /// Gets or sets the metadata
    /// </summary>
    Dictionary<string, object> Metadata { get; set; }

    /// <summary>
    /// Gets or sets the dependencies
    /// </summary>
    List<string> Dependencies { get; set; }

    /// <summary>
    /// Gets or sets the error handling strategy
    /// </summary>
    ErrorHandlingStrategy ErrorHandling { get; set; }

    /// <summary>
    /// Gets or sets the default value
    /// </summary>
    object? DefaultValue { get; set; }

    /// <summary>
    /// Gets or sets the last calculated value
    /// </summary>
    object? LastValue { get; set; }

    /// <summary>
    /// Gets or sets the calculation count
    /// </summary>
    long CalculationCount { get; set; }

    /// <summary>
    /// Gets or sets the error count
    /// </summary>
    long ErrorCount { get; set; }

    /// <summary>
    /// Gets or sets the last error message
    /// </summary>
    string? LastError { get; set; }

    /// <summary>
    /// Gets or sets the last calculation time
    /// </summary>
    DateTime? LastCalculationTime { get; set; }

    /// <summary>
    /// Gets or sets the last execution time in milliseconds
    /// </summary>
    double LastExecutionTimeMs { get; set; }
}
