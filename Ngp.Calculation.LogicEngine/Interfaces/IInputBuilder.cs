using Ngp.Calculation.LogicEngine.Enums;

namespace Ngp.Calculation.LogicEngine.Interfaces;

/// <summary>
/// Builder interface for configuring inputs
/// </summary>
public interface IInputBuilder
{
    /// <summary>
    /// Sets the initial value for the input
    /// </summary>
    /// <param name="value">Initial value</param>
    /// <returns>This builder for fluent configuration</returns>
    IInputBuilder WithInitialValue(object value);

    /// <summary>
    /// Sets the data type for the input
    /// </summary>
    /// <param name="dataType">Data type</param>
    /// <returns>This builder for fluent configuration</returns>
    IInputBuilder WithDataType(DataType dataType);

    /// <summary>
    /// Sets the scale factor for the input
    /// </summary>
    /// <param name="scale">Scale factor</param>
    /// <returns>This builder for fluent configuration</returns>
    IInputBuilder WithScale(double scale);

    /// <summary>
    /// Sets the offset for the input
    /// </summary>
    /// <param name="offset">Offset value</param>
    /// <returns>This builder for fluent configuration</returns>
    IInputBuilder WithOffset(double offset);

    /// <summary>
    /// Sets the lookup table for the input
    /// </summary>
    /// <param name="lookupTable">Lookup table</param>
    /// <returns>This builder for fluent configuration</returns>
    IInputBuilder WithLookupTable(ILookupTable lookupTable);

    /// <summary>
    /// Sets the description for the input
    /// </summary>
    /// <param name="description">Description</param>
    /// <returns>This builder for fluent configuration</returns>
    IInputBuilder WithDescription(string description);

    /// <summary>
    /// Sets the unit for the input
    /// </summary>
    /// <param name="unit">Unit</param>
    /// <returns>This builder for fluent configuration</returns>
    IInputBuilder WithUnit(string unit);

    /// <summary>
    /// Sets the minimum value for the input
    /// </summary>
    /// <param name="minValue">Minimum value</param>
    /// <returns>This builder for fluent configuration</returns>
    IInputBuilder WithMinValue(double minValue);

    /// <summary>
    /// Sets the maximum value for the input
    /// </summary>
    /// <param name="maxValue">Maximum value</param>
    /// <returns>This builder for fluent configuration</returns>
    IInputBuilder WithMaxValue(double maxValue);

    /// <summary>
    /// Sets the default value for the input
    /// </summary>
    /// <param name="defaultValue">Default value</param>
    /// <returns>This builder for fluent configuration</returns>
    IInputBuilder WithDefaultValue(object defaultValue);

    /// <summary>
    /// Sets whether the input is required
    /// </summary>
    /// <param name="required">True if required</param>
    /// <returns>This builder for fluent configuration</returns>
    IInputBuilder WithRequired(bool required);

    /// <summary>
    /// Sets the validation function for the input
    /// </summary>
    /// <param name="validator">Validation function</param>
    /// <returns>This builder for fluent configuration</returns>
    IInputBuilder WithValidator(Func<object, bool> validator);

    /// <summary>
    /// Sets the debounce time for the input
    /// </summary>
    /// <param name="debounceTimeMs">Debounce time in milliseconds</param>
    /// <returns>This builder for fluent configuration</returns>
    IInputBuilder WithDebounceTime(int debounceTimeMs);

    /// <summary>
    /// Sets tags for the input
    /// </summary>
    /// <param name="tags">Tags</param>
    /// <returns>This builder for fluent configuration</returns>
    IInputBuilder WithTags(params string[] tags);

    /// <summary>
    /// Sets metadata for the input
    /// </summary>
    /// <param name="metadata">Metadata dictionary</param>
    /// <returns>This builder for fluent configuration</returns>
    IInputBuilder WithMetadata(Dictionary<string, object> metadata);

    /// <summary>
    /// Builds and returns the configured input
    /// </summary>
    /// <returns>Configured input</returns>
    IInput Build();

    /// <summary>
    /// Completes the input configuration and returns the engine for further configuration
    /// </summary>
    /// <returns>The logic engine for method chaining</returns>
    ILogicEngine Complete();
}
