namespace Ngp.Calculation.LogicEngine.Interfaces;

/// <summary>
/// Interface for lookup tables
/// </summary>
public interface ILookupTable
{
    /// <summary>
    /// Performs a lookup operation
    /// </summary>
    /// <param name="input">Input value</param>
    /// <returns>Lookup result</returns>
    object? Lookup(object input);

    /// <summary>
    /// Gets the number of entries in the lookup table
    /// </summary>
    int Count { get; }

    /// <summary>
    /// Gets whether the lookup table is empty
    /// </summary>
    bool IsEmpty { get; }
}
