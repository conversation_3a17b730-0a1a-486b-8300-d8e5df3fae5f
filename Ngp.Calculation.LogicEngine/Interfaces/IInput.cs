using Ngp.Calculation.LogicEngine.Enums;

namespace Ngp.Calculation.LogicEngine.Interfaces;

/// <summary>
/// Interface for input variables
/// </summary>
public interface IInput
{
    /// <summary>
    /// Gets the input identifier
    /// </summary>
    string Id { get; }

    /// <summary>
    /// Gets or sets the data type
    /// </summary>
    DataType DataType { get; set; }

    /// <summary>
    /// Gets or sets the raw value
    /// </summary>
    object? RawValue { get; set; }

    /// <summary>
    /// Gets the scaled value (after applying scale and offset)
    /// </summary>
    object? ScaledValue { get; }

    /// <summary>
    /// Gets or sets the scale factor
    /// </summary>
    double Scale { get; set; }

    /// <summary>
    /// Gets or sets the offset
    /// </summary>
    double Offset { get; set; }

    /// <summary>
    /// Gets or sets the lookup table
    /// </summary>
    ILookupTable? LookupTable { get; set; }

    /// <summary>
    /// Gets or sets the description
    /// </summary>
    string? Description { get; set; }

    /// <summary>
    /// Gets or sets the unit
    /// </summary>
    string? Unit { get; set; }

    /// <summary>
    /// Gets or sets the minimum value
    /// </summary>
    double? MinValue { get; set; }

    /// <summary>
    /// Gets or sets the maximum value
    /// </summary>
    double? MaxValue { get; set; }

    /// <summary>
    /// Gets or sets the default value
    /// </summary>
    object? DefaultValue { get; set; }

    /// <summary>
    /// Gets or sets whether the input is required
    /// </summary>
    bool IsRequired { get; set; }

    /// <summary>
    /// Gets or sets the validation function
    /// </summary>
    Func<object, bool>? Validator { get; set; }

    /// <summary>
    /// Gets or sets the debounce time in milliseconds
    /// </summary>
    int DebounceTimeMs { get; set; }

    /// <summary>
    /// Gets or sets the tags
    /// </summary>
    List<string> Tags { get; set; }

    /// <summary>
    /// Gets or sets the metadata
    /// </summary>
    Dictionary<string, object> Metadata { get; set; }

    /// <summary>
    /// Gets the last update time
    /// </summary>
    DateTime? LastUpdateTime { get; }

    /// <summary>
    /// Validates the current value
    /// </summary>
    /// <returns>True if valid, false otherwise</returns>
    bool IsValid();
}
