using Microsoft.Extensions.Logging;

namespace Ngp.Calculation.LogicEngine;

/// <summary>
/// Factory for creating LogicEngine instances
/// </summary>
public static class LogicEngineFactory
{
    /// <summary>
    /// Creates a simple LogicEngine with default settings
    /// </summary>
    /// <returns>LogicEngine instance</returns>
    public static LogicEngine CreateSimple()
    {
        return new LogicEngine(
            maxConcurrency: Environment.ProcessorCount,
            cacheMaxEntries: 1000,
            cacheTimeToLiveMs: 60000,
            defaultDebounceTimeMs: 100);
    }

    /// <summary>
    /// Creates a LogicEngine with custom settings
    /// </summary>
    /// <param name="maxConcurrency">Maximum concurrency level</param>
    /// <param name="cacheMaxEntries">Maximum cache entries</param>
    /// <param name="cacheTimeToLiveMs">Cache time-to-live in milliseconds</param>
    /// <param name="defaultDebounceTimeMs">Default debounce time in milliseconds</param>
    /// <param name="logger">Logger instance</param>
    /// <returns>LogicEngine instance</returns>
    public static LogicEngine Create(
        int maxConcurrency = 0,
        int cacheMaxEntries = 1000,
        int cacheTimeToLiveMs = 60000,
        int defaultDebounceTimeMs = 100,
        ILogger<LogicEngine>? logger = null)
    {
        if (maxConcurrency <= 0)
            maxConcurrency = Environment.ProcessorCount;

        return new LogicEngine(
            maxConcurrency,
            cacheMaxEntries,
            cacheTimeToLiveMs,
            defaultDebounceTimeMs,
            logger);
    }

    /// <summary>
    /// Creates a high-performance LogicEngine optimized for speed
    /// </summary>
    /// <param name="logger">Logger instance</param>
    /// <returns>LogicEngine instance</returns>
    public static LogicEngine CreateHighPerformance(ILogger<LogicEngine>? logger = null)
    {
        return new LogicEngine(
            maxConcurrency: Environment.ProcessorCount * 2,
            cacheMaxEntries: 10000,
            cacheTimeToLiveMs: 300000, // 5 minutes
            defaultDebounceTimeMs: 10,
            logger);
    }

    /// <summary>
    /// Creates a memory-efficient LogicEngine optimized for low memory usage
    /// </summary>
    /// <param name="logger">Logger instance</param>
    /// <returns>LogicEngine instance</returns>
    public static LogicEngine CreateMemoryEfficient(ILogger<LogicEngine>? logger = null)
    {
        return new LogicEngine(
            maxConcurrency: 1,
            cacheMaxEntries: 100,
            cacheTimeToLiveMs: 30000, // 30 seconds
            defaultDebounceTimeMs: 500,
            logger);
    }
}
