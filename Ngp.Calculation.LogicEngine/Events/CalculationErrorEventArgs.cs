namespace Ngp.Calculation.LogicEngine.Events;

/// <summary>
/// Event arguments for calculation error events
/// </summary>
public class CalculationErrorEventArgs : EventArgs
{
    /// <summary>
    /// Gets the formula identifier
    /// </summary>
    public string FormulaId { get; }

    /// <summary>
    /// Gets the formula expression
    /// </summary>
    public string Expression { get; }

    /// <summary>
    /// Gets the error message
    /// </summary>
    public string ErrorMessage { get; }

    /// <summary>
    /// Gets the exception that occurred
    /// </summary>
    public Exception? Exception { get; }

    /// <summary>
    /// Gets the timestamp when the error occurred
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// Gets the input values used in the calculation
    /// </summary>
    public IReadOnlyDictionary<string, object?> InputValues { get; }

    /// <summary>
    /// Initializes a new instance of the CalculationErrorEventArgs class
    /// </summary>
    /// <param name="formulaId">Formula identifier</param>
    /// <param name="expression">Formula expression</param>
    /// <param name="errorMessage">Error message</param>
    /// <param name="exception">Exception that occurred</param>
    /// <param name="timestamp">Timestamp</param>
    /// <param name="inputValues">Input values</param>
    public CalculationErrorEventArgs(
        string formulaId,
        string expression,
        string errorMessage,
        Exception? exception,
        DateTime timestamp,
        IReadOnlyDictionary<string, object?> inputValues)
    {
        FormulaId = formulaId ?? throw new ArgumentNullException(nameof(formulaId));
        Expression = expression ?? throw new ArgumentNullException(nameof(expression));
        ErrorMessage = errorMessage ?? throw new ArgumentNullException(nameof(errorMessage));
        Exception = exception;
        Timestamp = timestamp;
        InputValues = inputValues ?? throw new ArgumentNullException(nameof(inputValues));
    }
}
