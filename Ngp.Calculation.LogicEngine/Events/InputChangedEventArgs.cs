namespace Ngp.Calculation.LogicEngine.Events;

/// <summary>
/// Event arguments for input changed events
/// </summary>
public class InputChangedEventArgs : EventArgs
{
    /// <summary>
    /// Gets the input identifier
    /// </summary>
    public string InputId { get; }

    /// <summary>
    /// Gets the old raw value
    /// </summary>
    public object? OldValue { get; }

    /// <summary>
    /// Gets the new raw value
    /// </summary>
    public object? NewValue { get; }

    /// <summary>
    /// Gets the scaled value (after applying scale and offset)
    /// </summary>
    public object? ScaledValue { get; }

    /// <summary>
    /// Gets the timestamp when the change occurred
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// Initializes a new instance of the InputChangedEventArgs class
    /// </summary>
    /// <param name="inputId">Input identifier</param>
    /// <param name="oldValue">Old raw value</param>
    /// <param name="newValue">New raw value</param>
    /// <param name="scaledValue">Scaled value</param>
    /// <param name="timestamp">Timestamp</param>
    public InputChangedEventArgs(
        string inputId,
        object? oldValue,
        object? newValue,
        object? scaledValue,
        DateTime timestamp)
    {
        InputId = inputId ?? throw new ArgumentNullException(nameof(inputId));
        OldValue = oldValue;
        NewValue = newValue;
        ScaledValue = scaledValue;
        Timestamp = timestamp;
    }
}
