namespace Ngp.Calculation.LogicEngine.Events;

/// <summary>
/// Event arguments for output changed events
/// </summary>
public class OutputChangedEventArgs : EventArgs
{
    /// <summary>
    /// Gets the formula identifier
    /// </summary>
    public string FormulaId { get; }

    /// <summary>
    /// Gets the formula expression
    /// </summary>
    public string Expression { get; }

    /// <summary>
    /// Gets the old value
    /// </summary>
    public object? OldValue { get; }

    /// <summary>
    /// Gets the new value
    /// </summary>
    public object? NewValue { get; }

    /// <summary>
    /// Gets the timestamp when the change occurred
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// Gets the input values used in the calculation
    /// </summary>
    public IReadOnlyDictionary<string, object?> InputValues { get; }

    /// <summary>
    /// Gets the execution time in milliseconds
    /// </summary>
    public double ExecutionTimeMs { get; }

    /// <summary>
    /// Initializes a new instance of the OutputChangedEventArgs class
    /// </summary>
    /// <param name="formulaId">Formula identifier</param>
    /// <param name="expression">Formula expression</param>
    /// <param name="oldValue">Old value</param>
    /// <param name="newValue">New value</param>
    /// <param name="timestamp">Timestamp</param>
    /// <param name="inputValues">Input values</param>
    /// <param name="executionTimeMs">Execution time in milliseconds</param>
    public OutputChangedEventArgs(
        string formulaId,
        string expression,
        object? oldValue,
        object? newValue,
        DateTime timestamp,
        IReadOnlyDictionary<string, object?> inputValues,
        double executionTimeMs)
    {
        FormulaId = formulaId ?? throw new ArgumentNullException(nameof(formulaId));
        Expression = expression ?? throw new ArgumentNullException(nameof(expression));
        OldValue = oldValue;
        NewValue = newValue;
        Timestamp = timestamp;
        InputValues = inputValues ?? throw new ArgumentNullException(nameof(inputValues));
        ExecutionTimeMs = executionTimeMs;
    }
}
