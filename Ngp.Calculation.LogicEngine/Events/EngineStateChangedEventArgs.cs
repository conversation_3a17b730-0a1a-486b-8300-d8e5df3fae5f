using Ngp.Calculation.LogicEngine.Enums;

namespace Ngp.Calculation.LogicEngine.Events;

/// <summary>
/// Event arguments for engine state changed events
/// </summary>
public class EngineStateChangedEventArgs : EventArgs
{
    /// <summary>
    /// Gets the old engine state
    /// </summary>
    public EngineState OldState { get; }

    /// <summary>
    /// Gets the new engine state
    /// </summary>
    public EngineState NewState { get; }

    /// <summary>
    /// Gets the timestamp when the state change occurred
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// Gets the reason for the state change
    /// </summary>
    public string? Reason { get; }

    /// <summary>
    /// Initializes a new instance of the EngineStateChangedEventArgs class
    /// </summary>
    /// <param name="oldState">Old engine state</param>
    /// <param name="newState">New engine state</param>
    /// <param name="timestamp">Timestamp</param>
    /// <param name="reason">Reason for the state change</param>
    public EngineStateChangedEventArgs(
        EngineState oldState,
        EngineState newState,
        DateTime timestamp,
        string? reason = null)
    {
        OldState = oldState;
        NewState = newState;
        Timestamp = timestamp;
        Reason = reason;
    }
}
