using Ngp.Calculation.LogicEngine.Interfaces;

namespace Ngp.Calculation.LogicEngine.Builders;

/// <summary>
/// Builder for batch updates
/// </summary>
internal class BatchUpdateBuilder : IBatchUpdateBuilder
{
    private readonly Dictionary<string, object> _updates = new();

    /// <summary>
    /// Sets an input value in the batch
    /// </summary>
    /// <param name="inputId">Input identifier</param>
    /// <param name="value">New value</param>
    /// <returns>This builder for fluent configuration</returns>
    public IBatchUpdateBuilder SetInput(string inputId, object value)
    {
        if (string.IsNullOrWhiteSpace(inputId))
            throw new ArgumentException("Input ID cannot be null or empty", nameof(inputId));

        _updates[inputId] = value;
        return this;
    }

    /// <summary>
    /// Gets the updates dictionary
    /// </summary>
    /// <returns>Dictionary of updates</returns>
    internal Dictionary<string, object> GetUpdates() => _updates;
}
