using Ngp.Calculation.LogicEngine.Enums;
using Ngp.Calculation.LogicEngine.Interfaces;
using Ngp.Calculation.LogicEngine.Models;

namespace Ngp.Calculation.LogicEngine.Builders;

/// <summary>
/// Wrapper for input builder that applies configuration to the input
/// </summary>
internal class InputBuilderWrapper : IInputBuilder
{
    private readonly Input _input;
    private readonly ILogicEngine _engine;

    /// <summary>
    /// Initializes a new instance of the InputBuilderWrapper class
    /// </summary>
    /// <param name="input">Input to configure</param>
    /// <param name="engine">Logic engine instance</param>
    public InputBuilderWrapper(Input input, ILogicEngine engine)
    {
        _input = input ?? throw new ArgumentNullException(nameof(input));
        _engine = engine ?? throw new ArgumentNullException(nameof(engine));
    }

    /// <summary>
    /// Sets the initial value for the input
    /// </summary>
    /// <param name="value">Initial value</param>
    /// <returns>This builder for fluent configuration</returns>
    public IInputBuilder WithInitialValue(object value)
    {
        _input.RawValue = value;
        return this;
    }

    /// <summary>
    /// Sets the data type for the input
    /// </summary>
    /// <param name="dataType">Data type</param>
    /// <returns>This builder for fluent configuration</returns>
    public IInputBuilder WithDataType(DataType dataType)
    {
        _input.DataType = dataType;
        return this;
    }

    /// <summary>
    /// Sets the scale factor for the input
    /// </summary>
    /// <param name="scale">Scale factor</param>
    /// <returns>This builder for fluent configuration</returns>
    public IInputBuilder WithScale(double scale)
    {
        _input.Scale = scale;
        return this;
    }

    /// <summary>
    /// Sets the offset for the input
    /// </summary>
    /// <param name="offset">Offset value</param>
    /// <returns>This builder for fluent configuration</returns>
    public IInputBuilder WithOffset(double offset)
    {
        _input.Offset = offset;
        return this;
    }

    /// <summary>
    /// Sets the lookup table for the input
    /// </summary>
    /// <param name="lookupTable">Lookup table</param>
    /// <returns>This builder for fluent configuration</returns>
    public IInputBuilder WithLookupTable(ILookupTable lookupTable)
    {
        _input.LookupTable = lookupTable;
        return this;
    }

    /// <summary>
    /// Sets the description for the input
    /// </summary>
    /// <param name="description">Description</param>
    /// <returns>This builder for fluent configuration</returns>
    public IInputBuilder WithDescription(string description)
    {
        _input.Description = description;
        return this;
    }

    /// <summary>
    /// Sets the unit for the input
    /// </summary>
    /// <param name="unit">Unit</param>
    /// <returns>This builder for fluent configuration</returns>
    public IInputBuilder WithUnit(string unit)
    {
        _input.Unit = unit;
        return this;
    }

    /// <summary>
    /// Sets the minimum value for the input
    /// </summary>
    /// <param name="minValue">Minimum value</param>
    /// <returns>This builder for fluent configuration</returns>
    public IInputBuilder WithMinValue(double minValue)
    {
        _input.MinValue = minValue;
        return this;
    }

    /// <summary>
    /// Sets the maximum value for the input
    /// </summary>
    /// <param name="maxValue">Maximum value</param>
    /// <returns>This builder for fluent configuration</returns>
    public IInputBuilder WithMaxValue(double maxValue)
    {
        _input.MaxValue = maxValue;
        return this;
    }

    /// <summary>
    /// Sets the default value for the input
    /// </summary>
    /// <param name="defaultValue">Default value</param>
    /// <returns>This builder for fluent configuration</returns>
    public IInputBuilder WithDefaultValue(object defaultValue)
    {
        _input.DefaultValue = defaultValue;
        return this;
    }

    /// <summary>
    /// Sets whether the input is required
    /// </summary>
    /// <param name="required">True if required</param>
    /// <returns>This builder for fluent configuration</returns>
    public IInputBuilder WithRequired(bool required)
    {
        _input.IsRequired = required;
        return this;
    }

    /// <summary>
    /// Sets the validation function for the input
    /// </summary>
    /// <param name="validator">Validation function</param>
    /// <returns>This builder for fluent configuration</returns>
    public IInputBuilder WithValidator(Func<object, bool> validator)
    {
        _input.Validator = validator;
        return this;
    }

    /// <summary>
    /// Sets the debounce time for the input
    /// </summary>
    /// <param name="debounceTimeMs">Debounce time in milliseconds</param>
    /// <returns>This builder for fluent configuration</returns>
    public IInputBuilder WithDebounceTime(int debounceTimeMs)
    {
        _input.DebounceTimeMs = debounceTimeMs;
        return this;
    }

    /// <summary>
    /// Sets tags for the input
    /// </summary>
    /// <param name="tags">Tags</param>
    /// <returns>This builder for fluent configuration</returns>
    public IInputBuilder WithTags(params string[] tags)
    {
        _input.Tags = tags?.ToList() ?? new List<string>();
        return this;
    }

    /// <summary>
    /// Sets metadata for the input
    /// </summary>
    /// <param name="metadata">Metadata dictionary</param>
    /// <returns>This builder for fluent configuration</returns>
    public IInputBuilder WithMetadata(Dictionary<string, object> metadata)
    {
        _input.Metadata = metadata ?? new Dictionary<string, object>();
        return this;
    }

    /// <summary>
    /// Builds and returns the configured input
    /// </summary>
    /// <returns>Configured input</returns>
    public IInput Build()
    {
        return _input;
    }

    /// <summary>
    /// Completes the input configuration and returns the engine for further configuration
    /// </summary>
    /// <returns>The logic engine for method chaining</returns>
    public ILogicEngine Complete()
    {
        return _engine;
    }
}
