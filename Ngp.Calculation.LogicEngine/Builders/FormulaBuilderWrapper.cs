using Ngp.Calculation.LogicEngine.Enums;
using Ngp.Calculation.LogicEngine.Interfaces;
using Ngp.Calculation.LogicEngine.Models;

namespace Ngp.Calculation.LogicEngine.Builders;

/// <summary>
/// Wrapper for formula builder that applies configuration to the formula
/// </summary>
internal class FormulaBuilderWrapper : IFormulaBuilder
{
    private readonly Formula _formula;
    private readonly ILogicEngine _engine;

    /// <summary>
    /// Initializes a new instance of the FormulaBuilderWrapper class
    /// </summary>
    /// <param name="formula">Formula to configure</param>
    /// <param name="engine">Logic engine instance</param>
    public FormulaBuilderWrapper(Formula formula, ILogicEngine engine)
    {
        _formula = formula ?? throw new ArgumentNullException(nameof(formula));
        _engine = engine ?? throw new ArgumentNullException(nameof(engine));
    }

    /// <summary>
    /// Sets the expression for the formula
    /// </summary>
    /// <param name="expression">Mathematical expression</param>
    /// <returns>This builder for fluent configuration</returns>
    public IFormulaBuilder WithExpression(string expression)
    {
        if (string.IsNullOrWhiteSpace(expression))
            throw new ArgumentException("Expression cannot be null or empty", nameof(expression));

        _formula.Expression = expression;
        return this;
    }

    /// <summary>
    /// Sets the output data type for the formula
    /// </summary>
    /// <param name="dataType">Output data type</param>
    /// <returns>This builder for fluent configuration</returns>
    public IFormulaBuilder WithOutputType(DataType dataType)
    {
        _formula.OutputType = dataType;
        return this;
    }

    /// <summary>
    /// Sets the description for the formula
    /// </summary>
    /// <param name="description">Description</param>
    /// <returns>This builder for fluent configuration</returns>
    public IFormulaBuilder WithDescription(string description)
    {
        _formula.Description = description;
        return this;
    }

    /// <summary>
    /// Sets the unit for the formula output
    /// </summary>
    /// <param name="unit">Unit</param>
    /// <returns>This builder for fluent configuration</returns>
    public IFormulaBuilder WithUnit(string unit)
    {
        _formula.Unit = unit;
        return this;
    }

    /// <summary>
    /// Sets the priority for the formula
    /// </summary>
    /// <param name="priority">Priority level</param>
    /// <returns>This builder for fluent configuration</returns>
    public IFormulaBuilder WithPriority(int priority)
    {
        _formula.Priority = priority;
        return this;
    }

    /// <summary>
    /// Sets whether the formula is enabled
    /// </summary>
    /// <param name="enabled">True if enabled</param>
    /// <returns>This builder for fluent configuration</returns>
    public IFormulaBuilder WithEnabled(bool enabled)
    {
        _formula.IsEnabled = enabled;
        return this;
    }

    /// <summary>
    /// Sets the timeout for formula calculation
    /// </summary>
    /// <param name="timeoutMs">Timeout in milliseconds</param>
    /// <returns>This builder for fluent configuration</returns>
    public IFormulaBuilder WithTimeout(int timeoutMs)
    {
        _formula.TimeoutMs = timeoutMs;
        return this;
    }

    /// <summary>
    /// Sets the retry count for failed calculations
    /// </summary>
    /// <param name="retryCount">Number of retries</param>
    /// <returns>This builder for fluent configuration</returns>
    public IFormulaBuilder WithRetryCount(int retryCount)
    {
        _formula.RetryCount = retryCount;
        return this;
    }

    /// <summary>
    /// Sets whether to cache the formula result
    /// </summary>
    /// <param name="cacheResult">True to cache result</param>
    /// <returns>This builder for fluent configuration</returns>
    public IFormulaBuilder WithCacheResult(bool cacheResult)
    {
        _formula.CacheResult = cacheResult;
        return this;
    }

    /// <summary>
    /// Sets the cache time-to-live for the formula result
    /// </summary>
    /// <param name="cacheTtlMs">Cache TTL in milliseconds</param>
    /// <returns>This builder for fluent configuration</returns>
    public IFormulaBuilder WithCacheTtl(int cacheTtlMs)
    {
        _formula.CacheTtlMs = cacheTtlMs;
        return this;
    }

    /// <summary>
    /// Sets tags for the formula
    /// </summary>
    /// <param name="tags">Tags</param>
    /// <returns>This builder for fluent configuration</returns>
    public IFormulaBuilder WithTags(params string[] tags)
    {
        _formula.Tags = tags?.ToList() ?? new List<string>();
        return this;
    }

    /// <summary>
    /// Sets metadata for the formula
    /// </summary>
    /// <param name="metadata">Metadata dictionary</param>
    /// <returns>This builder for fluent configuration</returns>
    public IFormulaBuilder WithMetadata(Dictionary<string, object> metadata)
    {
        _formula.Metadata = metadata ?? new Dictionary<string, object>();
        return this;
    }

    /// <summary>
    /// Sets dependencies for the formula
    /// </summary>
    /// <param name="dependencies">List of dependency IDs</param>
    /// <returns>This builder for fluent configuration</returns>
    public IFormulaBuilder WithDependencies(params string[] dependencies)
    {
        _formula.Dependencies = dependencies?.ToList() ?? new List<string>();
        return this;
    }

    /// <summary>
    /// Sets the error handling strategy for the formula
    /// </summary>
    /// <param name="strategy">Error handling strategy</param>
    /// <returns>This builder for fluent configuration</returns>
    public IFormulaBuilder WithErrorHandling(ErrorHandlingStrategy strategy)
    {
        _formula.ErrorHandling = strategy;
        return this;
    }

    /// <summary>
    /// Sets the default value to use when calculation fails
    /// </summary>
    /// <param name="defaultValue">Default value</param>
    /// <returns>This builder for fluent configuration</returns>
    public IFormulaBuilder WithDefaultValue(object defaultValue)
    {
        _formula.DefaultValue = defaultValue;
        return this;
    }

    /// <summary>
    /// Builds and returns the configured formula
    /// </summary>
    /// <returns>Configured formula</returns>
    public IFormula Build()
    {
        return _formula;
    }

    /// <summary>
    /// Completes the formula configuration and returns the engine for further configuration
    /// </summary>
    /// <returns>The logic engine for method chaining</returns>
    public ILogicEngine Complete()
    {
        return _engine;
    }
}
