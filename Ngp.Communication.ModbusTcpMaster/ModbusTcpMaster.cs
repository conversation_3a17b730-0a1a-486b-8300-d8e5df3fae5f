using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Ngp.Communication.ModbusTcpMaster.Commands;
using Ngp.Communication.ModbusTcpMaster.Connection;
using Ngp.Communication.ModbusTcpMaster.Enums;
using Ngp.Communication.ModbusTcpMaster.Events;
using Ngp.Communication.ModbusTcpMaster.Interfaces;
using Ngp.Communication.ModbusTcpMaster.Models;
using Ngp.Communication.ModbusTcpMaster.Optimization;
using Ngp.Communication.ModbusTcpMaster.Polling;
using Ngp.Communication.ModbusTcpMaster.Utilities;

namespace Ngp.Communication.ModbusTcpMaster;

/// <summary>
/// Main implementation of the Modbus TCP Master
/// </summary>
public class ModbusTcpMaster : IModbusTcpMaster
{
    private readonly EndpointConfiguration _configuration;
    private readonly List<RegisterDefinition> _registers;
    private readonly ILogger<ModbusTcpMaster> _logger;
    
    private readonly ConnectionManager _connectionManager;
    private readonly ModbusCommandProcessor _commandProcessor;
    private readonly PacketOptimizer _optimizer;
    private readonly PollingEngine _pollingEngine;
    
    private bool _disposed = false;

    /// <summary>
    /// Event raised when a connection state changes
    /// </summary>
    public event EventHandler<ConnectionStateChangedEventArgs>? ConnectionStateChanged;

    /// <summary>
    /// Event raised when a register value changes
    /// </summary>
    public event EventHandler<RegisterValueChangedEventArgs>? RegisterValueChanged;

    /// <summary>
    /// Event raised when a Modbus error occurs
    /// </summary>
    public event EventHandler<ModbusErrorEventArgs>? ModbusError;

    /// <summary>
    /// Gets the current connection state
    /// </summary>
    public ConnectionState ConnectionState => _connectionManager.State;

    /// <summary>
    /// Gets the endpoint configuration
    /// </summary>
    public EndpointConfiguration Configuration => _configuration;

    /// <summary>
    /// Gets the list of registered polling registers
    /// </summary>
    public IReadOnlyList<RegisterDefinition> PollingRegisters => _registers.AsReadOnly();

    /// <summary>
    /// Initializes a new instance of the ModbusTcpMaster class
    /// </summary>
    /// <param name="configuration">Endpoint configuration</param>
    /// <param name="registers">List of registers to poll</param>
    /// <param name="logger">Logger instance</param>
    internal ModbusTcpMaster(
        EndpointConfiguration configuration,
        IEnumerable<RegisterDefinition> registers,
        ILogger<ModbusTcpMaster>? logger = null)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _registers = registers?.ToList() ?? throw new ArgumentNullException(nameof(registers));
        _logger = logger ?? NullLogger<ModbusTcpMaster>.Instance;

        // Initialize components with proper logging
        _connectionManager = new ConnectionManager(_configuration,
            logger != null ? new LoggerAdapter<ConnectionManager>(logger) : NullLogger<ConnectionManager>.Instance);

        _commandProcessor = new ModbusCommandProcessor(_configuration, _connectionManager,
            logger != null ? new LoggerAdapter<ModbusCommandProcessor>(logger) : NullLogger<ModbusCommandProcessor>.Instance);

        _optimizer = new PacketOptimizer(_configuration,
            logger != null ? new LoggerAdapter<PacketOptimizer>(logger) : NullLogger<PacketOptimizer>.Instance);

        _pollingEngine = new PollingEngine(_configuration, _commandProcessor, _optimizer,
            logger != null ? new LoggerAdapter<PollingEngine>(logger) : NullLogger<PollingEngine>.Instance);

        // Wire up events
        _connectionManager.ConnectionStateChanged += OnConnectionStateChanged;
        _pollingEngine.RegisterValueChanged += OnRegisterValueChanged;
        _pollingEngine.ModbusError += OnModbusError;

        // Set registers for polling
        _logger.LogInformation("Setting {RegisterCount} registers for polling optimization", _registers.Count);
        try
        {
            _pollingEngine.SetRegisters(_registers);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting registers for polling optimization");
            throw;
        }

        _logger.LogInformation("ModbusTcpMaster created for endpoint {EndpointId} with {RegisterCount} registers",
            _configuration.Id, _registers.Count);
    }

    /// <summary>
    /// Starts the polling operation
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(ModbusTcpMaster));

        _logger.LogInformation("Starting ModbusTcpMaster for endpoint {EndpointId}", _configuration.Id);

        try
        {
            // Connect to the endpoint
            var connected = await _connectionManager.ConnectAsync(cancellationToken);
            if (!connected)
            {
                throw new InvalidOperationException($"Failed to connect to endpoint {_configuration.Id}");
            }

            // Start polling (command processor will use the connection manager's connection)
            _logger.LogInformation("Starting polling engine for endpoint {EndpointId}", _configuration.Id);
            await _pollingEngine.StartAsync(cancellationToken);
            _logger.LogInformation("Polling engine started successfully for endpoint {EndpointId}", _configuration.Id);

            _logger.LogInformation("ModbusTcpMaster started successfully for endpoint {EndpointId}", _configuration.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start ModbusTcpMaster for endpoint {EndpointId}", _configuration.Id);
            throw;
        }
    }

    /// <summary>
    /// Stops the polling operation
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed) return;

        _logger.LogInformation("Stopping ModbusTcpMaster for endpoint {EndpointId}", _configuration.Id);

        try
        {
            // Stop polling first
            await _pollingEngine.StopAsync(cancellationToken);

            // Then disconnect connection manager (command processor uses the same connection)
            await _connectionManager.DisconnectAsync(cancellationToken);

            _logger.LogInformation("ModbusTcpMaster stopped successfully for endpoint {EndpointId}", _configuration.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping ModbusTcpMaster for endpoint {EndpointId}", _configuration.Id);
        }
    }

    /// <summary>
    /// Adds a register for polling
    /// </summary>
    /// <param name="register">The register definition</param>
    public void AddRegister(RegisterDefinition register)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(ModbusTcpMaster));
        if (register == null) throw new ArgumentNullException(nameof(register));
        if (!register.IsValid()) throw new ArgumentException("Invalid register definition", nameof(register));

        _registers.Add(register);
        _pollingEngine.SetRegisters(_registers);

        _logger.LogDebug("Added register: SlaveId={SlaveId}, Address={Address}, Function={Function}", 
            register.SlaveId, register.Address, register.Function);
    }

    /// <summary>
    /// Removes a register from polling
    /// </summary>
    /// <param name="slaveId">The slave ID</param>
    /// <param name="address">The register address</param>
    /// <param name="function">The function code</param>
    /// <returns>True if removed, false if not found</returns>
    public bool RemoveRegister(byte slaveId, ushort address, ModbusFunction function)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(ModbusTcpMaster));

        var register = _registers.FirstOrDefault(r => 
            r.SlaveId == slaveId && r.Address == address && r.Function == function);

        if (register != null)
        {
            _registers.Remove(register);
            _pollingEngine.SetRegisters(_registers);

            _logger.LogDebug("Removed register: SlaveId={SlaveId}, Address={Address}, Function={Function}", 
                slaveId, address, function);
            return true;
        }

        return false;
    }

    /// <summary>
    /// Writes a single coil
    /// </summary>
    /// <param name="slaveId">The slave ID</param>
    /// <param name="address">The coil address</param>
    /// <param name="value">The value to write</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task<bool> WriteSingleCoilAsync(byte slaveId, ushort address, bool value, CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(ModbusTcpMaster));

        var writeRequest = new WriteRequest
        {
            SlaveId = slaveId,
            Address = address,
            Function = ModbusFunction.WriteSingleCoil,
            Value = value,
            DataType = typeof(bool),
            CompletionSource = new TaskCompletionSource<bool>()
        };

        _pollingEngine.QueueWrite(writeRequest);
        return await writeRequest.CompletionSource.Task.WaitAsync(cancellationToken);
    }

    /// <summary>
    /// Writes a single register
    /// </summary>
    /// <param name="slaveId">The slave ID</param>
    /// <param name="address">The register address</param>
    /// <param name="value">The value to write</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task<bool> WriteSingleRegisterAsync(byte slaveId, ushort address, ushort value, CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(ModbusTcpMaster));

        var writeRequest = new WriteRequest
        {
            SlaveId = slaveId,
            Address = address,
            Function = ModbusFunction.WriteSingleRegister,
            Value = value,
            DataType = typeof(ushort),
            CompletionSource = new TaskCompletionSource<bool>()
        };

        _pollingEngine.QueueWrite(writeRequest);
        return await writeRequest.CompletionSource.Task.WaitAsync(cancellationToken);
    }

    /// <summary>
    /// Writes multiple coils
    /// </summary>
    /// <param name="slaveId">The slave ID</param>
    /// <param name="startAddress">The starting address</param>
    /// <param name="values">The values to write</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task<bool> WriteMultipleCoilsAsync(byte slaveId, ushort startAddress, bool[] values, CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(ModbusTcpMaster));
        if (values == null) throw new ArgumentNullException(nameof(values));

        // Check write mode
        if (_configuration.WriteMode == WriteMode.Single)
        {
            throw new InvalidOperationException("Multiple write operations are not allowed in Single write mode");
        }

        var writeRequest = new WriteRequest
        {
            SlaveId = slaveId,
            Address = startAddress,
            Function = ModbusFunction.WriteMultipleCoils,
            Value = values,
            DataType = typeof(bool[]),
            CompletionSource = new TaskCompletionSource<bool>()
        };

        _pollingEngine.QueueWrite(writeRequest);
        return await writeRequest.CompletionSource.Task.WaitAsync(cancellationToken);
    }

    /// <summary>
    /// Writes multiple registers
    /// </summary>
    /// <param name="slaveId">The slave ID</param>
    /// <param name="startAddress">The starting address</param>
    /// <param name="values">The values to write</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task<bool> WriteMultipleRegistersAsync(byte slaveId, ushort startAddress, ushort[] values, CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(ModbusTcpMaster));
        if (values == null) throw new ArgumentNullException(nameof(values));

        // Check write mode
        if (_configuration.WriteMode == WriteMode.Single)
        {
            throw new InvalidOperationException("Multiple write operations are not allowed in Single write mode");
        }

        var writeRequest = new WriteRequest
        {
            SlaveId = slaveId,
            Address = startAddress,
            Function = ModbusFunction.WriteMultipleRegisters,
            Value = values,
            DataType = typeof(ushort[]),
            CompletionSource = new TaskCompletionSource<bool>()
        };

        _pollingEngine.QueueWrite(writeRequest);
        return await writeRequest.CompletionSource.Task.WaitAsync(cancellationToken);
    }

    /// <summary>
    /// Writes a typed value to a register
    /// </summary>
    /// <param name="slaveId">The slave ID</param>
    /// <param name="address">The register address</param>
    /// <param name="value">The value to write</param>
    /// <param name="dataType">The data type</param>
    /// <param name="endianType">The endian type</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task<bool> WriteTypedValueAsync(byte slaveId, ushort address, object value, Type dataType, EndianType endianType, CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(ModbusTcpMaster));
        if (value == null) throw new ArgumentNullException(nameof(value));

        // Convert typed value to register values
        var bytes = DataConverter.ConvertToBytes(value, dataType, endianType);
        var registerCount = (bytes.Length + 1) / 2; // Round up to nearest register

        if (registerCount == 1)
        {
            var registerValue = BitConverter.ToUInt16(bytes, 0);
            return await WriteSingleRegisterAsync(slaveId, address, registerValue, cancellationToken);
        }
        else
        {
            var registerValues = new ushort[registerCount];
            for (int i = 0; i < registerCount; i++)
            {
                var offset = i * 2;
                if (offset + 1 < bytes.Length)
                {
                    registerValues[i] = BitConverter.ToUInt16(bytes, offset);
                }
                else if (offset < bytes.Length)
                {
                    registerValues[i] = bytes[offset];
                }
            }
            return await WriteMultipleRegistersAsync(slaveId, address, registerValues, cancellationToken);
        }
    }

    /// <summary>
    /// Gets the current value of a register
    /// </summary>
    /// <param name="slaveId">The slave ID</param>
    /// <param name="address">The register address</param>
    /// <param name="function">The function code</param>
    /// <returns>The current value as raw bytes, or null if not available</returns>
    public byte[]? GetCurrentValue(byte slaveId, ushort address, ModbusFunction function)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(ModbusTcpMaster));

        var registerValue = _pollingEngine.GetCurrentValue(slaveId, address, function);
        return registerValue?.RawData;
    }

    /// <summary>
    /// Gets the current typed value of a register
    /// </summary>
    /// <param name="slaveId">The slave ID</param>
    /// <param name="address">The register address</param>
    /// <param name="function">The function code</param>
    /// <param name="dataType">The data type</param>
    /// <param name="endianType">The endian type</param>
    /// <returns>The current typed value, or null if not available</returns>
    public object? GetCurrentTypedValue(byte slaveId, ushort address, ModbusFunction function, Type dataType, EndianType endianType)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(ModbusTcpMaster));

        var rawValue = GetCurrentValue(slaveId, address, function);
        if (rawValue == null) return null;

        return DataConverter.ConvertFromBytes(rawValue, dataType, endianType);
    }

    /// <summary>
    /// Handles connection state changes
    /// </summary>
    private void OnConnectionStateChanged(object? sender, ConnectionStateChangedEventArgs e)
    {
        ConnectionStateChanged?.Invoke(this, e);
    }

    /// <summary>
    /// Handles register value changes
    /// </summary>
    private void OnRegisterValueChanged(object? sender, RegisterValueChangedEventArgs e)
    {
        RegisterValueChanged?.Invoke(this, e);
    }

    /// <summary>
    /// Handles Modbus errors
    /// </summary>
    private void OnModbusError(object? sender, ModbusErrorEventArgs e)
    {
        ModbusError?.Invoke(this, e);
    }

    /// <summary>
    /// Disposes the Modbus TCP Master
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _disposed = true;

            try
            {
                StopAsync().Wait(5000);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error during dispose of ModbusTcpMaster for {EndpointId}", _configuration.Id);
            }

            _pollingEngine?.Dispose();
            _commandProcessor?.Dispose();
            _connectionManager?.Dispose();

            _logger.LogInformation("ModbusTcpMaster disposed for endpoint {EndpointId}", _configuration.Id);
        }
    }
}
