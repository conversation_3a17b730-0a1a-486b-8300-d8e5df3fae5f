using Ngp.Communication.ModbusTcpMaster.Enums;

namespace Ngp.Communication.ModbusTcpMaster.Models;

/// <summary>
/// Configuration for a Modbus TCP endpoint
/// </summary>
public class EndpointConfiguration
{
    /// <summary>
    /// Gets or sets the endpoint identifier
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the IP address
    /// </summary>
    public string IpAddress { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the port number
    /// </summary>
    public ushort Port { get; set; } = 502;

    /// <summary>
    /// Gets or sets the Modbus protocol type
    /// </summary>
    public ModbusProtocol Protocol { get; set; } = ModbusProtocol.ModbusTcp;

    /// <summary>
    /// Gets or sets the write mode
    /// </summary>
    public WriteMode WriteMode { get; set; } = WriteMode.Single;

    /// <summary>
    /// Gets or sets the address mode
    /// </summary>
    public AddressMode AddressMode { get; set; } = AddressMode.ZeroBased;

    /// <summary>
    /// Gets or sets the read timeout in milliseconds
    /// </summary>
    public int ReadTimeoutMilliseconds { get; set; } = 5000;

    /// <summary>
    /// Gets or sets the write timeout in milliseconds
    /// </summary>
    public int WriteTimeoutMilliseconds { get; set; } = 5000;

    /// <summary>
    /// Gets or sets the package delay in milliseconds
    /// </summary>
    public int PackageDelayMilliseconds { get; set; } = 0;

    /// <summary>
    /// Gets or sets the poll delay in milliseconds
    /// </summary>
    public int PollDelayMilliseconds { get; set; } = 100;

    /// <summary>
    /// Gets or sets the maximum digital polling quantity
    /// </summary>
    public ushort MaxDigitalPollingQuantity { get; set; } = 2000;

    /// <summary>
    /// Gets or sets the maximum analog polling quantity
    /// </summary>
    public ushort MaxAnalogPollingQuantity { get; set; } = 125;

    /// <summary>
    /// Gets or sets the connection retry count
    /// </summary>
    public int RetryCount { get; set; } = 3;

    /// <summary>
    /// Gets or sets the connection retry delay in milliseconds
    /// </summary>
    public int RetryDelayMilliseconds { get; set; } = 1000;

    /// <summary>
    /// Gets or sets the parallelization level for ModbusTCP (ignored for RTU over TCP)
    /// </summary>
    public int ParallelizationLevel { get; set; } = 1;

    /// <summary>
    /// Gets or sets whether to enable automatic parallelization optimization
    /// </summary>
    public bool AutoParallelization { get; set; } = false;

    /// <summary>
    /// Gets or sets the debounce time in milliseconds for digital inputs
    /// </summary>
    public int DebounceTimeMilliseconds { get; set; } = 50;
}
