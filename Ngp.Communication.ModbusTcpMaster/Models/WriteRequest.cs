using Ngp.Communication.ModbusTcpMaster.Enums;

namespace Ngp.Communication.ModbusTcpMaster.Models;

/// <summary>
/// Represents a write request to a Modbus slave
/// </summary>
public class WriteRequest
{
    /// <summary>
    /// Gets or sets the slave ID
    /// </summary>
    public byte SlaveId { get; set; }

    /// <summary>
    /// Gets or sets the register address
    /// </summary>
    public ushort Address { get; set; }

    /// <summary>
    /// Gets or sets the Modbus function code
    /// </summary>
    public ModbusFunction Function { get; set; }

    /// <summary>
    /// Gets or sets the value to write
    /// </summary>
    public object Value { get; set; } = null!;

    /// <summary>
    /// Gets or sets the data type
    /// </summary>
    public Type DataType { get; set; } = typeof(ushort);

    /// <summary>
    /// Gets or sets the endian type
    /// </summary>
    public EndianType EndianType { get; set; } = EndianType.BigEndian;

    /// <summary>
    /// Gets or sets the priority of this write request
    /// </summary>
    public int Priority { get; set; } = 0;

    /// <summary>
    /// Gets or sets the timestamp when the request was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Gets or sets the completion source for async operations
    /// </summary>
    public TaskCompletionSource<bool>? CompletionSource { get; set; }

    /// <summary>
    /// Validates the write request
    /// </summary>
    /// <returns>True if valid, false otherwise</returns>
    public bool IsValid()
    {
        if (SlaveId == 0 || SlaveId > 247)
            return false;

        if (!Enum.IsDefined(typeof(ModbusFunction), Function))
            return false;

        if (!IsWriteFunction(Function))
            return false;

        if (Value == null)
            return false;

        if (!Enum.IsDefined(typeof(EndianType), EndianType))
            return false;

        return true;
    }

    /// <summary>
    /// Checks if the function code is a write function
    /// </summary>
    /// <param name="function">The function code to check</param>
    /// <returns>True if it's a write function, false otherwise</returns>
    private static bool IsWriteFunction(ModbusFunction function)
    {
        return function is ModbusFunction.WriteSingleCoil or
                         ModbusFunction.WriteSingleRegister or
                         ModbusFunction.WriteMultipleCoils or
                         ModbusFunction.WriteMultipleRegisters;
    }
}
