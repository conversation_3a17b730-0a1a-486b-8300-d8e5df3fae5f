using Ngp.Communication.ModbusTcpMaster.Enums;

namespace Ngp.Communication.ModbusTcpMaster.Models;

/// <summary>
/// Definition of a Modbus register for polling
/// </summary>
public class RegisterDefinition
{
    /// <summary>
    /// Gets or sets the slave ID
    /// </summary>
    public byte SlaveId { get; set; }

    /// <summary>
    /// Gets or sets the register address
    /// </summary>
    public ushort Address { get; set; }

    /// <summary>
    /// Gets or sets the Modbus function code
    /// </summary>
    public ModbusFunction Function { get; set; }

    /// <summary>
    /// Gets or sets the data type
    /// </summary>
    public Type DataType { get; set; } = typeof(ushort);

    /// <summary>
    /// Gets or sets the endian type
    /// </summary>
    public EndianType EndianType { get; set; } = EndianType.BigEndian;

    /// <summary>
    /// Gets or sets the number of registers to read (for multi-register data types)
    /// </summary>
    public ushort RegisterCount { get; set; } = 1;

    /// <summary>
    /// Gets or sets whether debouncing is enabled for this register
    /// </summary>
    public bool EnableDebounce { get; set; } = false;

    /// <summary>
    /// Gets or sets a custom identifier for this register
    /// </summary>
    public string? Tag { get; set; }

    /// <summary>
    /// Gets or sets a description for this register
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Calculates the number of registers needed for the specified data type
    /// </summary>
    /// <returns>The number of registers required</returns>
    public ushort CalculateRegisterCount()
    {
        return DataType switch
        {
            Type t when t == typeof(bool) => 1,
            Type t when t == typeof(byte) => 1,
            Type t when t == typeof(short) => 1,
            Type t when t == typeof(ushort) => 1,
            Type t when t == typeof(int) => 2,
            Type t when t == typeof(uint) => 2,
            Type t when t == typeof(long) => 4,
            Type t when t == typeof(ulong) => 4,
            Type t when t == typeof(float) => 2,
            Type t when t == typeof(double) => 4,
            _ => RegisterCount
        };
    }

    /// <summary>
    /// Validates the register definition
    /// </summary>
    /// <returns>True if valid, false otherwise</returns>
    public bool IsValid()
    {
        if (SlaveId == 0 || SlaveId > 247)
            return false;

        if (!Enum.IsDefined(typeof(ModbusFunction), Function))
            return false;

        if (!Enum.IsDefined(typeof(EndianType), EndianType))
            return false;

        var calculatedCount = CalculateRegisterCount();
        if (RegisterCount != calculatedCount)
            RegisterCount = calculatedCount;

        return true;
    }
}
