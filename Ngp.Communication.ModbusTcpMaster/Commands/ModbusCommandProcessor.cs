using Microsoft.Extensions.Logging;
using Ngp.Communication.ModbusTcpMaster.Connection;
using Ngp.Communication.ModbusTcpMaster.Enums;
using Ngp.Communication.ModbusTcpMaster.Models;
using Ngp.Communication.ModbusTcpMaster.Protocol;
using Ngp.Communication.ModbusTcpMaster.Utilities;
using System.Net.Sockets;

namespace Ngp.Communication.ModbusTcpMaster.Commands;

/// <summary>
/// Processes Modbus commands and handles communication with slaves
/// </summary>
public class ModbusCommandProcessor : IDisposable
{
    private readonly EndpointConfiguration _configuration;
    private readonly ConnectionManager _connectionManager;
    private readonly ILogger<ModbusCommandProcessor> _logger;
    private readonly SemaphoreSlim _commandSemaphore;
    private ushort _transactionId = 0;
    private bool _disposed = false;

    /// <summary>
    /// Initializes a new instance of the ModbusCommandProcessor class
    /// </summary>
    /// <param name="configuration">Endpoint configuration</param>
    /// <param name="connectionManager">Connection manager instance</param>
    /// <param name="logger">Logger instance</param>
    public ModbusCommandProcessor(EndpointConfiguration configuration, ConnectionManager connectionManager, ILogger<ModbusCommandProcessor> logger)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        // For RTU over TCP, only allow one command at a time
        var maxConcurrency = _configuration.Protocol == ModbusProtocol.ModbusRtuOverTcp ? 1 : _configuration.ParallelizationLevel;
        _commandSemaphore = new SemaphoreSlim(maxConcurrency, maxConcurrency);
    }

    /// <summary>
    /// Checks if the connection is active
    /// </summary>
    /// <returns>True if connected</returns>
    public bool IsConnected => _connectionManager.IsConnected;

    /// <summary>
    /// Executes a read coils command
    /// </summary>
    /// <param name="slaveId">Slave ID</param>
    /// <param name="startAddress">Starting address</param>
    /// <param name="quantity">Number of coils to read</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Array of coil values</returns>
    public async Task<bool[]?> ReadCoilsAsync(byte slaveId, ushort startAddress, ushort quantity, CancellationToken cancellationToken = default)
    {
        var convertedAddress = AddressConverter.ToZeroBased(startAddress, _configuration.AddressMode, ModbusFunction.ReadCoils);
        var commandData = ModbusCommandBuilder.CreateReadCoilsCommand(convertedAddress, quantity);
        
        var response = await ExecuteCommandAsync(slaveId, ModbusFunction.ReadCoils, commandData, cancellationToken);
        if (response == null) return null;

        return ModbusCommandBuilder.ParseReadCoilsResponse(response, quantity);
    }

    /// <summary>
    /// Executes a read discrete inputs command
    /// </summary>
    /// <param name="slaveId">Slave ID</param>
    /// <param name="startAddress">Starting address</param>
    /// <param name="quantity">Number of inputs to read</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Array of input values</returns>
    public async Task<bool[]?> ReadDiscreteInputsAsync(byte slaveId, ushort startAddress, ushort quantity, CancellationToken cancellationToken = default)
    {
        var convertedAddress = AddressConverter.ToZeroBased(startAddress, _configuration.AddressMode, ModbusFunction.ReadDiscreteInputs);
        var commandData = ModbusCommandBuilder.CreateReadDiscreteInputsCommand(convertedAddress, quantity);
        
        var response = await ExecuteCommandAsync(slaveId, ModbusFunction.ReadDiscreteInputs, commandData, cancellationToken);
        if (response == null) return null;

        return ModbusCommandBuilder.ParseReadDiscreteInputsResponse(response, quantity);
    }

    /// <summary>
    /// Executes a read holding registers command
    /// </summary>
    /// <param name="slaveId">Slave ID</param>
    /// <param name="startAddress">Starting address</param>
    /// <param name="quantity">Number of registers to read</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Array of register values</returns>
    public async Task<ushort[]?> ReadHoldingRegistersAsync(byte slaveId, ushort startAddress, ushort quantity, CancellationToken cancellationToken = default)
    {
        var convertedAddress = AddressConverter.ToZeroBased(startAddress, _configuration.AddressMode, ModbusFunction.ReadHoldingRegisters);
        var commandData = ModbusCommandBuilder.CreateReadHoldingRegistersCommand(convertedAddress, quantity);
        
        var response = await ExecuteCommandAsync(slaveId, ModbusFunction.ReadHoldingRegisters, commandData, cancellationToken);
        if (response == null) return null;

        return ModbusCommandBuilder.ParseReadHoldingRegistersResponse(response, quantity);
    }

    /// <summary>
    /// Executes a read input registers command
    /// </summary>
    /// <param name="slaveId">Slave ID</param>
    /// <param name="startAddress">Starting address</param>
    /// <param name="quantity">Number of registers to read</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Array of register values</returns>
    public async Task<ushort[]?> ReadInputRegistersAsync(byte slaveId, ushort startAddress, ushort quantity, CancellationToken cancellationToken = default)
    {
        var convertedAddress = AddressConverter.ToZeroBased(startAddress, _configuration.AddressMode, ModbusFunction.ReadInputRegisters);
        var commandData = ModbusCommandBuilder.CreateReadInputRegistersCommand(convertedAddress, quantity);
        
        var response = await ExecuteCommandAsync(slaveId, ModbusFunction.ReadInputRegisters, commandData, cancellationToken);
        if (response == null) return null;

        return ModbusCommandBuilder.ParseReadInputRegistersResponse(response, quantity);
    }

    /// <summary>
    /// Executes a write single coil command
    /// </summary>
    /// <param name="slaveId">Slave ID</param>
    /// <param name="address">Coil address</param>
    /// <param name="value">Value to write</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if successful</returns>
    public async Task<bool> WriteSingleCoilAsync(byte slaveId, ushort address, bool value, CancellationToken cancellationToken = default)
    {
        var convertedAddress = AddressConverter.ToZeroBased(address, _configuration.AddressMode, ModbusFunction.WriteSingleCoil);
        var commandData = ModbusCommandBuilder.CreateWriteSingleCoilCommand(convertedAddress, value);
        
        var response = await ExecuteCommandAsync(slaveId, ModbusFunction.WriteSingleCoil, commandData, cancellationToken);
        return response != null;
    }

    /// <summary>
    /// Executes a write single register command
    /// </summary>
    /// <param name="slaveId">Slave ID</param>
    /// <param name="address">Register address</param>
    /// <param name="value">Value to write</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if successful</returns>
    public async Task<bool> WriteSingleRegisterAsync(byte slaveId, ushort address, ushort value, CancellationToken cancellationToken = default)
    {
        var convertedAddress = AddressConverter.ToZeroBased(address, _configuration.AddressMode, ModbusFunction.WriteSingleRegister);
        var commandData = ModbusCommandBuilder.CreateWriteSingleRegisterCommand(convertedAddress, value);
        
        var response = await ExecuteCommandAsync(slaveId, ModbusFunction.WriteSingleRegister, commandData, cancellationToken);
        return response != null;
    }

    /// <summary>
    /// Executes a write multiple coils command
    /// </summary>
    /// <param name="slaveId">Slave ID</param>
    /// <param name="startAddress">Starting address</param>
    /// <param name="values">Values to write</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if successful</returns>
    public async Task<bool> WriteMultipleCoilsAsync(byte slaveId, ushort startAddress, bool[] values, CancellationToken cancellationToken = default)
    {
        var convertedAddress = AddressConverter.ToZeroBased(startAddress, _configuration.AddressMode, ModbusFunction.WriteMultipleCoils);
        var commandData = ModbusCommandBuilder.CreateWriteMultipleCoilsCommand(convertedAddress, values);
        
        var response = await ExecuteCommandAsync(slaveId, ModbusFunction.WriteMultipleCoils, commandData, cancellationToken);
        return response != null;
    }

    /// <summary>
    /// Executes a write multiple registers command
    /// </summary>
    /// <param name="slaveId">Slave ID</param>
    /// <param name="startAddress">Starting address</param>
    /// <param name="values">Values to write</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if successful</returns>
    public async Task<bool> WriteMultipleRegistersAsync(byte slaveId, ushort startAddress, ushort[] values, CancellationToken cancellationToken = default)
    {
        var convertedAddress = AddressConverter.ToZeroBased(startAddress, _configuration.AddressMode, ModbusFunction.WriteMultipleRegisters);
        var commandData = ModbusCommandBuilder.CreateWriteMultipleRegistersCommand(convertedAddress, values);
        
        var response = await ExecuteCommandAsync(slaveId, ModbusFunction.WriteMultipleRegisters, commandData, cancellationToken);
        return response != null;
    }

    /// <summary>
    /// Executes a Modbus command and returns the response data
    /// </summary>
    /// <param name="slaveId">Slave ID</param>
    /// <param name="function">Function code</param>
    /// <param name="commandData">Command data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Response data or null if failed</returns>
    private async Task<byte[]?> ExecuteCommandAsync(byte slaveId, ModbusFunction function, byte[] commandData, CancellationToken cancellationToken)
    {
        // Ensure connection is active
        if (!await _connectionManager.EnsureConnectedAsync(cancellationToken))
        {
            _logger.LogWarning("Failed to establish connection to {IpAddress}:{Port}", _configuration.IpAddress, _configuration.Port);
            return null;
        }

        await _commandSemaphore.WaitAsync(cancellationToken);
        try
        {
            if (_configuration.Protocol == ModbusProtocol.ModbusTcp)
            {
                return await ExecuteModbusTcpCommandAsync(slaveId, function, commandData, cancellationToken);
            }
            else
            {
                return await ExecuteModbusRtuCommandAsync(slaveId, function, commandData, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            // Handle connection errors by notifying the connection manager
            if (IsConnectionException(ex))
            {
                _logger.LogWarning(ex, "Connection error detected, triggering reconnection");
                await _connectionManager.HandleConnectionErrorAsync(ex);
            }
            throw;
        }
        finally
        {
            _commandSemaphore.Release();
        }
    }

    /// <summary>
    /// Determines if an exception is connection-related
    /// </summary>
    private static bool IsConnectionException(Exception ex)
    {
        return ex is SocketException or
               IOException or
               ObjectDisposedException or
               InvalidOperationException;
    }

    /// <summary>
    /// Executes a Modbus TCP command
    /// </summary>
    private async Task<byte[]?> ExecuteModbusTcpCommandAsync(byte slaveId, ModbusFunction function, byte[] commandData, CancellationToken cancellationToken)
    {
        var networkStream = _connectionManager.NetworkStream;
        if (networkStream == null)
        {
            _logger.LogWarning("Network stream is not available");
            return null;
        }

        var transactionId = ++_transactionId;
        var frame = new ModbusTcpFrame
        {
            TransactionId = transactionId,
            UnitId = slaveId,
            Function = function,
            Data = commandData
        };

        var requestBytes = frame.ToByteArray();

        try
        {
            // Send request
            await networkStream.WriteAsync(requestBytes, cancellationToken);
            await networkStream.FlushAsync(cancellationToken);

            if (_configuration.PackageDelayMilliseconds > 0)
            {
                await Task.Delay(_configuration.PackageDelayMilliseconds, cancellationToken);
            }

            // Read response
            var responseFrame = await ReadModbusTcpFrameAsync(networkStream, cancellationToken);
            if (responseFrame == null || responseFrame.TransactionId != transactionId)
            {
                _logger.LogWarning("Invalid response frame for transaction {TransactionId}", transactionId);
                return null;
            }

            if (responseFrame.IsErrorResponse())
            {
                var errorCode = responseFrame.GetErrorCode();
                _logger.LogWarning("Modbus error response: Function {Function}, Error Code {ErrorCode}", function, errorCode);
                return null;
            }

            return responseFrame.Data;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing Modbus TCP command: Function {Function}, Slave {SlaveId}", function, slaveId);
            throw; // Re-throw to allow connection error handling in ExecuteCommandAsync
        }
    }

    /// <summary>
    /// Executes a Modbus RTU over TCP command
    /// </summary>
    private async Task<byte[]?> ExecuteModbusRtuCommandAsync(byte slaveId, ModbusFunction function, byte[] commandData, CancellationToken cancellationToken)
    {
        var networkStream = _connectionManager.NetworkStream;
        if (networkStream == null)
        {
            _logger.LogWarning("Network stream is not available");
            return null;
        }

        var frame = new ModbusRtuFrame
        {
            SlaveAddress = slaveId,
            Function = function,
            Data = commandData
        };

        var requestBytes = frame.ToByteArray();

        try
        {
            // Send request
            await networkStream.WriteAsync(requestBytes, cancellationToken);
            await networkStream.FlushAsync(cancellationToken);

            if (_configuration.PackageDelayMilliseconds > 0)
            {
                await Task.Delay(_configuration.PackageDelayMilliseconds, cancellationToken);
            }

            // Read response
            var responseFrame = await ReadModbusRtuFrameAsync(networkStream, cancellationToken);
            if (responseFrame == null || responseFrame.SlaveAddress != slaveId)
            {
                _logger.LogWarning("Invalid response frame for slave {SlaveId}", slaveId);
                return null;
            }

            if (responseFrame.IsErrorResponse())
            {
                var errorCode = responseFrame.GetErrorCode();
                _logger.LogWarning("Modbus error response: Function {Function}, Error Code {ErrorCode}", function, errorCode);
                return null;
            }

            return responseFrame.Data;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing Modbus RTU command: Function {Function}, Slave {SlaveId}", function, slaveId);
            throw; // Re-throw to allow connection error handling in ExecuteCommandAsync
        }
    }

    /// <summary>
    /// Reads a Modbus TCP frame from the network stream
    /// </summary>
    private async Task<ModbusTcpFrame?> ReadModbusTcpFrameAsync(NetworkStream networkStream, CancellationToken cancellationToken)
    {
        try
        {
            // Read MBAP header first (6 bytes)
            var headerBuffer = new byte[6];
            var bytesRead = await ReadExactAsync(networkStream, headerBuffer, 6, cancellationToken);
            if (bytesRead != 6) return null;

            // Extract length from header
            var length = (ushort)((headerBuffer[4] << 8) | headerBuffer[5]);

            // Read the remaining data
            var totalFrameSize = 6 + length;
            var frameBuffer = new byte[totalFrameSize];
            Array.Copy(headerBuffer, frameBuffer, 6);

            bytesRead = await ReadExactAsync(networkStream, frameBuffer, 6, length, cancellationToken);
            if (bytesRead != length) return null;

            return ModbusTcpFrame.FromByteArray(frameBuffer);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reading Modbus TCP frame");
            throw; // Re-throw to allow connection error handling
        }
    }

    /// <summary>
    /// Reads a Modbus RTU frame from the network stream
    /// </summary>
    private async Task<ModbusRtuFrame?> ReadModbusRtuFrameAsync(NetworkStream networkStream, CancellationToken cancellationToken)
    {
        try
        {
            // For RTU, we need to read until we have a complete frame
            // This is more complex as RTU doesn't have a length field
            var buffer = new List<byte>();
            var readBuffer = new byte[1];

            // Read bytes until we have at least the minimum frame size
            while (buffer.Count < 4) // Minimum: Address + Function + CRC (2 bytes)
            {
                var bytesRead = await networkStream.ReadAsync(readBuffer, cancellationToken);
                if (bytesRead == 0) break;
                buffer.Add(readBuffer[0]);
            }

            if (buffer.Count < 4) return null;

            // Continue reading based on function code to determine expected frame size
            var function = (ModbusFunction)buffer[1];
            var expectedSize = GetExpectedRtuFrameSize(function, buffer.ToArray());

            while (buffer.Count < expectedSize)
            {
                var bytesRead = await networkStream.ReadAsync(readBuffer, cancellationToken);
                if (bytesRead == 0) break;
                buffer.Add(readBuffer[0]);
            }

            return ModbusRtuFrame.FromByteArray(buffer.ToArray());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reading Modbus RTU frame");
            throw; // Re-throw to allow connection error handling
        }
    }

    /// <summary>
    /// Determines the expected size of an RTU frame based on function code
    /// </summary>
    private static int GetExpectedRtuFrameSize(ModbusFunction function, byte[] partialFrame)
    {
        return function switch
        {
            ModbusFunction.ReadCoils or ModbusFunction.ReadDiscreteInputs or 
            ModbusFunction.ReadHoldingRegisters or ModbusFunction.ReadInputRegisters => 
                partialFrame.Length >= 3 ? 5 + partialFrame[2] : 255, // Address + Function + ByteCount + Data + CRC
            
            ModbusFunction.WriteSingleCoil or ModbusFunction.WriteSingleRegister => 8, // Fixed size
            
            ModbusFunction.WriteMultipleCoils or ModbusFunction.WriteMultipleRegisters => 8, // Fixed response size
            
            _ => 255 // Maximum possible frame size as fallback
        };
    }

    /// <summary>
    /// Reads exactly the specified number of bytes from the stream
    /// </summary>
    private async Task<int> ReadExactAsync(NetworkStream networkStream, byte[] buffer, int count, CancellationToken cancellationToken)
    {
        return await ReadExactAsync(networkStream, buffer, 0, count, cancellationToken);
    }

    /// <summary>
    /// Reads exactly the specified number of bytes from the stream starting at offset
    /// </summary>
    private async Task<int> ReadExactAsync(NetworkStream networkStream, byte[] buffer, int offset, int count, CancellationToken cancellationToken)
    {
        var totalBytesRead = 0;
        while (totalBytesRead < count)
        {
            var bytesRead = await networkStream.ReadAsync(buffer, offset + totalBytesRead, count - totalBytesRead, cancellationToken);
            if (bytesRead == 0) break;
            totalBytesRead += bytesRead;
        }
        return totalBytesRead;
    }

    /// <summary>
    /// Disposes the command processor
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _commandSemaphore?.Dispose();
            _disposed = true;
        }
    }
}
