using Microsoft.Extensions.Logging;
using Ngp.Communication.ModbusTcpMaster.Enums;
using Ngp.Communication.ModbusTcpMaster.Models;

namespace Ngp.Communication.ModbusTcpMaster.Optimization;

/// <summary>
/// Optimizes Modbus packets by merging adjacent registers and respecting boundaries
/// </summary>
public class PacketOptimizer
{
    private readonly EndpointConfiguration _configuration;
    private readonly ILogger<PacketOptimizer> _logger;

    /// <summary>
    /// Initializes a new instance of the PacketOptimizer class
    /// </summary>
    /// <param name="configuration">Endpoint configuration</param>
    /// <param name="logger">Logger instance</param>
    public PacketOptimizer(EndpointConfiguration configuration, ILogger<PacketOptimizer> logger)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Optimizes a list of register definitions by merging adjacent registers
    /// </summary>
    /// <param name="registers">List of register definitions</param>
    /// <returns>Optimized list of register groups</returns>
    public List<OptimizedRegisterGroup> OptimizeRegisters(IEnumerable<RegisterDefinition> registers)
    {
        var optimizedGroups = new List<OptimizedRegisterGroup>();
        
        // Group registers by slave ID and function code
        var groupedRegisters = registers
            .Where(r => r.IsValid())
            .GroupBy(r => new { r.SlaveId, r.Function })
            .ToList();

        foreach (var group in groupedRegisters)
        {
            var slaveId = group.Key.SlaveId;
            var function = group.Key.Function;
            
            // Sort registers by address
            var sortedRegisters = group.OrderBy(r => r.Address).ToList();
            
            // Get maximum polling quantity for this function type
            var maxQuantity = GetMaxPollingQuantity(function);
            
            // Merge adjacent registers
            var mergedGroups = MergeAdjacentRegisters(sortedRegisters, maxQuantity);
            
            foreach (var mergedGroup in mergedGroups)
            {
                var optimizedGroup = new OptimizedRegisterGroup
                {
                    SlaveId = slaveId,
                    Function = function,
                    StartAddress = mergedGroup.StartAddress,
                    Quantity = mergedGroup.Quantity,
                    Registers = mergedGroup.Registers
                };

                optimizedGroups.Add(optimizedGroup);

                // Log the optimized group details
                _logger.LogInformation("Created optimized group: SlaveId={SlaveId}, Function={Function}, StartAddress={StartAddress}, Quantity={Quantity}, RegisterCount={RegisterCount}",
                    slaveId, function, mergedGroup.StartAddress, mergedGroup.Quantity, mergedGroup.Registers.Count);
            }
        }

        _logger.LogInformation("Optimized {OriginalCount} registers into {OptimizedCount} groups",
            registers.Count(), optimizedGroups.Count);

        // Log details of each optimized group
        foreach (var group in optimizedGroups)
        {
            _logger.LogInformation("Optimized Group: Slave={SlaveId}, Function={Function}, StartAddress={StartAddress}, Quantity={Quantity}, RegisterCount={RegisterCount}",
                group.SlaveId, group.Function, group.StartAddress, group.Quantity, group.Registers.Count);
        }

        return optimizedGroups;
    }

    /// <summary>
    /// Merges adjacent registers into optimized groups
    /// </summary>
    /// <param name="registers">Sorted list of registers</param>
    /// <param name="maxQuantity">Maximum quantity per group</param>
    /// <returns>List of merged register groups</returns>
    private List<MergedRegisterGroup> MergeAdjacentRegisters(List<RegisterDefinition> registers, ushort maxQuantity)
    {
        var mergedGroups = new List<MergedRegisterGroup>();
        
        if (!registers.Any()) return mergedGroups;

        var currentGroup = new MergedRegisterGroup
        {
            StartAddress = registers[0].Address,
            Registers = new List<RegisterDefinition> { registers[0] }
        };

        for (int i = 1; i < registers.Count; i++)
        {
            var currentRegister = registers[i];
            var lastRegister = currentGroup.Registers.Last();
            
            // Calculate the expected next address
            var expectedNextAddress = lastRegister.Address + lastRegister.RegisterCount;
            
            // Check if registers are adjacent and within limits
            if (currentRegister.Address == expectedNextAddress &&
                CanMergeRegisters(currentGroup, currentRegister, maxQuantity))
            {
                // Merge with current group
                currentGroup.Registers.Add(currentRegister);
            }
            else
            {
                // Finalize current group and start a new one
                currentGroup.Quantity = CalculateGroupQuantity(currentGroup);
                mergedGroups.Add(currentGroup);
                
                currentGroup = new MergedRegisterGroup
                {
                    StartAddress = currentRegister.Address,
                    Registers = new List<RegisterDefinition> { currentRegister }
                };
            }
        }

        // Add the last group
        currentGroup.Quantity = CalculateGroupQuantity(currentGroup);
        mergedGroups.Add(currentGroup);

        _logger.LogInformation("Merged {RegisterCount} registers into {GroupCount} optimized groups with max quantity {MaxQuantity}",
            registers.Count, mergedGroups.Count, maxQuantity);

        return mergedGroups;
    }

    /// <summary>
    /// Checks if two register groups can be merged
    /// </summary>
    /// <param name="currentGroup">Current register group</param>
    /// <param name="newRegister">New register to add</param>
    /// <param name="maxQuantity">Maximum allowed quantity</param>
    /// <returns>True if they can be merged</returns>
    private bool CanMergeRegisters(MergedRegisterGroup currentGroup, RegisterDefinition newRegister, ushort maxQuantity)
    {
        // Calculate total quantity if merged
        var totalQuantity = CalculateGroupQuantity(currentGroup) + newRegister.RegisterCount;

        // Check if it exceeds maximum quantity
        if (totalQuantity > maxQuantity)
        {
            _logger.LogDebug("Cannot merge register {Address}: total quantity {TotalQuantity} exceeds max {MaxQuantity}",
                newRegister.Address, totalQuantity, maxQuantity);
            return false;
        }

        // Check for register boundary crossing (e.g., don't cross 10000 boundary)
        var startAddress = currentGroup.StartAddress;
        var endAddress = newRegister.Address + newRegister.RegisterCount - 1;

        var crossesBoundary = CrossesRegisterBoundary(startAddress, (ushort)endAddress);
        if (crossesBoundary)
        {
            _logger.LogDebug("Cannot merge register {Address}: crosses register boundary from {StartAddress} to {EndAddress}",
                newRegister.Address, startAddress, endAddress);
        }

        return !crossesBoundary;
    }

    /// <summary>
    /// Calculates the total quantity for a register group
    /// </summary>
    /// <param name="group">Register group</param>
    /// <returns>Total quantity</returns>
    private ushort CalculateGroupQuantity(MergedRegisterGroup group)
    {
        if (!group.Registers.Any()) return 0;
        
        var startAddress = group.StartAddress;
        var lastRegister = group.Registers.Last();
        var endAddress = lastRegister.Address + lastRegister.RegisterCount;
        
        return (ushort)(endAddress - startAddress);
    }

    /// <summary>
    /// Checks if the address range crosses register boundaries
    /// </summary>
    /// <param name="startAddress">Start address</param>
    /// <param name="endAddress">End address</param>
    /// <returns>True if it crosses boundaries</returns>
    private bool CrossesRegisterBoundary(ushort startAddress, ushort endAddress)
    {
        // Define boundary points (these are common Modbus register boundaries)
        var boundaries = new ushort[] { 10000, 20000, 30000, 40000, 50000, 60000 };
        
        foreach (var boundary in boundaries)
        {
            if (startAddress < boundary && endAddress >= boundary)
                return true;
        }
        
        return false;
    }

    /// <summary>
    /// Gets the maximum polling quantity for a function type
    /// </summary>
    /// <param name="function">Modbus function code</param>
    /// <returns>Maximum polling quantity</returns>
    private ushort GetMaxPollingQuantity(ModbusFunction function)
    {
        return function switch
        {
            ModbusFunction.ReadCoils or ModbusFunction.ReadDiscreteInputs => 
                _configuration.MaxDigitalPollingQuantity,
            
            ModbusFunction.ReadHoldingRegisters or ModbusFunction.ReadInputRegisters => 
                _configuration.MaxAnalogPollingQuantity,
            
            _ => 1
        };
    }

    /// <summary>
    /// Validates an optimized register group
    /// </summary>
    /// <param name="group">Register group to validate</param>
    /// <returns>True if valid</returns>
    public bool ValidateOptimizedGroup(OptimizedRegisterGroup group)
    {
        if (group.Quantity == 0 || !group.Registers.Any())
            return false;

        var maxQuantity = GetMaxPollingQuantity(group.Function);
        if (group.Quantity > maxQuantity)
        {
            _logger.LogWarning("Register group exceeds maximum quantity: {Quantity} > {MaxQuantity}", 
                group.Quantity, maxQuantity);
            return false;
        }

        // Validate address continuity
        var sortedRegisters = group.Registers.OrderBy(r => r.Address).ToList();
        for (int i = 1; i < sortedRegisters.Count; i++)
        {
            var prev = sortedRegisters[i - 1];
            var current = sortedRegisters[i];
            
            if (current.Address != prev.Address + prev.RegisterCount)
            {
                _logger.LogWarning("Non-continuous addresses in register group: {PrevAddr}+{PrevCount} != {CurrentAddr}", 
                    prev.Address, prev.RegisterCount, current.Address);
                return false;
            }
        }

        return true;
    }
}

/// <summary>
/// Represents an optimized group of registers for polling
/// </summary>
public class OptimizedRegisterGroup
{
    /// <summary>
    /// Gets or sets the slave ID
    /// </summary>
    public byte SlaveId { get; set; }

    /// <summary>
    /// Gets or sets the function code
    /// </summary>
    public ModbusFunction Function { get; set; }

    /// <summary>
    /// Gets or sets the starting address
    /// </summary>
    public ushort StartAddress { get; set; }

    /// <summary>
    /// Gets or sets the quantity of registers to read
    /// </summary>
    public ushort Quantity { get; set; }

    /// <summary>
    /// Gets or sets the list of individual registers in this group
    /// </summary>
    public List<RegisterDefinition> Registers { get; set; } = new();

    /// <summary>
    /// Gets the end address of this group
    /// </summary>
    public ushort EndAddress => (ushort)(StartAddress + Quantity - 1);

    /// <summary>
    /// Checks if this group contains a specific register
    /// </summary>
    /// <param name="address">Register address</param>
    /// <returns>True if the group contains the register</returns>
    public bool ContainsAddress(ushort address)
    {
        return address >= StartAddress && address <= EndAddress;
    }

    /// <summary>
    /// Gets the register definition for a specific address
    /// </summary>
    /// <param name="address">Register address</param>
    /// <returns>Register definition or null if not found</returns>
    public RegisterDefinition? GetRegisterAt(ushort address)
    {
        return Registers.FirstOrDefault(r => r.Address <= address && 
                                           address < r.Address + r.RegisterCount);
    }
}

/// <summary>
/// Internal class for merging registers
/// </summary>
internal class MergedRegisterGroup
{
    public ushort StartAddress { get; set; }
    public ushort Quantity { get; set; }
    public List<RegisterDefinition> Registers { get; set; } = new();
}
