using Microsoft.Extensions.Logging;
using Ngp.Communication.ModbusTcpMaster.Enums;
using Ngp.Communication.ModbusTcpMaster.Events;
using Ngp.Communication.ModbusTcpMaster.Models;
using System.Collections.Concurrent;
using System.Net.Sockets;

namespace Ngp.Communication.ModbusTcpMaster.Connection;

/// <summary>
/// Manages TCP connections for Modbus communication with retry and reconnection logic
/// </summary>
public class ConnectionManager : IDisposable
{
    private readonly EndpointConfiguration _configuration;
    private readonly ILogger<ConnectionManager> _logger;
    private readonly SemaphoreSlim _connectionSemaphore;
    private readonly CancellationTokenSource _cancellationTokenSource;
    private readonly Timer _reconnectTimer;
    
    private TcpClient? _tcpClient;
    private NetworkStream? _networkStream;
    private ConnectionState _connectionState = ConnectionState.Disconnected;
    private DateTime _lastConnectionAttempt = DateTime.MinValue;
    private int _consecutiveFailures = 0;
    private bool _disposed = false;

    /// <summary>
    /// Event raised when connection state changes
    /// </summary>
    public event EventHandler<ConnectionStateChangedEventArgs>? ConnectionStateChanged;

    /// <summary>
    /// Gets the current connection state
    /// </summary>
    public ConnectionState State => _connectionState;

    /// <summary>
    /// Gets the network stream if connected
    /// </summary>
    public NetworkStream? NetworkStream => _networkStream;

    /// <summary>
    /// Gets whether the connection is active
    /// </summary>
    public bool IsConnected => _connectionState == ConnectionState.Connected && 
                              _tcpClient?.Connected == true && 
                              _networkStream != null;

    /// <summary>
    /// Initializes a new instance of the ConnectionManager class
    /// </summary>
    /// <param name="configuration">Endpoint configuration</param>
    /// <param name="logger">Logger instance</param>
    public ConnectionManager(EndpointConfiguration configuration, ILogger<ConnectionManager> logger)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        _connectionSemaphore = new SemaphoreSlim(1, 1);
        _cancellationTokenSource = new CancellationTokenSource();
        
        // Setup reconnect timer
        _reconnectTimer = new Timer(ReconnectTimerCallback, null, Timeout.Infinite, Timeout.Infinite);
    }

    /// <summary>
    /// Connects to the endpoint
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if connected successfully</returns>
    public async Task<bool> ConnectAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed) return false;

        await _connectionSemaphore.WaitAsync(cancellationToken);
        try
        {
            if (IsConnected) return true;

            return await InternalConnectAsync(cancellationToken);
        }
        finally
        {
            _connectionSemaphore.Release();
        }
    }

    /// <summary>
    /// Disconnects from the endpoint
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task DisconnectAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed) return;

        await _connectionSemaphore.WaitAsync(cancellationToken);
        try
        {
            await InternalDisconnectAsync();
        }
        finally
        {
            _connectionSemaphore.Release();
        }
    }

    /// <summary>
    /// Ensures the connection is active, reconnecting if necessary
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if connection is active</returns>
    public async Task<bool> EnsureConnectedAsync(CancellationToken cancellationToken = default)
    {
        if (IsConnected) return true;

        return await ConnectAsync(cancellationToken);
    }

    /// <summary>
    /// Internal connection logic
    /// </summary>
    private async Task<bool> InternalConnectAsync(CancellationToken cancellationToken)
    {
        try
        {
            SetConnectionState(ConnectionState.Connecting);
            _lastConnectionAttempt = DateTime.UtcNow;

            // Clean up existing connection
            await InternalDisconnectAsync();

            // Create new TCP client with timeout
            _tcpClient = new TcpClient();
            
            // Set socket options for better performance
            _tcpClient.NoDelay = true;
            _tcpClient.ReceiveTimeout = _configuration.ReadTimeoutMilliseconds;
            _tcpClient.SendTimeout = _configuration.WriteTimeoutMilliseconds;

            // Connect with timeout
            using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            timeoutCts.CancelAfter(TimeSpan.FromMilliseconds(_configuration.ReadTimeoutMilliseconds));

            await _tcpClient.ConnectAsync(_configuration.IpAddress, _configuration.Port, timeoutCts.Token);
            _networkStream = _tcpClient.GetStream();

            // Reset failure counter on successful connection
            _consecutiveFailures = 0;
            SetConnectionState(ConnectionState.Connected);

            _logger.LogInformation("Connected to {EndpointId} ({IpAddress}:{Port})", 
                _configuration.Id, _configuration.IpAddress, _configuration.Port);

            return true;
        }
        catch (Exception ex)
        {
            _consecutiveFailures++;
            var errorMessage = $"Failed to connect to {_configuration.Id} ({_configuration.IpAddress}:{_configuration.Port}): {ex.Message}";
            
            _logger.LogError(ex, "Connection failed to {EndpointId} (attempt {Attempt})", 
                _configuration.Id, _consecutiveFailures);

            SetConnectionState(ConnectionState.Error, errorMessage);
            
            // Schedule reconnection if not cancelled (infinite retry for robust connection management)
            if (!cancellationToken.IsCancellationRequested)
            {
                ScheduleReconnect();
            }

            return false;
        }
    }

    /// <summary>
    /// Internal disconnection logic
    /// </summary>
    private async Task InternalDisconnectAsync()
    {
        try
        {
            if (_connectionState == ConnectionState.Connected)
            {
                SetConnectionState(ConnectionState.Disconnecting);
            }

            // Stop reconnect timer
            _reconnectTimer.Change(Timeout.Infinite, Timeout.Infinite);

            // Close network stream
            if (_networkStream != null)
            {
                await _networkStream.FlushAsync();
                _networkStream.Close();
                _networkStream.Dispose();
                _networkStream = null;
            }

            // Close TCP client
            if (_tcpClient != null)
            {
                _tcpClient.Close();
                _tcpClient.Dispose();
                _tcpClient = null;
            }

            SetConnectionState(ConnectionState.Disconnected);

            _logger.LogInformation("Disconnected from {EndpointId} ({IpAddress}:{Port})", 
                _configuration.Id, _configuration.IpAddress, _configuration.Port);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error during disconnect from {EndpointId}", _configuration.Id);
        }
    }

    /// <summary>
    /// Schedules a reconnection attempt
    /// </summary>
    private void ScheduleReconnect()
    {
        if (_disposed) return;

        var delay = CalculateReconnectDelay();
        SetConnectionState(ConnectionState.Retrying);
        
        _logger.LogInformation("Scheduling reconnect to {EndpointId} in {Delay}ms (attempt {Attempt})",
            _configuration.Id, delay, _consecutiveFailures);

        _reconnectTimer.Change(delay, Timeout.Infinite);
    }

    /// <summary>
    /// Calculates the delay before next reconnection attempt using exponential backoff
    /// </summary>
    private int CalculateReconnectDelay()
    {
        var baseDelay = _configuration.RetryDelayMilliseconds;
        var exponentialDelay = baseDelay * Math.Pow(2, Math.Min(_consecutiveFailures - 1, 5)); // Cap at 2^5
        var maxDelay = Math.Min(exponentialDelay, 30000); // Cap at 30 seconds
        
        return (int)maxDelay;
    }

    /// <summary>
    /// Timer callback for reconnection attempts
    /// </summary>
    private async void ReconnectTimerCallback(object? state)
    {
        if (_disposed) return;

        try
        {
            await ConnectAsync(_cancellationTokenSource.Token);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during scheduled reconnect to {EndpointId}", _configuration.Id);
        }
    }

    /// <summary>
    /// Sets the connection state and raises the event
    /// </summary>
    private void SetConnectionState(ConnectionState newState, string? errorMessage = null)
    {
        var previousState = _connectionState;
        _connectionState = newState;

        if (previousState != newState)
        {
            var eventArgs = new ConnectionStateChangedEventArgs(
                _configuration.Id, previousState, newState, errorMessage);
            
            ConnectionStateChanged?.Invoke(this, eventArgs);
        }
    }

    /// <summary>
    /// Handles connection errors and triggers reconnection if needed
    /// </summary>
    /// <param name="exception">The exception that occurred</param>
    public async Task HandleConnectionErrorAsync(Exception exception)
    {
        if (_disposed) return;

        _logger.LogError(exception, "Connection error occurred for {EndpointId}", _configuration.Id);

        await _connectionSemaphore.WaitAsync();
        try
        {
            if (_connectionState == ConnectionState.Connected)
            {
                SetConnectionState(ConnectionState.Error, exception.Message);
                await InternalDisconnectAsync();
                
                // Schedule reconnection (infinite retry for robust connection management)
                ScheduleReconnect();
            }
        }
        finally
        {
            _connectionSemaphore.Release();
        }
    }

    /// <summary>
    /// Gets connection statistics
    /// </summary>
    /// <returns>Connection statistics</returns>
    public ConnectionStatistics GetStatistics()
    {
        return new ConnectionStatistics
        {
            EndpointId = _configuration.Id,
            State = _connectionState,
            ConsecutiveFailures = _consecutiveFailures,
            LastConnectionAttempt = _lastConnectionAttempt,
            IsConnected = IsConnected
        };
    }

    /// <summary>
    /// Disposes the connection manager
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _disposed = true;
            
            _cancellationTokenSource.Cancel();
            _reconnectTimer?.Dispose();
            
            try
            {
                InternalDisconnectAsync().Wait(5000); // Wait up to 5 seconds for graceful disconnect
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error during dispose of ConnectionManager for {EndpointId}", _configuration.Id);
            }
            
            _connectionSemaphore?.Dispose();
            _cancellationTokenSource?.Dispose();
        }
    }
}

/// <summary>
/// Connection statistics information
/// </summary>
public class ConnectionStatistics
{
    /// <summary>
    /// Gets or sets the endpoint identifier
    /// </summary>
    public string EndpointId { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the current connection state
    /// </summary>
    public ConnectionState State { get; set; }

    /// <summary>
    /// Gets or sets the number of consecutive failures
    /// </summary>
    public int ConsecutiveFailures { get; set; }

    /// <summary>
    /// Gets or sets the timestamp of the last connection attempt
    /// </summary>
    public DateTime LastConnectionAttempt { get; set; }

    /// <summary>
    /// Gets or sets whether the connection is currently active
    /// </summary>
    public bool IsConnected { get; set; }
}
