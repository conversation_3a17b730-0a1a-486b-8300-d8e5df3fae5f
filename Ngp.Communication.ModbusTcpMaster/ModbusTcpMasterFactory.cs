using Microsoft.Extensions.Logging;
using Ngp.Communication.ModbusTcpMaster.Builders;
using Ngp.Communication.ModbusTcpMaster.Interfaces;

namespace Ngp.Communication.ModbusTcpMaster;

/// <summary>
/// Factory class for creating Modbus TCP Master instances using Fluent API
/// </summary>
public static class ModbusTcpMasterFactory
{
    /// <summary>
    /// Creates a new Modbus TCP Master builder
    /// </summary>
    /// <param name="loggerFactory">Optional logger factory</param>
    /// <returns>A new builder instance</returns>
    public static IModbusTcpMasterBuilder Create(ILoggerFactory? loggerFactory = null)
    {
        return new ModbusTcpMasterBuilder(loggerFactory);
    }

    /// <summary>
    /// Creates a new Modbus TCP Master builder with endpoint configuration
    /// </summary>
    /// <param name="ipAddress">IP address of the endpoint</param>
    /// <param name="port">Port number (default: 502)</param>
    /// <param name="loggerFactory">Optional logger factory</param>
    /// <returns>A new builder instance with endpoint configured</returns>
    public static IModbusTcpMasterBuilder CreateForEndpoint(string ipAddress, ushort port = 502, ILoggerFactory? loggerFactory = null)
    {
        return new ModbusTcpMasterBuilder(loggerFactory)
            .WithEndpoint(ipAddress, port);
    }

    /// <summary>
    /// Creates a new Modbus TCP Master builder with endpoint and ID configuration
    /// </summary>
    /// <param name="id">Endpoint identifier</param>
    /// <param name="ipAddress">IP address of the endpoint</param>
    /// <param name="port">Port number (default: 502)</param>
    /// <param name="loggerFactory">Optional logger factory</param>
    /// <returns>A new builder instance with endpoint and ID configured</returns>
    public static IModbusTcpMasterBuilder CreateForEndpoint(string id, string ipAddress, ushort port = 502, ILoggerFactory? loggerFactory = null)
    {
        return new ModbusTcpMasterBuilder(loggerFactory)
            .WithId(id)
            .WithEndpoint(ipAddress, port);
    }
}
