using Microsoft.Extensions.Logging;
using Ngp.Communication.ModbusTcpMaster.Commands;
using Ngp.Communication.ModbusTcpMaster.Enums;
using Ngp.Communication.ModbusTcpMaster.Events;
using Ngp.Communication.ModbusTcpMaster.Models;
using Ngp.Communication.ModbusTcpMaster.Optimization;
using Ngp.Communication.ModbusTcpMaster.Utilities;
using System.Collections.Concurrent;

namespace Ngp.Communication.ModbusTcpMaster.Polling;

/// <summary>
/// Manages background polling of Modbus registers and triggers events on value changes
/// </summary>
public class PollingEngine : IDisposable
{
    private readonly EndpointConfiguration _configuration;
    private readonly ModbusCommandProcessor _commandProcessor;
    private readonly PacketOptimizer _optimizer;
    private readonly ILogger<PollingEngine> _logger;
    
    private readonly ConcurrentDictionary<string, RegisterValue> _registerValues;
    private readonly ConcurrentDictionary<string, DateTime> _debounceTimestamps;
    private readonly ConcurrentQueue<WriteRequest> _writeQueue;
    private readonly SemaphoreSlim _pollingSemaphore;
    private readonly CancellationTokenSource _cancellationTokenSource;
    
    private List<OptimizedRegisterGroup> _optimizedGroups = new();
    private Task? _pollingTask;
    private Task? _writeProcessingTask;
    private int _currentGroupIndex = 0;
    private bool _disposed = false;

    /// <summary>
    /// Event raised when a register value changes
    /// </summary>
    public event EventHandler<RegisterValueChangedEventArgs>? RegisterValueChanged;

    /// <summary>
    /// Event raised when a Modbus error occurs
    /// </summary>
    public event EventHandler<ModbusErrorEventArgs>? ModbusError;

    /// <summary>
    /// Gets whether the polling engine is running
    /// </summary>
    public bool IsRunning => _pollingTask != null && !_pollingTask.IsCompleted;

    /// <summary>
    /// Initializes a new instance of the PollingEngine class
    /// </summary>
    /// <param name="configuration">Endpoint configuration</param>
    /// <param name="commandProcessor">Command processor</param>
    /// <param name="optimizer">Packet optimizer</param>
    /// <param name="logger">Logger instance</param>
    public PollingEngine(
        EndpointConfiguration configuration,
        ModbusCommandProcessor commandProcessor,
        PacketOptimizer optimizer,
        ILogger<PollingEngine> logger)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _commandProcessor = commandProcessor ?? throw new ArgumentNullException(nameof(commandProcessor));
        _optimizer = optimizer ?? throw new ArgumentNullException(nameof(optimizer));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        _registerValues = new ConcurrentDictionary<string, RegisterValue>();
        _debounceTimestamps = new ConcurrentDictionary<string, DateTime>();
        _writeQueue = new ConcurrentQueue<WriteRequest>();
        _pollingSemaphore = new SemaphoreSlim(1, 1);
        _cancellationTokenSource = new CancellationTokenSource();
    }

    /// <summary>
    /// Sets the registers to poll
    /// </summary>
    /// <param name="registers">List of register definitions</param>
    public void SetRegisters(IEnumerable<RegisterDefinition> registers)
    {
        var registerList = registers.ToList();
        _logger.LogInformation("SetRegisters called with {RegisterCount} registers", registerList.Count);

        _optimizedGroups = _optimizer.OptimizeRegisters(registerList);
        _currentGroupIndex = 0;

        _logger.LogInformation("Configured {GroupCount} optimized register groups for polling",
            _optimizedGroups.Count);

        // Log details of each group
        foreach (var group in _optimizedGroups)
        {
            _logger.LogInformation("Group: Slave={SlaveId}, Function={Function}, StartAddress={StartAddress}, Quantity={Quantity}, RegisterCount={RegisterCount}",
                group.SlaveId, group.Function, group.StartAddress, group.Quantity, group.Registers.Count);
        }
    }

    /// <summary>
    /// Starts the polling engine
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(PollingEngine));

        await _pollingSemaphore.WaitAsync(cancellationToken);
        try
        {
            if (IsRunning) return;

            _logger.LogInformation("Starting polling engine for {EndpointId}", _configuration.Id);

            // Start polling task
            _pollingTask = Task.Run(async () => await PollingLoopAsync(_cancellationTokenSource.Token), 
                _cancellationTokenSource.Token);

            // Start write processing task
            _writeProcessingTask = Task.Run(async () => await WriteProcessingLoopAsync(_cancellationTokenSource.Token), 
                _cancellationTokenSource.Token);
        }
        finally
        {
            _pollingSemaphore.Release();
        }
    }

    /// <summary>
    /// Stops the polling engine
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed) return;

        await _pollingSemaphore.WaitAsync(cancellationToken);
        try
        {
            if (!IsRunning) return;

            _logger.LogInformation("Stopping polling engine for {EndpointId}", _configuration.Id);

            _cancellationTokenSource.Cancel();

            // Wait for tasks to complete
            var tasks = new List<Task>();
            if (_pollingTask != null) tasks.Add(_pollingTask);
            if (_writeProcessingTask != null) tasks.Add(_writeProcessingTask);

            if (tasks.Any())
            {
                await Task.WhenAll(tasks);
            }

            _pollingTask = null;
            _writeProcessingTask = null;
        }
        finally
        {
            _pollingSemaphore.Release();
        }
    }

    /// <summary>
    /// Queues a write request
    /// </summary>
    /// <param name="writeRequest">Write request to queue</param>
    public void QueueWrite(WriteRequest writeRequest)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(PollingEngine));

        if (!writeRequest.IsValid())
        {
            _logger.LogWarning("Invalid write request: SlaveId={SlaveId}, Address={Address}, Function={Function}", 
                writeRequest.SlaveId, writeRequest.Address, writeRequest.Function);
            return;
        }

        _writeQueue.Enqueue(writeRequest);
        _logger.LogDebug("Queued write request: SlaveId={SlaveId}, Address={Address}, Function={Function}", 
            writeRequest.SlaveId, writeRequest.Address, writeRequest.Function);
    }

    /// <summary>
    /// Gets the current value of a register
    /// </summary>
    /// <param name="slaveId">Slave ID</param>
    /// <param name="address">Register address</param>
    /// <param name="function">Function code</param>
    /// <returns>Current register value or null if not available</returns>
    public RegisterValue? GetCurrentValue(byte slaveId, ushort address, ModbusFunction function)
    {
        var key = CreateRegisterKey(slaveId, address, function);
        return _registerValues.TryGetValue(key, out var value) ? value : null;
    }

    /// <summary>
    /// Main polling loop
    /// </summary>
    private async Task PollingLoopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Polling loop started for {EndpointId}", _configuration.Id);

        try
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                // Check if there are any write requests to process first
                if (!_writeQueue.IsEmpty)
                {
                    // Let write processing take priority
                    await Task.Delay(10, cancellationToken);
                    continue;
                }

                // Poll next group
                if (_optimizedGroups.Any())
                {
                    await PollNextGroupAsync(cancellationToken);
                }

                // Wait for poll delay
                if (_configuration.PollDelayMilliseconds > 0)
                {
                    await Task.Delay(_configuration.PollDelayMilliseconds, cancellationToken);
                }
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Polling loop cancelled for {EndpointId}", _configuration.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in polling loop for {EndpointId}", _configuration.Id);
        }
    }

    /// <summary>
    /// Polls the next register group
    /// </summary>
    private async Task PollNextGroupAsync(CancellationToken cancellationToken)
    {
        if (!_optimizedGroups.Any()) return;

        var group = _optimizedGroups[_currentGroupIndex];
        
        try
        {
            await PollRegisterGroupAsync(group, cancellationToken);
        }
        catch (Exception ex)
        {
            var errorArgs = new ModbusErrorEventArgs(
                _configuration.Id,
                group.SlaveId,
                group.Function,
                $"Error polling register group: {ex.Message}",
                exception: ex);

            ModbusError?.Invoke(this, errorArgs);
        }

        // Move to next group (round-robin)
        _currentGroupIndex = (_currentGroupIndex + 1) % _optimizedGroups.Count;
    }

    /// <summary>
    /// Polls a specific register group
    /// </summary>
    private async Task PollRegisterGroupAsync(OptimizedRegisterGroup group, CancellationToken cancellationToken)
    {
        // _logger.LogInformation("Polling group: Slave={SlaveId}, Function={Function}, StartAddress={StartAddress}, Quantity={Quantity}, RegisterCount={RegisterCount}",
            // group.SlaveId, group.Function, group.StartAddress, group.Quantity, group.Registers.Count);

        byte[]? rawData = null;

        // Execute the appropriate read command
        switch (group.Function)
        {
            case ModbusFunction.ReadCoils:
                var coils = await _commandProcessor.ReadCoilsAsync(group.SlaveId, group.StartAddress, group.Quantity, cancellationToken);
                if (coils != null)
                {
                    rawData = ConvertBoolArrayToBytes(coils);
                }
                break;

            case ModbusFunction.ReadDiscreteInputs:
                var inputs = await _commandProcessor.ReadDiscreteInputsAsync(group.SlaveId, group.StartAddress, group.Quantity, cancellationToken);
                if (inputs != null)
                {
                    rawData = ConvertBoolArrayToBytes(inputs);
                }
                break;

            case ModbusFunction.ReadHoldingRegisters:
                var holdingRegs = await _commandProcessor.ReadHoldingRegistersAsync(group.SlaveId, group.StartAddress, group.Quantity, cancellationToken);
                if (holdingRegs != null)
                {
                    rawData = ConvertUshortArrayToBytes(holdingRegs);
                }
                break;

            case ModbusFunction.ReadInputRegisters:
                var inputRegs = await _commandProcessor.ReadInputRegistersAsync(group.SlaveId, group.StartAddress, group.Quantity, cancellationToken);
                if (inputRegs != null)
                {
                    rawData = ConvertUshortArrayToBytes(inputRegs);
                }
                break;
        }

        if (rawData != null)
        {
            ProcessPolledData(group, rawData);
        }
    }

    /// <summary>
    /// Processes polled data and triggers events for changed values
    /// </summary>
    private void ProcessPolledData(OptimizedRegisterGroup group, byte[] rawData)
    {
        foreach (var register in group.Registers)
        {
            try
            {
                // Extract data for this specific register
                var registerData = ExtractRegisterData(rawData, group, register);

                // Skip this register if data extraction failed (insufficient data)
                if (registerData == null)
                {
                    _logger.LogWarning("Skipping register due to insufficient data: SlaveId={SlaveId}, Address={Address}, Function={Function}",
                        register.SlaveId, register.Address, register.Function);
                    continue;
                }

                // Check for value changes
                var key = CreateRegisterKey(register.SlaveId, register.Address, register.Function);
                var previousValue = _registerValues.TryGetValue(key, out var prev) ? prev : null;
                
                // Apply debouncing if enabled
                if (register.EnableDebounce && ShouldDebounce(key, registerData, previousValue?.RawData))
                {
                    continue;
                }

                // Check if value has changed
                if (HasValueChanged(registerData, previousValue?.RawData))
                {
                    // Convert to typed value
                    var convertedValue = DataConverter.ConvertFromBytes(registerData, register.DataType, register.EndianType);
                    var previousConvertedValue = previousValue?.RawData != null ? 
                        DataConverter.ConvertFromBytes(previousValue.RawData, register.DataType, register.EndianType) : null;

                    // Update stored value
                    var newValue = new RegisterValue
                    {
                        SlaveId = register.SlaveId,
                        Address = register.Address,
                        Function = register.Function,
                        RawData = registerData,
                        ConvertedValue = convertedValue,
                        Timestamp = DateTime.UtcNow,
                        DataType = register.DataType,
                        EndianType = register.EndianType
                    };

                    _registerValues[key] = newValue;

                    // Trigger value changed event
                    var eventArgs = new RegisterValueChangedEventArgs(
                        _configuration.Id,
                        register.SlaveId,
                        register.Address,
                        register.Function,
                        previousValue?.RawData ?? Array.Empty<byte>(),
                        registerData,
                        register.DataType,
                        register.EndianType,
                        previousConvertedValue,
                        convertedValue);

                    RegisterValueChanged?.Invoke(this, eventArgs);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing register data: SlaveId={SlaveId}, Address={Address}, Function={Function}", 
                    register.SlaveId, register.Address, register.Function);
            }
        }
    }

    /// <summary>
    /// Write processing loop
    /// </summary>
    private async Task WriteProcessingLoopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Write processing loop started for {EndpointId}", _configuration.Id);

        try
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                if (_writeQueue.TryDequeue(out var writeRequest))
                {
                    await ProcessWriteRequestAsync(writeRequest, cancellationToken);
                }
                else
                {
                    await Task.Delay(10, cancellationToken);
                }
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Write processing loop cancelled for {EndpointId}", _configuration.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in write processing loop for {EndpointId}", _configuration.Id);
        }
    }

    /// <summary>
    /// Processes a write request
    /// </summary>
    private async Task ProcessWriteRequestAsync(WriteRequest writeRequest, CancellationToken cancellationToken)
    {
        try
        {
            bool success = false;

            switch (writeRequest.Function)
            {
                case ModbusFunction.WriteSingleCoil:
                    success = await _commandProcessor.WriteSingleCoilAsync(
                        writeRequest.SlaveId, writeRequest.Address, (bool)writeRequest.Value, cancellationToken);
                    break;

                case ModbusFunction.WriteSingleRegister:
                    success = await _commandProcessor.WriteSingleRegisterAsync(
                        writeRequest.SlaveId, writeRequest.Address, (ushort)writeRequest.Value, cancellationToken);
                    break;

                case ModbusFunction.WriteMultipleCoils:
                    success = await _commandProcessor.WriteMultipleCoilsAsync(
                        writeRequest.SlaveId, writeRequest.Address, (bool[])writeRequest.Value, cancellationToken);
                    break;

                case ModbusFunction.WriteMultipleRegisters:
                    success = await _commandProcessor.WriteMultipleRegistersAsync(
                        writeRequest.SlaveId, writeRequest.Address, (ushort[])writeRequest.Value, cancellationToken);
                    break;
            }

            // Complete the write request
            writeRequest.CompletionSource?.SetResult(success);

            if (success)
            {
                _logger.LogDebug("Write request completed successfully: SlaveId={SlaveId}, Address={Address}, Function={Function}", 
                    writeRequest.SlaveId, writeRequest.Address, writeRequest.Function);
            }
            else
            {
                _logger.LogWarning("Write request failed: SlaveId={SlaveId}, Address={Address}, Function={Function}", 
                    writeRequest.SlaveId, writeRequest.Address, writeRequest.Function);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing write request: SlaveId={SlaveId}, Address={Address}, Function={Function}", 
                writeRequest.SlaveId, writeRequest.Address, writeRequest.Function);

            writeRequest.CompletionSource?.SetException(ex);
        }
    }

    /// <summary>
    /// Creates a unique key for a register
    /// </summary>
    private static string CreateRegisterKey(byte slaveId, ushort address, ModbusFunction function)
    {
        return $"{slaveId}:{address}:{(int)function}";
    }

    /// <summary>
    /// Checks if debouncing should be applied
    /// </summary>
    private bool ShouldDebounce(string key, byte[] newData, byte[]? previousData)
    {
        if (previousData == null || !HasValueChanged(newData, previousData))
            return false;

        var now = DateTime.UtcNow;
        if (_debounceTimestamps.TryGetValue(key, out var lastChange))
        {
            var timeSinceLastChange = now - lastChange;
            if (timeSinceLastChange.TotalMilliseconds < _configuration.DebounceTimeMilliseconds)
            {
                return true; // Still within debounce period
            }
        }

        _debounceTimestamps[key] = now;
        return false;
    }

    /// <summary>
    /// Checks if a value has changed
    /// </summary>
    private static bool HasValueChanged(byte[] newData, byte[]? previousData)
    {
        if (previousData == null) return true;
        if (newData.Length != previousData.Length) return true;
        
        return !newData.SequenceEqual(previousData);
    }

    /// <summary>
    /// Extracts register data from raw polling data
    /// </summary>
    /// <returns>Register data bytes, or null if insufficient data available</returns>
    private static byte[]? ExtractRegisterData(byte[] rawData, OptimizedRegisterGroup group, RegisterDefinition register)
    {
        switch (group.Function)
        {
            case ModbusFunction.ReadCoils:
            case ModbusFunction.ReadDiscreteInputs:
                // For coils/discrete inputs, extract the specific bit
                var bitIndex = register.Address - group.StartAddress;
                var byteIndex = bitIndex / 8;
                var bitPosition = bitIndex % 8;

                if (byteIndex >= rawData.Length)
                {
                    throw new ArgumentException($"Insufficient data for coil at bit index {bitIndex}");
                }

                var bitValue = (rawData[byteIndex] & (1 << bitPosition)) != 0;
                return new byte[] { (byte)(bitValue ? 1 : 0) };

            case ModbusFunction.ReadHoldingRegisters:
            case ModbusFunction.ReadInputRegisters:
                // For registers, calculate byte offset (2 bytes per register)
                var registerOffset = (register.Address - group.StartAddress) * 2;
                var dataSize = register.RegisterCount * 2;

                if (registerOffset + dataSize > rawData.Length)
                {
                    // If insufficient data for this register (likely the last one in a group),
                    // return null to indicate this register should be skipped
                    return null;
                }

                var result = new byte[dataSize];
                Array.Copy(rawData, registerOffset, result, 0, dataSize);
                return result;

            default:
                throw new ArgumentException($"Unsupported function code: {group.Function}");
        }
    }

    /// <summary>
    /// Converts boolean array to byte array
    /// </summary>
    private static byte[] ConvertBoolArrayToBytes(bool[] values)
    {
        var byteCount = (values.Length + 7) / 8;
        var result = new byte[byteCount];
        
        for (int i = 0; i < values.Length; i++)
        {
            if (values[i])
            {
                var byteIndex = i / 8;
                var bitIndex = i % 8;
                result[byteIndex] |= (byte)(1 << bitIndex);
            }
        }
        
        return result;
    }

    /// <summary>
    /// Converts ushort array to byte array
    /// </summary>
    private static byte[] ConvertUshortArrayToBytes(ushort[] values)
    {
        var result = new byte[values.Length * 2];
        for (int i = 0; i < values.Length; i++)
        {
            var bytes = BitConverter.GetBytes(values[i]);
            if (BitConverter.IsLittleEndian)
            {
                Array.Reverse(bytes); // Convert to big-endian
            }
            Array.Copy(bytes, 0, result, i * 2, 2);
        }
        return result;
    }

    /// <summary>
    /// Disposes the polling engine
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _disposed = true;
            
            try
            {
                StopAsync().Wait(5000);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error during dispose of PollingEngine for {EndpointId}", _configuration.Id);
            }
            
            _cancellationTokenSource?.Dispose();
            _pollingSemaphore?.Dispose();
        }
    }
}

/// <summary>
/// Represents a register value with metadata
/// </summary>
public class RegisterValue
{
    /// <summary>
    /// Gets or sets the slave ID
    /// </summary>
    public byte SlaveId { get; set; }

    /// <summary>
    /// Gets or sets the register address
    /// </summary>
    public ushort Address { get; set; }

    /// <summary>
    /// Gets or sets the function code
    /// </summary>
    public ModbusFunction Function { get; set; }

    /// <summary>
    /// Gets or sets the raw data bytes
    /// </summary>
    public byte[] RawData { get; set; } = Array.Empty<byte>();

    /// <summary>
    /// Gets or sets the converted value
    /// </summary>
    public object? ConvertedValue { get; set; }

    /// <summary>
    /// Gets or sets the timestamp when the value was read
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// Gets or sets the data type
    /// </summary>
    public Type DataType { get; set; } = typeof(ushort);

    /// <summary>
    /// Gets or sets the endian type
    /// </summary>
    public EndianType EndianType { get; set; }
}
