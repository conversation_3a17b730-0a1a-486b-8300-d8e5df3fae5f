using Ngp.Communication.ModbusTcpMaster.Enums;

namespace Ngp.Communication.ModbusTcpMaster.Events;

/// <summary>
/// Event arguments for Modbus errors
/// </summary>
public class ModbusErrorEventArgs : EventArgs
{
    /// <summary>
    /// Gets the endpoint identifier
    /// </summary>
    public string EndpointId { get; }

    /// <summary>
    /// Gets the slave ID
    /// </summary>
    public byte SlaveId { get; }

    /// <summary>
    /// Gets the Modbus function code that caused the error
    /// </summary>
    public ModbusFunction Function { get; }

    /// <summary>
    /// Gets the error code returned by the slave
    /// </summary>
    public byte? ErrorCode { get; }

    /// <summary>
    /// Gets the error message
    /// </summary>
    public string ErrorMessage { get; }

    /// <summary>
    /// Gets the exception that caused the error, if any
    /// </summary>
    public Exception? Exception { get; }

    /// <summary>
    /// Gets the timestamp when the error occurred
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// Gets the raw request data that caused the error
    /// </summary>
    public byte[]? RequestData { get; }

    /// <summary>
    /// Gets the raw response data that contained the error
    /// </summary>
    public byte[]? ResponseData { get; }

    /// <summary>
    /// Initializes a new instance of the ModbusErrorEventArgs class
    /// </summary>
    public ModbusErrorEventArgs(
        string endpointId,
        byte slaveId,
        ModbusFunction function,
        string errorMessage,
        byte? errorCode = null,
        Exception? exception = null,
        byte[]? requestData = null,
        byte[]? responseData = null)
    {
        EndpointId = endpointId;
        SlaveId = slaveId;
        Function = function;
        ErrorMessage = errorMessage;
        ErrorCode = errorCode;
        Exception = exception;
        Timestamp = DateTime.UtcNow;
        RequestData = requestData;
        ResponseData = responseData;
    }
}
