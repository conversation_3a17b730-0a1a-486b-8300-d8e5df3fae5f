using Ngp.Communication.ModbusTcpMaster.Enums;

namespace Ngp.Communication.ModbusTcpMaster.Events;

/// <summary>
/// Event arguments for register value changes
/// </summary>
public class RegisterValueChangedEventArgs : EventArgs
{
    /// <summary>
    /// Gets the endpoint identifier
    /// </summary>
    public string EndpointId { get; }

    /// <summary>
    /// Gets the slave ID
    /// </summary>
    public byte SlaveId { get; }

    /// <summary>
    /// Gets the register address
    /// </summary>
    public ushort Address { get; }

    /// <summary>
    /// Gets the Modbus function code
    /// </summary>
    public ModbusFunction Function { get; }

    /// <summary>
    /// Gets the previous value as raw bytes
    /// </summary>
    public byte[] PreviousValue { get; }

    /// <summary>
    /// Gets the current value as raw bytes
    /// </summary>
    public byte[] CurrentValue { get; }

    /// <summary>
    /// Gets the data type of the value
    /// </summary>
    public Type DataType { get; }

    /// <summary>
    /// Gets the endian type used for conversion
    /// </summary>
    public EndianType EndianType { get; }

    /// <summary>
    /// Gets the timestamp when the value change occurred
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// Gets the converted previous value
    /// </summary>
    public object? PreviousConvertedValue { get; }

    /// <summary>
    /// Gets the converted current value
    /// </summary>
    public object? CurrentConvertedValue { get; }

    /// <summary>
    /// Initializes a new instance of the RegisterValueChangedEventArgs class
    /// </summary>
    public RegisterValueChangedEventArgs(
        string endpointId,
        byte slaveId,
        ushort address,
        ModbusFunction function,
        byte[] previousValue,
        byte[] currentValue,
        Type dataType,
        EndianType endianType,
        object? previousConvertedValue,
        object? currentConvertedValue)
    {
        EndpointId = endpointId;
        SlaveId = slaveId;
        Address = address;
        Function = function;
        PreviousValue = previousValue;
        CurrentValue = currentValue;
        DataType = dataType;
        EndianType = endianType;
        Timestamp = DateTime.UtcNow;
        PreviousConvertedValue = previousConvertedValue;
        CurrentConvertedValue = currentConvertedValue;
    }
}
