using Ngp.Communication.ModbusTcpMaster.Enums;

namespace Ngp.Communication.ModbusTcpMaster.Events;

/// <summary>
/// Event arguments for connection state changes
/// </summary>
public class ConnectionStateChangedEventArgs : EventArgs
{
    /// <summary>
    /// Gets the endpoint identifier
    /// </summary>
    public string EndpointId { get; }

    /// <summary>
    /// Gets the previous connection state
    /// </summary>
    public ConnectionState PreviousState { get; }

    /// <summary>
    /// Gets the current connection state
    /// </summary>
    public ConnectionState CurrentState { get; }

    /// <summary>
    /// Gets the timestamp when the state change occurred
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// Gets the error message if the state change was due to an error
    /// </summary>
    public string? ErrorMessage { get; }

    /// <summary>
    /// Initializes a new instance of the ConnectionStateChangedEventArgs class
    /// </summary>
    /// <param name="endpointId">The endpoint identifier</param>
    /// <param name="previousState">The previous connection state</param>
    /// <param name="currentState">The current connection state</param>
    /// <param name="errorMessage">Optional error message</param>
    public ConnectionStateChangedEventArgs(
        string endpointId,
        ConnectionState previousState,
        ConnectionState currentState,
        string? errorMessage = null)
    {
        EndpointId = endpointId;
        PreviousState = previousState;
        CurrentState = currentState;
        Timestamp = DateTime.UtcNow;
        ErrorMessage = errorMessage;
    }
}
