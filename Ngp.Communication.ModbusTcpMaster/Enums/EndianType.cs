namespace Ngp.Communication.ModbusTcpMaster.Enums;

/// <summary>
/// Endian types for data conversion
/// </summary>
public enum EndianType
{
    /// <summary>
    /// Big-Endian (ABCD) - Most significant byte first
    /// </summary>
    BigEndian,

    /// <summary>
    /// Little-End<PERSON> (DCBA) - Least significant byte first
    /// </summary>
    LittleEndian,

    /// <summary>
    /// Big-Endian Byte-Swap (BADC) - Big-endian with byte pairs swapped
    /// </summary>
    BigEndianByteSwap,

    /// <summary>
    /// Little-Endian Byte-Swap (CDAB) - Little-endian with byte pairs swapped
    /// </summary>
    LittleEndianByteSwap
}
