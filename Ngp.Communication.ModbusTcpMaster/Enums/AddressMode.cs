namespace Ngp.Communication.ModbusTcpMaster.Enums;

/// <summary>
/// Address modes for Modbus register addressing
/// </summary>
public enum AddressMode
{
    /// <summary>
    /// 0-based addressing (0, 1, 2, ...)
    /// </summary>
    ZeroBased,

    /// <summary>
    /// 1-based addressing (1, 2, 3, ...)
    /// </summary>
    OneBased,

    /// <summary>
    /// Modbus register addressing (40001, 40002, 30001, etc.)
    /// </summary>
    ModbusRegister
}
