namespace Ngp.Communication.ModbusTcpMaster.Enums;

/// <summary>
/// Modbus function codes as defined in the Modbus specification
/// </summary>
public enum ModbusFunction : byte
{
    /// <summary>
    /// Read coils (0x01) - Read discrete outputs
    /// </summary>
    ReadCoils = 0x01,

    /// <summary>
    /// Read discrete inputs (0x02) - Read discrete inputs
    /// </summary>
    ReadDiscreteInputs = 0x02,

    /// <summary>
    /// Read holding registers (0x03) - Read analog outputs
    /// </summary>
    ReadHoldingRegisters = 0x03,

    /// <summary>
    /// Read input registers (0x04) - Read analog inputs
    /// </summary>
    ReadInputRegisters = 0x04,

    /// <summary>
    /// Write single coil (0x05) - Write single discrete output
    /// </summary>
    WriteSingleCoil = 0x05,

    /// <summary>
    /// Write single register (0x06) - Write single analog output
    /// </summary>
    WriteSingleRegister = 0x06,

    /// <summary>
    /// Write multiple coils (0x0F) - Write multiple discrete outputs
    /// </summary>
    WriteMultipleCoils = 0x0F,

    /// <summary>
    /// Write multiple registers (0x10) - Write multiple analog outputs
    /// </summary>
    WriteMultipleRegisters = 0x10
}
