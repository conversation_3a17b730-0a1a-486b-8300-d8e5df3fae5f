using Ngp.Communication.ModbusTcpMaster.Enums;
using Ngp.Communication.ModbusTcpMaster.Models;

namespace Ngp.Communication.ModbusTcpMaster.Interfaces;

/// <summary>
/// Fluent API builder interface for configuring Modbus TCP Master
/// </summary>
public interface IModbusTcpMasterBuilder
{
    /// <summary>
    /// Sets the endpoint configuration
    /// </summary>
    /// <param name="ipAddress">IP address</param>
    /// <param name="port">Port number</param>
    /// <returns>The builder instance</returns>
    IModbusTcpMasterBuilder WithEndpoint(string ipAddress, ushort port = 502);

    /// <summary>
    /// Sets the endpoint ID
    /// </summary>
    /// <param name="id">Endpoint identifier</param>
    /// <returns>The builder instance</returns>
    IModbusTcpMasterBuilder WithId(string id);

    /// <summary>
    /// Sets the protocol type
    /// </summary>
    /// <param name="protocol">Protocol type</param>
    /// <returns>The builder instance</returns>
    IModbusTcpMasterBuilder WithProtocol(ModbusProtocol protocol);

    /// <summary>
    /// Sets the write mode
    /// </summary>
    /// <param name="writeMode">Write mode</param>
    /// <returns>The builder instance</returns>
    IModbusTcpMasterBuilder WithWriteMode(WriteMode writeMode);

    /// <summary>
    /// Sets the address mode
    /// </summary>
    /// <param name="addressMode">Address mode</param>
    /// <returns>The builder instance</returns>
    IModbusTcpMasterBuilder WithAddressMode(AddressMode addressMode);

    /// <summary>
    /// Sets timeout values
    /// </summary>
    /// <param name="readTimeout">Read timeout in milliseconds</param>
    /// <param name="writeTimeout">Write timeout in milliseconds</param>
    /// <returns>The builder instance</returns>
    IModbusTcpMasterBuilder WithTimeouts(int readTimeout, int writeTimeout);

    /// <summary>
    /// Sets delay values
    /// </summary>
    /// <param name="packageDelay">Package delay in milliseconds</param>
    /// <param name="pollDelay">Poll delay in milliseconds</param>
    /// <returns>The builder instance</returns>
    IModbusTcpMasterBuilder WithDelays(int packageDelay, int pollDelay);

    /// <summary>
    /// Sets maximum polling quantities
    /// </summary>
    /// <param name="maxDigital">Maximum digital polling quantity</param>
    /// <param name="maxAnalog">Maximum analog polling quantity</param>
    /// <returns>The builder instance</returns>
    IModbusTcpMasterBuilder WithMaxPollingQuantities(ushort maxDigital, ushort maxAnalog);

    /// <summary>
    /// Sets retry configuration
    /// </summary>
    /// <param name="retryCount">Number of retries</param>
    /// <param name="retryDelay">Delay between retries in milliseconds</param>
    /// <returns>The builder instance</returns>
    IModbusTcpMasterBuilder WithRetryPolicy(int retryCount, int retryDelay);

    /// <summary>
    /// Sets parallelization configuration
    /// </summary>
    /// <param name="level">Parallelization level</param>
    /// <param name="autoOptimize">Enable automatic optimization</param>
    /// <returns>The builder instance</returns>
    IModbusTcpMasterBuilder WithParallelization(int level, bool autoOptimize = false);

    /// <summary>
    /// Sets debounce time for digital inputs
    /// </summary>
    /// <param name="debounceTime">Debounce time in milliseconds</param>
    /// <returns>The builder instance</returns>
    IModbusTcpMasterBuilder WithDebounceTime(int debounceTime);

    /// <summary>
    /// Adds a register for polling
    /// </summary>
    /// <param name="slaveId">Slave ID</param>
    /// <param name="address">Register address</param>
    /// <param name="function">Function code</param>
    /// <param name="dataType">Data type</param>
    /// <param name="endianType">Endian type</param>
    /// <param name="enableDebounce">Enable debouncing</param>
    /// <param name="tag">Optional tag</param>
    /// <param name="description">Optional description</param>
    /// <returns>The builder instance</returns>
    IModbusTcpMasterBuilder AddRegister(
        byte slaveId,
        ushort address,
        ModbusFunction function,
        Type? dataType = null,
        EndianType endianType = EndianType.BigEndian,
        bool enableDebounce = false,
        string? tag = null,
        string? description = null);

    /// <summary>
    /// Adds multiple registers for polling
    /// </summary>
    /// <param name="registers">Register definitions</param>
    /// <returns>The builder instance</returns>
    IModbusTcpMasterBuilder AddRegisters(params RegisterDefinition[] registers);

    /// <summary>
    /// Adds a range of registers with the same configuration for polling
    /// </summary>
    /// <param name="slaveId">Slave ID</param>
    /// <param name="startAddress">Starting address</param>
    /// <param name="endAddress">Ending address (inclusive)</param>
    /// <param name="function">Function code</param>
    /// <param name="dataType">Data type</param>
    /// <param name="endianType">Endian type</param>
    /// <param name="enableDebounce">Enable debouncing</param>
    /// <param name="tagPrefix">Tag prefix for generated tags</param>
    /// <param name="description">Description for the registers</param>
    /// <returns>The builder instance</returns>
    IModbusTcpMasterBuilder AddRegisters(
        byte slaveId,
        ushort startAddress,
        ushort endAddress,
        ModbusFunction function,
        Type? dataType = null,
        EndianType endianType = EndianType.BigEndian,
        bool enableDebounce = false,
        string? tagPrefix = null,
        string? description = null);

    /// <summary>
    /// Adds a range of registers for polling
    /// </summary>
    /// <param name="slaveId">Slave ID</param>
    /// <param name="startAddress">Starting address</param>
    /// <param name="count">Number of registers</param>
    /// <param name="function">Function code</param>
    /// <param name="dataType">Data type</param>
    /// <param name="endianType">Endian type</param>
    /// <returns>The builder instance</returns>
    IModbusTcpMasterBuilder AddRegisterRange(
        byte slaveId,
        ushort startAddress,
        ushort count,
        ModbusFunction function,
        Type? dataType = null,
        EndianType endianType = EndianType.BigEndian);

    /// <summary>
    /// Builds the Modbus TCP Master instance
    /// </summary>
    /// <returns>The configured Modbus TCP Master</returns>
    IModbusTcpMaster Build();
}
