using Ngp.Communication.ModbusTcpMaster.Enums;
using Ngp.Communication.ModbusTcpMaster.Events;
using Ngp.Communication.ModbusTcpMaster.Models;

namespace Ngp.Communication.ModbusTcpMaster.Interfaces;

/// <summary>
/// Interface for Modbus TCP Master functionality
/// </summary>
public interface IModbusTcpMaster : IDisposable
{
    /// <summary>
    /// Event raised when a connection state changes
    /// </summary>
    event EventHandler<ConnectionStateChangedEventArgs>? ConnectionStateChanged;

    /// <summary>
    /// Event raised when a register value changes
    /// </summary>
    event EventHandler<RegisterValueChangedEventArgs>? RegisterValueChanged;

    /// <summary>
    /// Event raised when a Modbus error occurs
    /// </summary>
    event EventHandler<ModbusErrorEventArgs>? ModbusError;

    /// <summary>
    /// Gets the current connection state
    /// </summary>
    ConnectionState ConnectionState { get; }

    /// <summary>
    /// Gets the endpoint configuration
    /// </summary>
    EndpointConfiguration Configuration { get; }

    /// <summary>
    /// Gets the list of registered polling registers
    /// </summary>
    IReadOnlyList<RegisterDefinition> PollingRegisters { get; }

    /// <summary>
    /// Starts the polling operation
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task StartAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Stops the polling operation
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task StopAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Adds a register for polling
    /// </summary>
    /// <param name="register">The register definition</param>
    void AddRegister(RegisterDefinition register);

    /// <summary>
    /// Removes a register from polling
    /// </summary>
    /// <param name="slaveId">The slave ID</param>
    /// <param name="address">The register address</param>
    /// <param name="function">The function code</param>
    /// <returns>True if removed, false if not found</returns>
    bool RemoveRegister(byte slaveId, ushort address, ModbusFunction function);

    /// <summary>
    /// Writes a single coil
    /// </summary>
    /// <param name="slaveId">The slave ID</param>
    /// <param name="address">The coil address</param>
    /// <param name="value">The value to write</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task<bool> WriteSingleCoilAsync(byte slaveId, ushort address, bool value, CancellationToken cancellationToken = default);

    /// <summary>
    /// Writes a single register
    /// </summary>
    /// <param name="slaveId">The slave ID</param>
    /// <param name="address">The register address</param>
    /// <param name="value">The value to write</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task<bool> WriteSingleRegisterAsync(byte slaveId, ushort address, ushort value, CancellationToken cancellationToken = default);

    /// <summary>
    /// Writes multiple coils
    /// </summary>
    /// <param name="slaveId">The slave ID</param>
    /// <param name="startAddress">The starting address</param>
    /// <param name="values">The values to write</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task<bool> WriteMultipleCoilsAsync(byte slaveId, ushort startAddress, bool[] values, CancellationToken cancellationToken = default);

    /// <summary>
    /// Writes multiple registers
    /// </summary>
    /// <param name="slaveId">The slave ID</param>
    /// <param name="startAddress">The starting address</param>
    /// <param name="values">The values to write</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task<bool> WriteMultipleRegistersAsync(byte slaveId, ushort startAddress, ushort[] values, CancellationToken cancellationToken = default);

    /// <summary>
    /// Writes a typed value to a register
    /// </summary>
    /// <param name="slaveId">The slave ID</param>
    /// <param name="address">The register address</param>
    /// <param name="value">The value to write</param>
    /// <param name="dataType">The data type</param>
    /// <param name="endianType">The endian type</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task<bool> WriteTypedValueAsync(byte slaveId, ushort address, object value, Type dataType, EndianType endianType, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the current value of a register
    /// </summary>
    /// <param name="slaveId">The slave ID</param>
    /// <param name="address">The register address</param>
    /// <param name="function">The function code</param>
    /// <returns>The current value as raw bytes, or null if not available</returns>
    byte[]? GetCurrentValue(byte slaveId, ushort address, ModbusFunction function);

    /// <summary>
    /// Gets the current typed value of a register
    /// </summary>
    /// <param name="slaveId">The slave ID</param>
    /// <param name="address">The register address</param>
    /// <param name="function">The function code</param>
    /// <param name="dataType">The data type</param>
    /// <param name="endianType">The endian type</param>
    /// <returns>The current typed value, or null if not available</returns>
    object? GetCurrentTypedValue(byte slaveId, ushort address, ModbusFunction function, Type dataType, EndianType endianType);
}
