using Microsoft.Extensions.Logging;
using Ngp.Communication.ModbusTcpMaster.Enums;
using Ngp.Communication.ModbusTcpMaster.Interfaces;
using Ngp.Communication.ModbusTcpMaster.Models;

namespace Ngp.Communication.ModbusTcpMaster.Builders;

/// <summary>
/// Fluent API builder for configuring Modbus TCP Master
/// </summary>
public class ModbusTcpMasterBuilder : IModbusTcpMasterBuilder
{
    private readonly EndpointConfiguration _configuration;
    private readonly List<RegisterDefinition> _registers;
    private readonly ILoggerFactory? _loggerFactory;

    /// <summary>
    /// Initializes a new instance of the ModbusTcpMasterBuilder class
    /// </summary>
    /// <param name="loggerFactory">Optional logger factory</param>
    public ModbusTcpMasterBuilder(ILoggerFactory? loggerFactory = null)
    {
        _configuration = new EndpointConfiguration();
        _registers = new List<RegisterDefinition>();
        _loggerFactory = loggerFactory;
    }

    /// <summary>
    /// Sets the endpoint configuration
    /// </summary>
    /// <param name="ipAddress">IP address</param>
    /// <param name="port">Port number</param>
    /// <returns>The builder instance</returns>
    public IModbusTcpMasterBuilder WithEndpoint(string ipAddress, ushort port = 502)
    {
        if (string.IsNullOrWhiteSpace(ipAddress))
            throw new ArgumentException("IP address cannot be null or empty", nameof(ipAddress));

        _configuration.IpAddress = ipAddress;
        _configuration.Port = port;

        // Generate default ID if not set
        if (string.IsNullOrEmpty(_configuration.Id))
        {
            _configuration.Id = $"{ipAddress}:{port}";
        }

        return this;
    }

    /// <summary>
    /// Sets the endpoint ID
    /// </summary>
    /// <param name="id">Endpoint identifier</param>
    /// <returns>The builder instance</returns>
    public IModbusTcpMasterBuilder WithId(string id)
    {
        if (string.IsNullOrWhiteSpace(id))
            throw new ArgumentException("ID cannot be null or empty", nameof(id));

        _configuration.Id = id;
        return this;
    }

    /// <summary>
    /// Sets the protocol type
    /// </summary>
    /// <param name="protocol">Protocol type</param>
    /// <returns>The builder instance</returns>
    public IModbusTcpMasterBuilder WithProtocol(ModbusProtocol protocol)
    {
        _configuration.Protocol = protocol;

        // Force parallelization level to 1 for RTU over TCP
        if (protocol == ModbusProtocol.ModbusRtuOverTcp)
        {
            _configuration.ParallelizationLevel = 1;
            _configuration.AutoParallelization = false;
        }

        return this;
    }

    /// <summary>
    /// Sets the write mode
    /// </summary>
    /// <param name="writeMode">Write mode</param>
    /// <returns>The builder instance</returns>
    public IModbusTcpMasterBuilder WithWriteMode(WriteMode writeMode)
    {
        _configuration.WriteMode = writeMode;
        return this;
    }

    /// <summary>
    /// Sets the address mode
    /// </summary>
    /// <param name="addressMode">Address mode</param>
    /// <returns>The builder instance</returns>
    public IModbusTcpMasterBuilder WithAddressMode(AddressMode addressMode)
    {
        _configuration.AddressMode = addressMode;
        return this;
    }

    /// <summary>
    /// Sets timeout values
    /// </summary>
    /// <param name="readTimeout">Read timeout in milliseconds</param>
    /// <param name="writeTimeout">Write timeout in milliseconds</param>
    /// <returns>The builder instance</returns>
    public IModbusTcpMasterBuilder WithTimeouts(int readTimeout, int writeTimeout)
    {
        if (readTimeout <= 0)
            throw new ArgumentException("Read timeout must be positive", nameof(readTimeout));
        if (writeTimeout <= 0)
            throw new ArgumentException("Write timeout must be positive", nameof(writeTimeout));

        _configuration.ReadTimeoutMilliseconds = readTimeout;
        _configuration.WriteTimeoutMilliseconds = writeTimeout;
        return this;
    }

    /// <summary>
    /// Sets delay values
    /// </summary>
    /// <param name="packageDelay">Package delay in milliseconds</param>
    /// <param name="pollDelay">Poll delay in milliseconds</param>
    /// <returns>The builder instance</returns>
    public IModbusTcpMasterBuilder WithDelays(int packageDelay, int pollDelay)
    {
        if (packageDelay < 0)
            throw new ArgumentException("Package delay cannot be negative", nameof(packageDelay));
        if (pollDelay < 0)
            throw new ArgumentException("Poll delay cannot be negative", nameof(pollDelay));

        _configuration.PackageDelayMilliseconds = packageDelay;
        _configuration.PollDelayMilliseconds = pollDelay;
        return this;
    }

    /// <summary>
    /// Sets maximum polling quantities
    /// </summary>
    /// <param name="maxDigital">Maximum digital polling quantity</param>
    /// <param name="maxAnalog">Maximum analog polling quantity</param>
    /// <returns>The builder instance</returns>
    public IModbusTcpMasterBuilder WithMaxPollingQuantities(ushort maxDigital, ushort maxAnalog)
    {
        if (maxDigital == 0)
            throw new ArgumentException("Max digital polling quantity must be positive", nameof(maxDigital));
        if (maxAnalog == 0)
            throw new ArgumentException("Max analog polling quantity must be positive", nameof(maxAnalog));

        _configuration.MaxDigitalPollingQuantity = maxDigital;
        _configuration.MaxAnalogPollingQuantity = maxAnalog;
        return this;
    }

    /// <summary>
    /// Sets retry configuration
    /// </summary>
    /// <param name="retryCount">Number of retries</param>
    /// <param name="retryDelay">Delay between retries in milliseconds</param>
    /// <returns>The builder instance</returns>
    public IModbusTcpMasterBuilder WithRetryPolicy(int retryCount, int retryDelay)
    {
        if (retryCount < 0)
            throw new ArgumentException("Retry count cannot be negative", nameof(retryCount));
        if (retryDelay <= 0)
            throw new ArgumentException("Retry delay must be positive", nameof(retryDelay));

        _configuration.RetryCount = retryCount;
        _configuration.RetryDelayMilliseconds = retryDelay;
        return this;
    }

    /// <summary>
    /// Sets parallelization configuration
    /// </summary>
    /// <param name="level">Parallelization level</param>
    /// <param name="autoOptimize">Enable automatic optimization</param>
    /// <returns>The builder instance</returns>
    public IModbusTcpMasterBuilder WithParallelization(int level, bool autoOptimize = false)
    {
        if (level <= 0)
            throw new ArgumentException("Parallelization level must be positive", nameof(level));

        // Don't allow parallelization for RTU over TCP
        if (_configuration.Protocol == ModbusProtocol.ModbusRtuOverTcp && level > 1)
        {
            throw new InvalidOperationException("RTU over TCP only supports parallelization level of 1");
        }

        _configuration.ParallelizationLevel = level;
        _configuration.AutoParallelization = autoOptimize;
        return this;
    }

    /// <summary>
    /// Sets debounce time for digital inputs
    /// </summary>
    /// <param name="debounceTime">Debounce time in milliseconds</param>
    /// <returns>The builder instance</returns>
    public IModbusTcpMasterBuilder WithDebounceTime(int debounceTime)
    {
        if (debounceTime < 0)
            throw new ArgumentException("Debounce time cannot be negative", nameof(debounceTime));

        _configuration.DebounceTimeMilliseconds = debounceTime;
        return this;
    }

    /// <summary>
    /// Adds a register for polling
    /// </summary>
    /// <param name="slaveId">Slave ID</param>
    /// <param name="address">Register address</param>
    /// <param name="function">Function code</param>
    /// <param name="dataType">Data type</param>
    /// <param name="endianType">Endian type</param>
    /// <param name="enableDebounce">Enable debouncing</param>
    /// <param name="tag">Optional tag</param>
    /// <param name="description">Optional description</param>
    /// <returns>The builder instance</returns>
    public IModbusTcpMasterBuilder AddRegister(
        byte slaveId,
        ushort address,
        ModbusFunction function,
        Type? dataType = null,
        EndianType endianType = EndianType.BigEndian,
        bool enableDebounce = false,
        string? tag = null,
        string? description = null)
    {
        var register = new RegisterDefinition
        {
            SlaveId = slaveId,
            Address = address,
            Function = function,
            DataType = dataType ?? GetDefaultDataType(function),
            EndianType = endianType,
            EnableDebounce = enableDebounce,
            Tag = tag,
            Description = description
        };

        if (!register.IsValid())
        {
            throw new ArgumentException($"Invalid register definition: SlaveId={slaveId}, Address={address}, Function={function}");
        }

        _registers.Add(register);
        return this;
    }

    /// <summary>
    /// Adds multiple registers for polling
    /// </summary>
    /// <param name="registers">Register definitions</param>
    /// <returns>The builder instance</returns>
    public IModbusTcpMasterBuilder AddRegisters(params RegisterDefinition[] registers)
    {
        if (registers == null)
            throw new ArgumentNullException(nameof(registers));

        foreach (var register in registers)
        {
            if (!register.IsValid())
            {
                throw new ArgumentException($"Invalid register definition: SlaveId={register.SlaveId}, Address={register.Address}, Function={register.Function}");
            }
            _registers.Add(register);
        }

        return this;
    }

    /// <summary>
    /// Adds a range of registers with the same configuration for polling
    /// </summary>
    /// <param name="slaveId">Slave ID</param>
    /// <param name="startAddress">Starting address</param>
    /// <param name="endAddress">Ending address (inclusive)</param>
    /// <param name="function">Function code</param>
    /// <param name="dataType">Data type</param>
    /// <param name="endianType">Endian type</param>
    /// <param name="enableDebounce">Enable debouncing</param>
    /// <param name="tagPrefix">Tag prefix for generated tags</param>
    /// <param name="description">Description for the registers</param>
    /// <returns>The builder instance</returns>
    public IModbusTcpMasterBuilder AddRegisters(
        byte slaveId,
        ushort startAddress,
        ushort endAddress,
        ModbusFunction function,
        Type? dataType = null,
        EndianType endianType = EndianType.BigEndian,
        bool enableDebounce = false,
        string? tagPrefix = null,
        string? description = null)
    {
        if (startAddress > endAddress)
            throw new ArgumentException("Start address must be less than or equal to end address", nameof(startAddress));

        var actualDataType = dataType ?? GetDefaultDataType(function);

        // Calculate register count for the data type
        var tempRegister = new RegisterDefinition { DataType = actualDataType };
        var registerCount = tempRegister.CalculateRegisterCount();

        // Create registers with proper address stepping based on data type
        for (ushort address = startAddress; address <= endAddress; address += registerCount)
        {
            var tag = string.IsNullOrWhiteSpace(tagPrefix) ? null : $"{tagPrefix}_{address}";
            var register = new RegisterDefinition
            {
                SlaveId = slaveId,
                Address = address,
                Function = function,
                DataType = actualDataType,
                EndianType = endianType,
                EnableDebounce = enableDebounce,
                Tag = tag,
                Description = description
            };

            if (!register.IsValid())
            {
                throw new ArgumentException($"Invalid register definition: SlaveId={slaveId}, Address={address}, Function={function}");
            }

            _registers.Add(register);
        }

        return this;
    }

    /// <summary>
    /// Adds a range of registers for polling
    /// </summary>
    /// <param name="slaveId">Slave ID</param>
    /// <param name="startAddress">Starting address</param>
    /// <param name="count">Number of registers</param>
    /// <param name="function">Function code</param>
    /// <param name="dataType">Data type</param>
    /// <param name="endianType">Endian type</param>
    /// <returns>The builder instance</returns>
    public IModbusTcpMasterBuilder AddRegisterRange(
        byte slaveId,
        ushort startAddress,
        ushort count,
        ModbusFunction function,
        Type? dataType = null,
        EndianType endianType = EndianType.BigEndian)
    {
        if (count == 0)
            throw new ArgumentException("Count must be positive", nameof(count));

        var actualDataType = dataType ?? GetDefaultDataType(function);

        for (ushort i = 0; i < count; i++)
        {
            var address = (ushort)(startAddress + i);
            AddRegister(slaveId, address, function, actualDataType, endianType);
        }

        return this;
    }

    /// <summary>
    /// Builds the Modbus TCP Master instance
    /// </summary>
    /// <returns>The configured Modbus TCP Master</returns>
    public IModbusTcpMaster Build()
    {
        ValidateConfiguration();

        var logger = _loggerFactory?.CreateLogger<ModbusTcpMaster>();
        return new ModbusTcpMaster(_configuration, _registers, logger);
    }

    /// <summary>
    /// Gets the default data type for a function code
    /// </summary>
    /// <param name="function">Function code</param>
    /// <returns>Default data type</returns>
    private static Type GetDefaultDataType(ModbusFunction function)
    {
        return function switch
        {
            ModbusFunction.ReadCoils or ModbusFunction.ReadDiscreteInputs => typeof(bool),
            _ => typeof(ushort)
        };
    }

    /// <summary>
    /// Validates the configuration before building
    /// </summary>
    private void ValidateConfiguration()
    {
        if (string.IsNullOrWhiteSpace(_configuration.IpAddress))
            throw new InvalidOperationException("IP address must be specified");

        if (string.IsNullOrWhiteSpace(_configuration.Id))
            throw new InvalidOperationException("Endpoint ID must be specified");

        if (_configuration.Port == 0)
            throw new InvalidOperationException("Port must be specified");

        if (!_registers.Any())
            throw new InvalidOperationException("At least one register must be added");

        // Validate all registers
        foreach (var register in _registers)
        {
            if (!register.IsValid())
            {
                throw new InvalidOperationException($"Invalid register: SlaveId={register.SlaveId}, Address={register.Address}, Function={register.Function}");
            }
        }
    }
}
