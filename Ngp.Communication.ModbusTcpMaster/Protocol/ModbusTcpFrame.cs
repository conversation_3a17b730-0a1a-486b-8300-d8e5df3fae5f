using Ngp.Communication.ModbusTcpMaster.Enums;
using System.Buffers.Binary;

namespace Ngp.Communication.ModbusTcpMaster.Protocol;

/// <summary>
/// Represents a Modbus TCP frame structure
/// </summary>
public class ModbusTcpFrame
{
    /// <summary>
    /// Gets or sets the transaction identifier
    /// </summary>
    public ushort TransactionId { get; set; }

    /// <summary>
    /// Gets or sets the protocol identifier (always 0 for Modbus TCP)
    /// </summary>
    public ushort ProtocolId { get; set; } = 0;

    /// <summary>
    /// Gets or sets the length field
    /// </summary>
    public ushort Length { get; set; }

    /// <summary>
    /// Gets or sets the unit identifier (slave ID)
    /// </summary>
    public byte UnitId { get; set; }

    /// <summary>
    /// Gets or sets the function code
    /// </summary>
    public ModbusFunction Function { get; set; }

    /// <summary>
    /// Gets or sets the data payload
    /// </summary>
    public byte[] Data { get; set; } = Array.Empty<byte>();

    /// <summary>
    /// Converts the frame to a byte array for transmission
    /// </summary>
    /// <returns>The frame as a byte array</returns>
    public byte[] ToByteArray()
    {
        var dataLength = Data.Length + 2; // Function code + data
        Length = (ushort)dataLength;

        var frame = new byte[6 + dataLength]; // MBAP header (6 bytes) + data
        var index = 0;

        // MBAP Header
        BinaryPrimitives.WriteUInt16BigEndian(frame.AsSpan(index), TransactionId);
        index += 2;

        BinaryPrimitives.WriteUInt16BigEndian(frame.AsSpan(index), ProtocolId);
        index += 2;

        BinaryPrimitives.WriteUInt16BigEndian(frame.AsSpan(index), Length);
        index += 2;

        frame[index++] = UnitId;

        // PDU (Protocol Data Unit)
        frame[index++] = (byte)Function;

        if (Data.Length > 0)
        {
            Array.Copy(Data, 0, frame, index, Data.Length);
        }

        return frame;
    }

    /// <summary>
    /// Creates a frame from a byte array
    /// </summary>
    /// <param name="data">The byte array containing the frame</param>
    /// <returns>The parsed frame</returns>
    /// <exception cref="ArgumentException">Thrown when the data is invalid</exception>
    public static ModbusTcpFrame FromByteArray(byte[] data)
    {
        if (data.Length < 8) // Minimum frame size: 6 bytes MBAP + 1 byte unit + 1 byte function
            throw new ArgumentException("Frame too short", nameof(data));

        var frame = new ModbusTcpFrame();
        var index = 0;

        // Parse MBAP Header
        frame.TransactionId = BinaryPrimitives.ReadUInt16BigEndian(data.AsSpan(index));
        index += 2;

        frame.ProtocolId = BinaryPrimitives.ReadUInt16BigEndian(data.AsSpan(index));
        index += 2;

        frame.Length = BinaryPrimitives.ReadUInt16BigEndian(data.AsSpan(index));
        index += 2;

        frame.UnitId = data[index++];

        // Validate length
        if (data.Length < 6 + frame.Length)
            throw new ArgumentException("Frame length mismatch", nameof(data));

        // Parse PDU
        frame.Function = (ModbusFunction)data[index++];

        var dataLength = frame.Length - 2; // Subtract unit ID and function code
        if (dataLength > 0)
        {
            frame.Data = new byte[dataLength];
            Array.Copy(data, index, frame.Data, 0, dataLength);
        }

        return frame;
    }

    /// <summary>
    /// Validates the frame structure
    /// </summary>
    /// <returns>True if valid, false otherwise</returns>
    public bool IsValid()
    {
        if (ProtocolId != 0)
            return false;

        if (UnitId == 0 || UnitId > 247)
            return false;

        if (!Enum.IsDefined(typeof(ModbusFunction), Function))
            return false;

        if (Length != Data.Length + 2)
            return false;

        return true;
    }

    /// <summary>
    /// Checks if this frame is an error response
    /// </summary>
    /// <returns>True if it's an error response, false otherwise</returns>
    public bool IsErrorResponse()
    {
        return ((byte)Function & 0x80) != 0;
    }

    /// <summary>
    /// Gets the error code from an error response
    /// </summary>
    /// <returns>The error code, or null if not an error response</returns>
    public byte? GetErrorCode()
    {
        if (!IsErrorResponse() || Data.Length == 0)
            return null;

        return Data[0];
    }

    /// <summary>
    /// Creates an error response frame
    /// </summary>
    /// <param name="originalFrame">The original request frame</param>
    /// <param name="errorCode">The error code</param>
    /// <returns>The error response frame</returns>
    public static ModbusTcpFrame CreateErrorResponse(ModbusTcpFrame originalFrame, byte errorCode)
    {
        return new ModbusTcpFrame
        {
            TransactionId = originalFrame.TransactionId,
            ProtocolId = originalFrame.ProtocolId,
            UnitId = originalFrame.UnitId,
            Function = (ModbusFunction)((byte)originalFrame.Function | 0x80),
            Data = [errorCode]
        };
    }
}
