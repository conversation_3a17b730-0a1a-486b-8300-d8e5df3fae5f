using Ngp.Communication.ModbusTcpMaster.Enums;
using System.Buffers.Binary;

namespace Ngp.Communication.ModbusTcpMaster.Protocol;

/// <summary>
/// Builder class for creating Modbus command frames
/// </summary>
public static class ModbusCommandBuilder
{
    /// <summary>
    /// Creates a Read Coils command
    /// </summary>
    /// <param name="startAddress">Starting address</param>
    /// <param name="quantity">Number of coils to read</param>
    /// <returns>The command data</returns>
    public static byte[] CreateReadCoilsCommand(ushort startAddress, ushort quantity)
    {
        var data = new byte[4];
        BinaryPrimitives.WriteUInt16BigEndian(data.AsSpan(0), startAddress);
        BinaryPrimitives.WriteUInt16BigEndian(data.AsSpan(2), quantity);
        return data;
    }

    /// <summary>
    /// Creates a Read Discrete Inputs command
    /// </summary>
    /// <param name="startAddress">Starting address</param>
    /// <param name="quantity">Number of inputs to read</param>
    /// <returns>The command data</returns>
    public static byte[] CreateReadDiscreteInputsCommand(ushort startAddress, ushort quantity)
    {
        var data = new byte[4];
        BinaryPrimitives.WriteUInt16BigEndian(data.AsSpan(0), startAddress);
        BinaryPrimitives.WriteUInt16BigEndian(data.AsSpan(2), quantity);
        return data;
    }

    /// <summary>
    /// Creates a Read Holding Registers command
    /// </summary>
    /// <param name="startAddress">Starting address</param>
    /// <param name="quantity">Number of registers to read</param>
    /// <returns>The command data</returns>
    public static byte[] CreateReadHoldingRegistersCommand(ushort startAddress, ushort quantity)
    {
        var data = new byte[4];
        BinaryPrimitives.WriteUInt16BigEndian(data.AsSpan(0), startAddress);
        BinaryPrimitives.WriteUInt16BigEndian(data.AsSpan(2), quantity);
        return data;
    }

    /// <summary>
    /// Creates a Read Input Registers command
    /// </summary>
    /// <param name="startAddress">Starting address</param>
    /// <param name="quantity">Number of registers to read</param>
    /// <returns>The command data</returns>
    public static byte[] CreateReadInputRegistersCommand(ushort startAddress, ushort quantity)
    {
        var data = new byte[4];
        BinaryPrimitives.WriteUInt16BigEndian(data.AsSpan(0), startAddress);
        BinaryPrimitives.WriteUInt16BigEndian(data.AsSpan(2), quantity);
        return data;
    }

    /// <summary>
    /// Creates a Write Single Coil command
    /// </summary>
    /// <param name="address">Coil address</param>
    /// <param name="value">Coil value (true/false)</param>
    /// <returns>The command data</returns>
    public static byte[] CreateWriteSingleCoilCommand(ushort address, bool value)
    {
        var data = new byte[4];
        BinaryPrimitives.WriteUInt16BigEndian(data.AsSpan(0), address);
        BinaryPrimitives.WriteUInt16BigEndian(data.AsSpan(2), (ushort)(value ? 0xFF00 : 0x0000));
        return data;
    }

    /// <summary>
    /// Creates a Write Single Register command
    /// </summary>
    /// <param name="address">Register address</param>
    /// <param name="value">Register value</param>
    /// <returns>The command data</returns>
    public static byte[] CreateWriteSingleRegisterCommand(ushort address, ushort value)
    {
        var data = new byte[4];
        BinaryPrimitives.WriteUInt16BigEndian(data.AsSpan(0), address);
        BinaryPrimitives.WriteUInt16BigEndian(data.AsSpan(2), value);
        return data;
    }

    /// <summary>
    /// Creates a Write Multiple Coils command
    /// </summary>
    /// <param name="startAddress">Starting address</param>
    /// <param name="values">Coil values</param>
    /// <returns>The command data</returns>
    public static byte[] CreateWriteMultipleCoilsCommand(ushort startAddress, bool[] values)
    {
        var quantity = (ushort)values.Length;
        var byteCount = (byte)((quantity + 7) / 8); // Round up to nearest byte
        var data = new byte[5 + byteCount];

        BinaryPrimitives.WriteUInt16BigEndian(data.AsSpan(0), startAddress);
        BinaryPrimitives.WriteUInt16BigEndian(data.AsSpan(2), quantity);
        data[4] = byteCount;

        // Pack boolean values into bytes
        for (int i = 0; i < values.Length; i++)
        {
            if (values[i])
            {
                var byteIndex = 5 + (i / 8);
                var bitIndex = i % 8;
                data[byteIndex] |= (byte)(1 << bitIndex);
            }
        }

        return data;
    }

    /// <summary>
    /// Creates a Write Multiple Registers command
    /// </summary>
    /// <param name="startAddress">Starting address</param>
    /// <param name="values">Register values</param>
    /// <returns>The command data</returns>
    public static byte[] CreateWriteMultipleRegistersCommand(ushort startAddress, ushort[] values)
    {
        var quantity = (ushort)values.Length;
        var byteCount = (byte)(quantity * 2);
        var data = new byte[5 + byteCount];

        BinaryPrimitives.WriteUInt16BigEndian(data.AsSpan(0), startAddress);
        BinaryPrimitives.WriteUInt16BigEndian(data.AsSpan(2), quantity);
        data[4] = byteCount;

        // Write register values
        for (int i = 0; i < values.Length; i++)
        {
            BinaryPrimitives.WriteUInt16BigEndian(data.AsSpan(5 + (i * 2)), values[i]);
        }

        return data;
    }

    /// <summary>
    /// Parses a Read Coils response
    /// </summary>
    /// <param name="responseData">The response data</param>
    /// <param name="expectedQuantity">The expected number of coils</param>
    /// <returns>Array of boolean values</returns>
    public static bool[] ParseReadCoilsResponse(byte[] responseData, ushort expectedQuantity)
    {
        if (responseData.Length < 1)
            throw new ArgumentException("Invalid response data", nameof(responseData));

        var byteCount = responseData[0];
        if (responseData.Length != byteCount + 1)
            throw new ArgumentException("Response length mismatch", nameof(responseData));

        var values = new bool[expectedQuantity];
        for (int i = 0; i < expectedQuantity; i++)
        {
            var byteIndex = 1 + (i / 8);
            var bitIndex = i % 8;
            values[i] = (responseData[byteIndex] & (1 << bitIndex)) != 0;
        }

        return values;
    }

    /// <summary>
    /// Parses a Read Discrete Inputs response
    /// </summary>
    /// <param name="responseData">The response data</param>
    /// <param name="expectedQuantity">The expected number of inputs</param>
    /// <returns>Array of boolean values</returns>
    public static bool[] ParseReadDiscreteInputsResponse(byte[] responseData, ushort expectedQuantity)
    {
        return ParseReadCoilsResponse(responseData, expectedQuantity);
    }

    /// <summary>
    /// Parses a Read Holding Registers response
    /// </summary>
    /// <param name="responseData">The response data</param>
    /// <param name="expectedQuantity">The expected number of registers</param>
    /// <returns>Array of register values</returns>
    public static ushort[] ParseReadHoldingRegistersResponse(byte[] responseData, ushort expectedQuantity)
    {
        if (responseData.Length < 1)
            throw new ArgumentException("Invalid response data", nameof(responseData));

        var byteCount = responseData[0];
        if (responseData.Length != byteCount + 1 || byteCount != expectedQuantity * 2)
            throw new ArgumentException("Response length mismatch", nameof(responseData));

        var values = new ushort[expectedQuantity];
        for (int i = 0; i < expectedQuantity; i++)
        {
            values[i] = BinaryPrimitives.ReadUInt16BigEndian(responseData.AsSpan(1 + (i * 2)));
        }

        return values;
    }

    /// <summary>
    /// Parses a Read Input Registers response
    /// </summary>
    /// <param name="responseData">The response data</param>
    /// <param name="expectedQuantity">The expected number of registers</param>
    /// <returns>Array of register values</returns>
    public static ushort[] ParseReadInputRegistersResponse(byte[] responseData, ushort expectedQuantity)
    {
        return ParseReadHoldingRegistersResponse(responseData, expectedQuantity);
    }
}
