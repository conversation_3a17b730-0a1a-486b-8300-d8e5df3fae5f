using Ngp.Communication.ModbusTcpMaster.Enums;

namespace Ngp.Communication.ModbusTcpMaster.Protocol;

/// <summary>
/// Represents a Modbus RTU frame structure for RTU over TCP
/// </summary>
public class ModbusRtuFrame
{
    /// <summary>
    /// Gets or sets the slave address
    /// </summary>
    public byte SlaveAddress { get; set; }

    /// <summary>
    /// Gets or sets the function code
    /// </summary>
    public ModbusFunction Function { get; set; }

    /// <summary>
    /// Gets or sets the data payload
    /// </summary>
    public byte[] Data { get; set; } = Array.Empty<byte>();

    /// <summary>
    /// Gets or sets the CRC checksum
    /// </summary>
    public ushort Crc { get; set; }

    /// <summary>
    /// Converts the frame to a byte array for transmission
    /// </summary>
    /// <returns>The frame as a byte array</returns>
    public byte[] ToByteArray()
    {
        var frame = new byte[Data.Length + 4]; // Address + Function + Data + CRC (2 bytes)
        var index = 0;

        frame[index++] = SlaveAddress;
        frame[index++] = (byte)Function;

        if (Data.Length > 0)
        {
            Array.Copy(Data, 0, frame, index, Data.Length);
            index += Data.Length;
        }

        // Calculate and append CRC
        Crc = CalculateCrc(frame, index);
        frame[index++] = (byte)(Crc & 0xFF);
        frame[index] = (byte)((Crc >> 8) & 0xFF);

        return frame;
    }

    /// <summary>
    /// Creates a frame from a byte array
    /// </summary>
    /// <param name="data">The byte array containing the frame</param>
    /// <returns>The parsed frame</returns>
    /// <exception cref="ArgumentException">Thrown when the data is invalid</exception>
    public static ModbusRtuFrame FromByteArray(byte[] data)
    {
        if (data.Length < 4) // Minimum frame size: Address + Function + CRC (2 bytes)
            throw new ArgumentException("Frame too short", nameof(data));

        var frame = new ModbusRtuFrame();
        var index = 0;

        frame.SlaveAddress = data[index++];
        frame.Function = (ModbusFunction)data[index++];

        var dataLength = data.Length - 4; // Subtract address, function, and CRC
        if (dataLength > 0)
        {
            frame.Data = new byte[dataLength];
            Array.Copy(data, index, frame.Data, 0, dataLength);
            index += dataLength;
        }

        // Extract CRC
        frame.Crc = (ushort)(data[index] | (data[index + 1] << 8));

        // Validate CRC
        var calculatedCrc = CalculateCrc(data, data.Length - 2);
        if (frame.Crc != calculatedCrc)
            throw new ArgumentException("CRC mismatch", nameof(data));

        return frame;
    }

    /// <summary>
    /// Calculates the CRC-16 checksum for Modbus RTU
    /// </summary>
    /// <param name="data">The data to calculate CRC for</param>
    /// <param name="length">The length of data to include in CRC calculation</param>
    /// <returns>The calculated CRC</returns>
    public static ushort CalculateCrc(byte[] data, int length)
    {
        ushort crc = 0xFFFF;

        for (int i = 0; i < length; i++)
        {
            crc ^= data[i];

            for (int j = 0; j < 8; j++)
            {
                if ((crc & 0x0001) != 0)
                {
                    crc >>= 1;
                    crc ^= 0xA001;
                }
                else
                {
                    crc >>= 1;
                }
            }
        }

        return crc;
    }

    /// <summary>
    /// Validates the frame structure and CRC
    /// </summary>
    /// <param name="originalData">The original byte array used to create this frame</param>
    /// <returns>True if valid, false otherwise</returns>
    public bool IsValid(byte[] originalData)
    {
        if (SlaveAddress == 0 || SlaveAddress > 247)
            return false;

        if (!Enum.IsDefined(typeof(ModbusFunction), Function))
            return false;

        // Validate CRC
        var calculatedCrc = CalculateCrc(originalData, originalData.Length - 2);
        return Crc == calculatedCrc;
    }

    /// <summary>
    /// Checks if this frame is an error response
    /// </summary>
    /// <returns>True if it's an error response, false otherwise</returns>
    public bool IsErrorResponse()
    {
        return ((byte)Function & 0x80) != 0;
    }

    /// <summary>
    /// Gets the error code from an error response
    /// </summary>
    /// <returns>The error code, or null if not an error response</returns>
    public byte? GetErrorCode()
    {
        if (!IsErrorResponse() || Data.Length == 0)
            return null;

        return Data[0];
    }

    /// <summary>
    /// Creates an error response frame
    /// </summary>
    /// <param name="originalFrame">The original request frame</param>
    /// <param name="errorCode">The error code</param>
    /// <returns>The error response frame</returns>
    public static ModbusRtuFrame CreateErrorResponse(ModbusRtuFrame originalFrame, byte errorCode)
    {
        return new ModbusRtuFrame
        {
            SlaveAddress = originalFrame.SlaveAddress,
            Function = (ModbusFunction)((byte)originalFrame.Function | 0x80),
            Data = [errorCode]
        };
    }
}
