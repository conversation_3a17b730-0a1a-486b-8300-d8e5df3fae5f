using Ngp.Communication.ModbusTcpMaster.Enums;

namespace Ngp.Communication.ModbusTcpMaster.Utilities;

/// <summary>
/// Utility class for converting between different address formats
/// </summary>
public static class AddressConverter
{
    /// <summary>
    /// Converts an address from the specified mode to zero-based addressing
    /// </summary>
    /// <param name="address">The address to convert</param>
    /// <param name="addressMode">The current address mode</param>
    /// <param name="function">The Modbus function code</param>
    /// <returns>The zero-based address</returns>
    public static ushort ToZeroBased(ushort address, AddressMode addressMode, ModbusFunction function)
    {
        return addressMode switch
        {
            AddressMode.ZeroBased => address,
            AddressMode.OneBased => (ushort)(address - 1),
            AddressMode.ModbusRegister => ConvertModbusRegisterToZeroBased(address, function),
            _ => address
        };
    }

    /// <summary>
    /// Converts a zero-based address to the specified address mode
    /// </summary>
    /// <param name="address">The zero-based address</param>
    /// <param name="addressMode">The target address mode</param>
    /// <param name="function">The Modbus function code</param>
    /// <returns>The converted address</returns>
    public static ushort FromZeroBased(ushort address, AddressMode addressMode, ModbusFunction function)
    {
        return addressMode switch
        {
            AddressMode.ZeroBased => address,
            AddressMode.OneBased => (ushort)(address + 1),
            AddressMode.ModbusRegister => ConvertZeroBasedToModbusRegister(address, function),
            _ => address
        };
    }

    /// <summary>
    /// Converts a Modbus register address to zero-based addressing
    /// </summary>
    /// <param name="address">The Modbus register address</param>
    /// <param name="function">The Modbus function code</param>
    /// <returns>The zero-based address</returns>
    private static ushort ConvertModbusRegisterToZeroBased(ushort address, ModbusFunction function)
    {
        return function switch
        {
            ModbusFunction.ReadCoils or ModbusFunction.WriteSingleCoil or ModbusFunction.WriteMultipleCoils =>
                ConvertCoilAddress(address),
            
            ModbusFunction.ReadDiscreteInputs =>
                ConvertDiscreteInputAddress(address),
            
            ModbusFunction.ReadInputRegisters =>
                ConvertInputRegisterAddress(address),
            
            ModbusFunction.ReadHoldingRegisters or ModbusFunction.WriteSingleRegister or ModbusFunction.WriteMultipleRegisters =>
                ConvertHoldingRegisterAddress(address),
            
            _ => address
        };
    }

    /// <summary>
    /// Converts a zero-based address to Modbus register addressing
    /// </summary>
    /// <param name="address">The zero-based address</param>
    /// <param name="function">The Modbus function code</param>
    /// <returns>The Modbus register address</returns>
    private static ushort ConvertZeroBasedToModbusRegister(ushort address, ModbusFunction function)
    {
        return function switch
        {
            ModbusFunction.ReadCoils or ModbusFunction.WriteSingleCoil or ModbusFunction.WriteMultipleCoils =>
                (ushort)(address + 1),
            
            ModbusFunction.ReadDiscreteInputs =>
                (ushort)(address + 10001),
            
            ModbusFunction.ReadInputRegisters =>
                (ushort)(address + 30001),
            
            ModbusFunction.ReadHoldingRegisters or ModbusFunction.WriteSingleRegister or ModbusFunction.WriteMultipleRegisters =>
                (ushort)(address + 40001),
            
            _ => address
        };
    }

    /// <summary>
    /// Converts coil address (00001-09999 or extended)
    /// </summary>
    private static ushort ConvertCoilAddress(ushort address)
    {
        if (address >= 1 && address <= 9999)
            return (ushort)(address - 1);

        // For ushort range, extended addressing beyond 65535 is not possible
        // Return address as-is for other cases
        return address;
    }

    /// <summary>
    /// Converts discrete input address (10001-19999 or extended)
    /// </summary>
    private static ushort ConvertDiscreteInputAddress(ushort address)
    {
        if (address >= 10001 && address <= 19999)
            return (ushort)(address - 10001);

        // For ushort range, extended addressing beyond 65535 is not possible
        // Return address as-is for other cases
        return address;
    }

    /// <summary>
    /// Converts input register address (30001-39999 or extended)
    /// </summary>
    private static ushort ConvertInputRegisterAddress(ushort address)
    {
        if (address >= 30001 && address <= 39999)
            return (ushort)(address - 30001);

        // For ushort range, extended addressing beyond 65535 is not possible
        // Return address as-is for other cases
        return address;
    }

    /// <summary>
    /// Converts holding register address (40001-49999 or extended)
    /// </summary>
    private static ushort ConvertHoldingRegisterAddress(ushort address)
    {
        // Standard Modbus holding register range: 40001-49999 -> 0-9998
        if (address >= 40001 && address <= 49999)
            return (ushort)(address - 40001);

        // Extended addressing for addresses above 9999
        // Input like 10010 should map to holding register 410010
        // So we need to handle addresses that are meant to be in the 4xxxx range
        if (address >= 10000 && address <= 65535)
        {
            // Convert to 4xxxxx format: 10010 -> 410010, then to zero-based: 410010 - 400001 = 10009
            var extendedAddress = 400000 + address; // 10010 -> 410010
            if (extendedAddress <= 465535) // Valid extended range
                return (ushort)(extendedAddress - 400001); // 410010 - 400001 = 10009
        }

        // For ushort range, direct extended addressing beyond 65535 is not possible
        // This case would be handled by uint overloads if needed

        return address;
    }

    /// <summary>
    /// Validates if an address is valid for the given function and address mode
    /// </summary>
    /// <param name="address">The address to validate</param>
    /// <param name="addressMode">The address mode</param>
    /// <param name="function">The Modbus function code</param>
    /// <returns>True if valid, false otherwise</returns>
    public static bool IsValidAddress(ushort address, AddressMode addressMode, ModbusFunction function)
    {
        if (addressMode == AddressMode.ModbusRegister)
        {
            return function switch
            {
                ModbusFunction.ReadCoils or ModbusFunction.WriteSingleCoil or ModbusFunction.WriteMultipleCoils =>
                    (address >= 1 && address <= 9999),

                ModbusFunction.ReadDiscreteInputs =>
                    (address >= 10001 && address <= 19999),

                ModbusFunction.ReadInputRegisters =>
                    (address >= 30001 && address <= 39999),

                ModbusFunction.ReadHoldingRegisters or ModbusFunction.WriteSingleRegister or ModbusFunction.WriteMultipleRegisters =>
                    (address >= 40001 && address <= 49999) ||
                    (address >= 10000 && address <= 65535),

                _ => false
            };
        }

        return true; // For zero-based and one-based, any ushort value is valid
    }
}
