using Microsoft.Extensions.Logging;

namespace Ngp.Communication.ModbusTcpMaster.Utilities;

/// <summary>
/// Adapter to use a logger of one type for another type
/// </summary>
/// <typeparam name="T">Target logger type</typeparam>
public class LoggerAdapter<T> : ILogger<T>
{
    private readonly ILogger _logger;

    /// <summary>
    /// Initializes a new instance of the LoggerAdapter class
    /// </summary>
    /// <param name="logger">Source logger</param>
    public LoggerAdapter(ILogger logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <inheritdoc />
    public IDisposable? BeginScope<TState>(TState state) where TState : notnull
    {
        return _logger.BeginScope(state);
    }

    /// <inheritdoc />
    public bool IsEnabled(LogLevel logLevel)
    {
        return _logger.IsEnabled(logLevel);
    }

    /// <inheritdoc />
    public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
    {
        _logger.Log(logLevel, eventId, state, exception, formatter);
    }
}
