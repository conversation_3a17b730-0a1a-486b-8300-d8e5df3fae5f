using Ngp.Communication.ModbusTcpMaster.Enums;
using System.Buffers.Binary;

namespace Ngp.Communication.ModbusTcpMaster.Utilities;

/// <summary>
/// Utility class for converting data between different formats and endian types
/// </summary>
public static class DataConverter
{
    /// <summary>
    /// Converts raw bytes to the specified data type using the given endian type
    /// </summary>
    /// <param name="data">The raw byte data</param>
    /// <param name="dataType">The target data type</param>
    /// <param name="endianType">The endian type to use for conversion</param>
    /// <returns>The converted value</returns>
    public static object? ConvertFromBytes(byte[] data, Type dataType, EndianType endianType)
    {
        if (data == null || data.Length == 0)
            return null;

        // Check if data length is sufficient for the target data type
        var requiredBytes = GetByteSize(dataType);
        if (data.Length < requiredBytes)
        {
            throw new ArgumentException($"Data array length ({data.Length}) is insufficient for data type {dataType.Name} which requires {requiredBytes} bytes", nameof(data));
        }

        // Apply endian conversion to the data
        var convertedData = ApplyEndianConversion(data, endianType);

        return dataType switch
        {
            Type t when t == typeof(bool) => convertedData[0] != 0,
            Type t when t == typeof(byte) => convertedData[0],
            Type t when t == typeof(short) => BinaryPrimitives.ReadInt16BigEndian(convertedData),
            Type t when t == typeof(ushort) => BinaryPrimitives.ReadUInt16BigEndian(convertedData),
            Type t when t == typeof(int) => BinaryPrimitives.ReadInt32BigEndian(convertedData),
            Type t when t == typeof(uint) => BinaryPrimitives.ReadUInt32BigEndian(convertedData),
            Type t when t == typeof(long) => BinaryPrimitives.ReadInt64BigEndian(convertedData),
            Type t when t == typeof(ulong) => BinaryPrimitives.ReadUInt64BigEndian(convertedData),
            Type t when t == typeof(float) => BitConverter.ToSingle(convertedData, 0),
            Type t when t == typeof(double) => BitConverter.ToDouble(convertedData, 0),
            _ => null
        };
    }

    /// <summary>
    /// Converts a value to bytes using the specified endian type
    /// </summary>
    /// <param name="value">The value to convert</param>
    /// <param name="dataType">The data type of the value</param>
    /// <param name="endianType">The endian type to use for conversion</param>
    /// <returns>The converted byte array</returns>
    public static byte[] ConvertToBytes(object value, Type dataType, EndianType endianType)
    {
        byte[] data = dataType switch
        {
            Type t when t == typeof(bool) => [(byte)((bool)value ? 1 : 0)],
            Type t when t == typeof(byte) => [(byte)value],
            Type t when t == typeof(short) => BitConverter.GetBytes((short)value),
            Type t when t == typeof(ushort) => BitConverter.GetBytes((ushort)value),
            Type t when t == typeof(int) => BitConverter.GetBytes((int)value),
            Type t when t == typeof(uint) => BitConverter.GetBytes((uint)value),
            Type t when t == typeof(long) => BitConverter.GetBytes((long)value),
            Type t when t == typeof(ulong) => BitConverter.GetBytes((ulong)value),
            Type t when t == typeof(float) => BitConverter.GetBytes((float)value),
            Type t when t == typeof(double) => BitConverter.GetBytes((double)value),
            _ => throw new ArgumentException($"Unsupported data type: {dataType}")
        };

        // Apply endian conversion
        return ApplyEndianConversion(data, endianType);
    }

    /// <summary>
    /// Applies endian conversion to byte array
    /// </summary>
    /// <param name="data">The original byte array</param>
    /// <param name="endianType">The endian type to apply</param>
    /// <returns>The converted byte array</returns>
    private static byte[] ApplyEndianConversion(byte[] data, EndianType endianType)
    {
        if (data.Length <= 1)
            return data;

        var result = new byte[data.Length];
        Array.Copy(data, result, data.Length);

        switch (endianType)
        {
            case EndianType.BigEndian:
                // No conversion needed - this is our default format
                break;

            case EndianType.LittleEndian:
                // Reverse entire array
                Array.Reverse(result);
                break;

            case EndianType.BigEndianByteSwap:
                // Swap bytes in pairs (ABCD -> BADC)
                for (int i = 0; i < result.Length; i += 2)
                {
                    if (i + 1 < result.Length)
                    {
                        (result[i], result[i + 1]) = (result[i + 1], result[i]);
                    }
                }
                break;

            case EndianType.LittleEndianByteSwap:
                // Reverse entire array, then swap bytes in pairs (ABCD -> CDAB)
                Array.Reverse(result);
                for (int i = 0; i < result.Length; i += 2)
                {
                    if (i + 1 < result.Length)
                    {
                        (result[i], result[i + 1]) = (result[i + 1], result[i]);
                    }
                }
                break;
        }

        return result;
    }

    /// <summary>
    /// Gets the byte size required for the specified data type
    /// </summary>
    /// <param name="dataType">The data type</param>
    /// <returns>The number of bytes required</returns>
    public static int GetByteSize(Type dataType)
    {
        return dataType switch
        {
            Type t when t == typeof(bool) => 1,
            Type t when t == typeof(byte) => 1,
            Type t when t == typeof(short) => 2,
            Type t when t == typeof(ushort) => 2,
            Type t when t == typeof(int) => 4,
            Type t when t == typeof(uint) => 4,
            Type t when t == typeof(long) => 8,
            Type t when t == typeof(ulong) => 8,
            Type t when t == typeof(float) => 4,
            Type t when t == typeof(double) => 8,
            _ => throw new ArgumentException($"Unsupported data type: {dataType}")
        };
    }
}
