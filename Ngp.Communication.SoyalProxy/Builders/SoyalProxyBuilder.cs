using Microsoft.Extensions.Logging;
using Ngp.Communication.SoyalProxy.Models;
using Ngp.Communication.SoyalProxy.Services;
using Ngp.Shared.Factories;

namespace Ngp.Communication.SoyalProxy.Builders;

/// <summary>
/// Builder for creating SoyalProxy service instances
/// </summary>
public class SoyalProxyBuilder
{
    private readonly SoyalConfiguration _configuration = new();
    private ILogger<SoyalProxyService>? _logger;
    private IModbusTcpMasterFactory? _modbusTcpMasterFactory;

    /// <summary>
    /// Set the logger instance
    /// </summary>
    /// <param name="logger">Logger instance</param>
    /// <returns>Builder instance for method chaining</returns>
    public SoyalProxyBuilder WithLogger(ILogger<SoyalProxyService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        return this;
    }

    /// <summary>
    /// Set the ModbusTcp master factory instance
    /// </summary>
    /// <param name="modbusTcpMasterFactory">ModbusTcp master factory instance</param>
    /// <returns>Builder instance for method chaining</returns>
    public SoyalProxyBuilder WithModbusTcpMasterFactory(IModbusTcpMasterFactory modbusTcpMasterFactory)
    {
        _modbusTcpMasterFactory = modbusTcpMasterFactory ?? throw new ArgumentNullException(nameof(modbusTcpMasterFactory));
        return this;
    }

    /// <summary>
    /// Configure the ModbusTcp server connection
    /// </summary>
    /// <param name="ipAddress">IP address of the ModbusTcp server</param>
    /// <param name="port">Port of the ModbusTcp server</param>
    /// <returns>Builder instance for method chaining</returns>
    public SoyalProxyBuilder WithModbusTcpServer(string ipAddress, ushort port = 502)
    {
        if (string.IsNullOrWhiteSpace(ipAddress))
            throw new ArgumentException("IP address cannot be null or empty", nameof(ipAddress));

        _configuration.ModbusTcpHost = ipAddress;
        _configuration.ModbusTcpPort = port;
        return this;
    }

    /// <summary>
    /// Configure the JSON command server port
    /// </summary>
    /// <param name="port">JSON command server port</param>
    /// <returns>Builder instance for method chaining</returns>
    public SoyalProxyBuilder WithJsonServerPort(ushort port)
    {
        _configuration.JsonServerPort = port;
        return this;
    }

    /// <summary>
    /// Configure the number of parallel polling engines
    /// </summary>
    /// <param name="engineCount">Number of engines (1-100)</param>
    /// <returns>Builder instance for method chaining</returns>
    public SoyalProxyBuilder WithEngineCount(int engineCount)
    {
        if (engineCount < 1 || engineCount > 100)
            throw new ArgumentOutOfRangeException(nameof(engineCount), 
                "Engine count must be between 1 and 100");

        _configuration.EngineCount = engineCount;
        return this;
    }

    /// <summary>
    /// Configure the polling interval
    /// </summary>
    /// <param name="intervalMs">Polling interval in milliseconds (100-60000)</param>
    /// <returns>Builder instance for method chaining</returns>
    public SoyalProxyBuilder WithPollingInterval(int intervalMs)
    {
        if (intervalMs < 100 || intervalMs > 60000)
            throw new ArgumentOutOfRangeException(nameof(intervalMs), 
                "Polling interval must be between 100 and 60000 milliseconds");

        _configuration.PollingIntervalMs = intervalMs;
        return this;
    }

    /// <summary>
    /// Configure the JSON command timeout
    /// </summary>
    /// <param name="timeoutMs">Timeout in milliseconds (1000-30000)</param>
    /// <returns>Builder instance for method chaining</returns>
    public SoyalProxyBuilder WithJsonCommandTimeout(int timeoutMs)
    {
        if (timeoutMs < 1000 || timeoutMs > 30000)
            throw new ArgumentOutOfRangeException(nameof(timeoutMs), 
                "JSON command timeout must be between 1000 and 30000 milliseconds");

        _configuration.JsonCommandTimeoutMs = timeoutMs;
        return this;
    }

    /// <summary>
    /// Add an area to monitor
    /// </summary>
    /// <param name="areaId">Area identifier (0-255)</param>
    /// <param name="startNode">Starting node ID (1-255)</param>
    /// <param name="endNode">Ending node ID (1-255)</param>
    /// <param name="readerType">Door card reader type</param>
    /// <returns>Builder instance for method chaining</returns>
    public SoyalProxyBuilder WithArea(byte areaId, byte startNode = 1, byte endNode = 255, 
        DoorCardReaderType readerType = DoorCardReaderType.Enterprise)
    {
        if (startNode < 1 || startNode > 255)
            throw new ArgumentOutOfRangeException(nameof(startNode), 
                "Start node must be between 1 and 255");

        if (endNode < 1 || endNode > 255)
            throw new ArgumentOutOfRangeException(nameof(endNode), 
                "End node must be between 1 and 255");

        if (startNode > endNode)
            throw new ArgumentException("Start node cannot be greater than end node");

        var areaConfig = new AreaConfiguration
        {
            Id = areaId,
            StartNode = startNode,
            EndNode = endNode,
            ReaderType = readerType
        };

        _configuration.Areas.Add(areaConfig);
        return this;
    }

    /// <summary>
    /// Add multiple areas to monitor
    /// </summary>
    /// <param name="areas">Collection of area configurations</param>
    /// <returns>Builder instance for method chaining</returns>
    public SoyalProxyBuilder WithAreas(IEnumerable<AreaConfiguration> areas)
    {
        if (areas == null)
            throw new ArgumentNullException(nameof(areas));

        foreach (var area in areas)
        {
            if (area.StartNode < 1 || area.StartNode > 255)
                throw new ArgumentOutOfRangeException(nameof(areas), 
                    $"Start node for area {area.Id} must be between 1 and 255");

            if (area.EndNode < 1 || area.EndNode > 255)
                throw new ArgumentOutOfRangeException(nameof(areas), 
                    $"End node for area {area.Id} must be between 1 and 255");

            if (area.StartNode > area.EndNode)
                throw new ArgumentException($"Start node cannot be greater than end node for area {area.Id}");
        }

        _configuration.Areas.AddRange(areas);
        return this;
    }

    /// <summary>
    /// Build the SoyalProxy service instance
    /// </summary>
    /// <returns>Configured SoyalProxy service instance</returns>
    public SoyalProxyService Build()
    {
        if (_logger == null)
            throw new InvalidOperationException("Logger must be set using WithLogger method");

        if (_modbusTcpMasterFactory == null)
            throw new InvalidOperationException("ModbusTcp master factory must be set using WithModbusTcpMasterFactory method");

        if (_configuration.Areas.Count == 0)
            throw new InvalidOperationException("At least one area must be configured using WithArea method");

        return new SoyalProxyService(_logger, _modbusTcpMasterFactory, _configuration);
    }

    /// <summary>
    /// Complete the builder configuration and return the built service
    /// </summary>
    /// <returns>Configured SoyalProxy service instance</returns>
    public SoyalProxyService Complete() => Build();
}

/// <summary>
/// Extension methods for SoyalProxy builder
/// </summary>
public static class SoyalProxyBuilderExtensions
{
    /// <summary>
    /// Create a new SoyalProxy builder instance
    /// </summary>
    /// <returns>New builder instance</returns>
    public static SoyalProxyBuilder CreateSoyalProxy()
    {
        return new SoyalProxyBuilder();
    }
}