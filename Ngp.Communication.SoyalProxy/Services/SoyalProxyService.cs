using Microsoft.Extensions.Logging;
using Ngp.Communication.SoyalProxy.Models;
using Ngp.Shared.Models;
using Ngp.Shared.Factories;
using System.Collections.Concurrent;
using System.Net.Sockets;
using System.Text;
using System.Text.Json;
using SharedConnectionState = Ngp.Shared.Enums.ConnectionState;

namespace Ngp.Communication.SoyalProxy.Services;

/// <summary>
/// Service for Soyal access control proxy communication
/// </summary>
public class SoyalProxyService : IDisposable
{
    private readonly ILogger<SoyalProxyService> _logger;
    private readonly IModbusTcpMasterFactory _modbusTcpMasterFactory;
    private readonly SoyalConfiguration _configuration;
    private readonly ConcurrentDictionary<string, DoorNode> _doorNodes = new();
    private readonly SemaphoreSlim _jsonCommandSemaphore = new(1, 1);
    private readonly CancellationTokenSource _cancellationTokenSource = new();
    private readonly Timer _pollingTimer;
    private IModbusTcpMaster? _modbusTcpMaster;
    
    private SharedConnectionState _connectionState = SharedConnectionState.Disconnected;
    private bool _disposed;

    /// <summary>
    /// Event raised when connection state changes
    /// </summary>
    public event EventHandler<ConnectionStateChangedEventArgs>? ConnectionStateChanged;
    
    /// <summary>
    /// Event raised when door status changes
    /// </summary>
    public event EventHandler<DoorStatusChangedEventArgs>? DoorStatusChanged;

    /// <summary>
    /// Initializes a new instance of SoyalProxyService
    /// </summary>
    /// <param name="logger">Logger instance</param>
    /// <param name="modbusTcpMasterFactory">Factory for creating ModbusTcp masters</param>
    /// <param name="configuration">Service configuration</param>
    public SoyalProxyService(
        ILogger<SoyalProxyService> logger, 
        IModbusTcpMasterFactory modbusTcpMasterFactory,
        SoyalConfiguration configuration)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _modbusTcpMasterFactory = modbusTcpMasterFactory ?? throw new ArgumentNullException(nameof(modbusTcpMasterFactory));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        
        // Initialize door nodes from configuration
        InitializeDoorNodes();
        
        // Create polling timer but don't start it yet
        _pollingTimer = new Timer(async _ => await PollDoorStatusesAsync(), null, 
            Timeout.Infinite, Timeout.Infinite);
    }

    /// <summary>
    /// Start the service
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting Soyal proxy service");
            
            SetConnectionState(SharedConnectionState.Connecting);
            
            // Create ModbusTcp master using factory
            _modbusTcpMaster = _modbusTcpMasterFactory.CreateSoyalModbusTcpMaster(
                "SoyalDevice", 
                _configuration.ModbusTcpHost, 
                _configuration.ModbusTcpPort, 
                Microsoft.Extensions.Logging.LoggerFactory.Create(builder => builder.AddConsole()));
            
            // Subscribe to events
            _modbusTcpMaster.ConnectionStateChanged += OnModbusTcpConnectionStateChanged;
            _modbusTcpMaster.RegisterValueChanged += OnModbusTcpRegisterValueChanged;
            
            // Start the master
            await _modbusTcpMaster.StartAsync(cancellationToken);
            
            // Start polling timer
            _pollingTimer.Change(TimeSpan.Zero, TimeSpan.FromMilliseconds(_configuration.PollingIntervalMs));
            
            SetConnectionState(SharedConnectionState.Connected);
            
            _logger.LogInformation("Soyal proxy service started successfully with {DoorCount} doors", 
                _doorNodes.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start Soyal proxy service");
            SetConnectionState(SharedConnectionState.Error, ex.Message);
            throw;
        }
    }

    /// <summary>
    /// Stop the service
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Stopping Soyal proxy service");
            
            SetConnectionState(SharedConnectionState.Disconnecting);
            
            // Stop polling timer
            _pollingTimer.Change(Timeout.Infinite, Timeout.Infinite);
            
            // Stop ModbusTcp master
            if (_modbusTcpMaster != null)
            {
                await _modbusTcpMaster.StopAsync(cancellationToken);
                _modbusTcpMaster.Dispose();
                _modbusTcpMaster = null;
            }
            
            // Cancel all operations
            _cancellationTokenSource.Cancel();
            
            SetConnectionState(SharedConnectionState.Disconnected);
            
            _logger.LogInformation("Soyal proxy service stopped successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping Soyal proxy service");
            SetConnectionState(SharedConnectionState.Error, ex.Message);
            throw;
        }
    }

    /// <summary>
    /// Execute access control command
    /// </summary>
    /// <param name="command">Command to execute</param>
    /// <param name="cancellationToken">Cancellation token</param>
    public async Task ExecuteCommandAsync(AccessControlCommand command, CancellationToken cancellationToken = default)
    {
        if (command == null)
            throw new ArgumentNullException(nameof(command));

        var nodeKey = $"{command.Area:D3}-{command.NodeId:D3}";
        
        if (!_doorNodes.TryGetValue(nodeKey, out var doorNode))
        {
            _logger.LogWarning("Door node {NodeKey} not found for command execution", nodeKey);
            return;
        }

        try
        {
            switch (command.CommandType)
            {
                case AccessControlCommandType.Unlock:
                    await SendJsonCommandAsync(command.Area, command.NodeId, JsonCommandType.TurnRelayOn, cancellationToken);
                    _logger.LogInformation("Unlock command sent to door {NodeKey}", nodeKey);
                    break;
                    
                case AccessControlCommandType.Lock:
                    await SendJsonCommandAsync(command.Area, command.NodeId, JsonCommandType.TurnRelayOff, cancellationToken);
                    _logger.LogInformation("Lock command sent to door {NodeKey}", nodeKey);
                    break;
                    
                case AccessControlCommandType.QueryStatus:
                    await SendJsonCommandAsync(command.Area, command.NodeId, JsonCommandType.QueryStatus, cancellationToken);
                    _logger.LogDebug("Query status command sent to door {NodeKey}", nodeKey);
                    break;
                    
                default:
                    throw new NotSupportedException($"Command type {command.CommandType} is not supported");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to execute command {CommandType} for door {NodeKey}", 
                command.CommandType, nodeKey);
            throw;
        }
    }

    /// <summary>
    /// Get current door status
    /// </summary>
    /// <param name="area">Area identifier</param>
    /// <param name="nodeId">Node identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    public Task<DoorStatus?> GetDoorStatusAsync(byte area, byte nodeId, CancellationToken cancellationToken = default)
    {
        var nodeKey = $"{area:D3}-{nodeId:D3}";
        
        if (_doorNodes.TryGetValue(nodeKey, out var doorNode))
        {
            var status = new DoorStatus
            {
                Area = doorNode.Area,
                NodeId = doorNode.NodeId,
                IsDoorOpen = doorNode.IsDoorOpen,
                IsLocked = doorNode.IsLocked,
                LastUpdated = doorNode.LastUpdated
            };
            
            return Task.FromResult<DoorStatus?>(status);
        }
        
        return Task.FromResult<DoorStatus?>(null);
    }

    /// <summary>
    /// Get all monitored door statuses
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    public Task<IEnumerable<DoorStatus>> GetAllDoorStatusesAsync(CancellationToken cancellationToken = default)
    {
        var statuses = _doorNodes.Values.Select(node => new DoorStatus
        {
            Area = node.Area,
            NodeId = node.NodeId,
            IsDoorOpen = node.IsDoorOpen,
            IsLocked = node.IsLocked,
            LastUpdated = node.LastUpdated
        });
        
        return Task.FromResult(statuses);
    }

    /// <summary>
    /// Calculate ModbusTcp address for the given area and node
    /// </summary>
    /// <param name="area">Area identifier</param>
    /// <param name="nodeId">Node identifier</param>
    /// <returns>Calculated ModbusTcp address</returns>
    private static ushort CalculateModbusAddress(byte area, byte nodeId)
    {
        // Enterprise: Area * 4096 + Node * 256
        return (ushort)(area * 4096 + nodeId * 256);
    }

    /// <summary>
    /// Initialize door nodes from configuration
    /// </summary>
    private void InitializeDoorNodes()
    {
        foreach (var areaConfig in _configuration.Areas)
        {
            for (byte nodeId = areaConfig.StartNode; nodeId <= areaConfig.EndNode; nodeId++)
            {
                var doorNode = new DoorNode
                {
                    Area = areaConfig.Id,
                    NodeId = nodeId,
                    ReaderType = areaConfig.ReaderType
                };
                
                _doorNodes.TryAdd(doorNode.UniqueId, doorNode);
            }
        }
        
        _logger.LogInformation("Initialized {DoorCount} door nodes from configuration", _doorNodes.Count);
    }

    /// <summary>
    /// Poll door statuses from ModbusTcp
    /// </summary>
    private async Task PollDoorStatusesAsync()
    {
        if (_connectionState != SharedConnectionState.Connected)
            return;

        var tasks = new List<Task>();
        
        foreach (var doorNode in _doorNodes.Values)
        {
            tasks.Add(PollSingleDoorAsync(doorNode));
            
            // Limit concurrent polling to prevent overwhelming the system
            if (tasks.Count >= _configuration.EngineCount)
            {
                await Task.WhenAll(tasks);
                tasks.Clear();
            }
        }
        
        if (tasks.Count > 0)
        {
            await Task.WhenAll(tasks);
        }
    }

    /// <summary>
    /// Poll a single door node
    /// </summary>
    /// <param name="doorNode">Door node to poll</param>
    private Task PollSingleDoorAsync(DoorNode doorNode)
    {
        try
        {
            if (_modbusTcpMaster == null)
                return Task.CompletedTask;
                
            var address = CalculateModbusAddress(doorNode.Area, doorNode.NodeId);
            
            // Get current values from the master
            var doorValue = _modbusTcpMaster.GetCurrentValue(1, address, Shared.Factories.ModbusFunction.ReadDiscreteInputs);
            var lockValue = _modbusTcpMaster.GetCurrentValue(1, address, Shared.Factories.ModbusFunction.ReadCoils);
            
            var isDoorOpen = doorValue != null && doorValue.Length > 0 && doorValue[0] != 0;
            var isLocked = lockValue != null && lockValue.Length > 0 && lockValue[0] != 0;
            
            // Update door node status
            var previousDoorOpen = doorNode.IsDoorOpen;
            var previousLocked = doorNode.IsLocked;
            
            doorNode.IsDoorOpen = isDoorOpen;
            doorNode.IsLocked = isLocked;
            doorNode.LastUpdated = DateTime.UtcNow;
            
            // Raise event if status changed
            if (previousDoorOpen != doorNode.IsDoorOpen || previousLocked != doorNode.IsLocked)
            {
                var previousStatus = new DoorStatus
                {
                    Area = doorNode.Area,
                    NodeId = doorNode.NodeId,
                    IsDoorOpen = previousDoorOpen,
                    IsLocked = previousLocked,
                    LastUpdated = doorNode.LastUpdated
                };
                
                var currentStatus = new DoorStatus
                {
                    Area = doorNode.Area,
                    NodeId = doorNode.NodeId,
                    IsDoorOpen = doorNode.IsDoorOpen,
                    IsLocked = doorNode.IsLocked,
                    LastUpdated = doorNode.LastUpdated
                };
                
                OnDoorStatusChanged(new DoorStatusChangedEventArgs
                {
                    PreviousStatus = previousStatus,
                    CurrentStatus = currentStatus
                });
                
                _logger.LogDebug("Door status changed for {NodeKey}: Door={DoorOpen}, Lock={Locked}", 
                    doorNode.UniqueId, doorNode.IsDoorOpen, doorNode.IsLocked);
            }
            
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to poll door {NodeKey}", doorNode.UniqueId);
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// Send JSON command to the Soyal system
    /// </summary>
    /// <param name="area">Area identifier</param>
    /// <param name="nodeId">Node identifier</param>
    /// <param name="commandType">Command type</param>
    /// <param name="cancellationToken">Cancellation token</param>
    private async Task SendJsonCommandAsync(byte area, byte nodeId, JsonCommandType commandType, 
        CancellationToken cancellationToken)
    {
        await _jsonCommandSemaphore.WaitAsync(cancellationToken);
        
        try
        {
            var command = new JsonCommand
            {
                Area = area,
                Node = nodeId,
                CommandType = commandType
            };
            
            var request = new JsonCommandRequest
            {
                Commands = new List<JsonCommand> { command }
            };
            
            var jsonContent = JsonSerializer.Serialize(request);
            var contentBytes = Encoding.UTF8.GetBytes(jsonContent);
            
            using var client = new TcpClient();
            using var timeoutCts = new CancellationTokenSource(_configuration.JsonCommandTimeoutMs);
            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(
                cancellationToken, timeoutCts.Token);
            
            await client.ConnectAsync(_configuration.ModbusTcpHost, _configuration.JsonServerPort, 
                combinedCts.Token);
            
            var stream = client.GetStream();
            await stream.WriteAsync(contentBytes, combinedCts.Token);
            await stream.FlushAsync(combinedCts.Token);
            
            _logger.LogDebug("JSON command sent: {Command} to {Area}-{Node}", 
                commandType, area, nodeId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send JSON command {CommandType} to {Area}-{NodeId}", 
                commandType, area, nodeId);
            throw;
        }
        finally
        {
            _jsonCommandSemaphore.Release();
        }
    }

    /// <summary>
    /// Handle ModbusTcp connection state changes
    /// </summary>
    private void OnModbusTcpConnectionStateChanged(object? sender, 
        ConnectionStateChangedEventArgs e)
    {
        _logger.LogInformation("ModbusTcp connection state changed: {EndpointId} - {PreviousState} -> {CurrentState}", 
            e.EndpointId, e.PreviousState, e.CurrentState);
        
        // Update our connection state based on ModbusTcp state
        if (e.CurrentState == SharedConnectionState.Connected && _connectionState == SharedConnectionState.Connecting)
        {
            SetConnectionState(SharedConnectionState.Connected);
        }
        else if (e.CurrentState == SharedConnectionState.Error || e.CurrentState == SharedConnectionState.Disconnected)
        {
            SetConnectionState(e.CurrentState, e.ErrorMessage);
        }
    }

    /// <summary>
    /// Handle ModbusTcp register value changes
    /// </summary>
    private void OnModbusTcpRegisterValueChanged(object? sender, 
        ModbusRegisterValueChangedEventArgs e)
    {
        // Find the door node that corresponds to this register change
        var doorNode = _doorNodes.Values.FirstOrDefault(node => 
        {
            var expectedAddress = CalculateModbusAddress(node.Area, node.NodeId);
            return e.SlaveId == 1 && e.Address == expectedAddress;
        });
        
        if (doorNode != null)
        {
            var previousStatus = new DoorStatus
            {
                Area = doorNode.Area,
                NodeId = doorNode.NodeId,
                IsDoorOpen = doorNode.IsDoorOpen,
                IsLocked = doorNode.IsLocked,
                LastUpdated = doorNode.LastUpdated
            };
            
            // Update the appropriate status based on register type
            if (e.RegisterType == ModbusRegisterType.DiscreteInput)
            {
                doorNode.IsDoorOpen = e.CurrentValue;
            }
            else if (e.RegisterType == ModbusRegisterType.Coil)
            {
                doorNode.IsLocked = e.CurrentValue;
            }
            
            doorNode.LastUpdated = DateTime.UtcNow;
            
            var currentStatus = new DoorStatus
            {
                Area = doorNode.Area,
                NodeId = doorNode.NodeId,
                IsDoorOpen = doorNode.IsDoorOpen,
                IsLocked = doorNode.IsLocked,
                LastUpdated = doorNode.LastUpdated
            };
            
            OnDoorStatusChanged(new DoorStatusChangedEventArgs
            {
                PreviousStatus = previousStatus,
                CurrentStatus = currentStatus
            });
            
            _logger.LogInformation("Door status changed - Area: {Area}, Node: {NodeId}, Door: {Door}, Lock: {Lock}",
                doorNode.Area, doorNode.NodeId, doorNode.IsDoorOpen, doorNode.IsLocked);
        }
    }

    /// <summary>
    /// Set connection state and raise event if changed
    /// </summary>
    /// <param name="newState">New connection state</param>
    /// <param name="errorMessage">Optional error message</param>
    private void SetConnectionState(SharedConnectionState newState, string? errorMessage = null)
    {
        var previousState = _connectionState;
        _connectionState = newState;
        
        if (previousState != newState)
        {
            OnConnectionStateChanged(new ConnectionStateChangedEventArgs
            {
                EndpointId = "SoyalProxy",
                PreviousState = previousState,
                CurrentState = newState,
                ErrorMessage = errorMessage
            });
        }
    }

    /// <summary>
    /// Raise connection state changed event
    /// </summary>
    /// <param name="e">Event arguments</param>
    protected virtual void OnConnectionStateChanged(ConnectionStateChangedEventArgs e)
    {
        ConnectionStateChanged?.Invoke(this, e);
    }

    /// <summary>
    /// Raise door status changed event
    /// </summary>
    /// <param name="e">Event arguments</param>
    protected virtual void OnDoorStatusChanged(DoorStatusChangedEventArgs e)
    {
        DoorStatusChanged?.Invoke(this, e);
    }

    /// <summary>
    /// Dispose resources
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _pollingTimer?.Dispose();
            _jsonCommandSemaphore?.Dispose();
            _cancellationTokenSource?.Dispose();
            
            // Unsubscribe from events and dispose ModbusTcp master
            if (_modbusTcpMaster != null)
            {
                _modbusTcpMaster.ConnectionStateChanged -= OnModbusTcpConnectionStateChanged;
                _modbusTcpMaster.RegisterValueChanged -= OnModbusTcpRegisterValueChanged;
                _modbusTcpMaster.Dispose();
                _modbusTcpMaster = null;
            }
            
            _disposed = true;
        }
    }
}