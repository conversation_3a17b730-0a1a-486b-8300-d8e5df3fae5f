using System.Text.Json.Serialization;

namespace Ngp.Communication.SoyalProxy.Models;

/// <summary>
/// JSON command request structure
/// </summary>
public class JsonCommandRequest
{
    /// <summary>
    /// Login user identifier
    /// </summary>
    [JsonPropertyName("l_user")]
    public string User { get; set; } = "login user";
    
    /// <summary>
    /// Array of commands to execute
    /// </summary>
    [JsonPropertyName("cmd_array")]
    public List<JsonCommand> Commands { get; set; } = new();
}

/// <summary>
/// Individual JSON command
/// </summary>
public class JsonCommand
{
    /// <summary>
    /// Command prefix (always 2000)
    /// </summary>
    [JsonPropertyName("c_cmd")]
    public int CommandPrefix { get; set; } = 2000;
    
    /// <summary>
    /// Area identifier
    /// </summary>
    [JsonPropertyName("Area")]
    public int Area { get; set; }
    
    /// <summary>
    /// Node identifier
    /// </summary>
    [JsonPropertyName("Node")]
    public int Node { get; set; }
    
    /// <summary>
    /// Command type (ignored in serialization)
    /// </summary>
    [JsonIgnore]
    public JsonCommandType CommandType { get; set; } = JsonCommandType.QueryStatus;
    
    /// <summary>
    /// Hex command string based on command type
    /// </summary>
    [JsonPropertyName("Hex")]
    public string Hex => CommandType switch
    {
        JsonCommandType.QueryStatus => "0x2100",
        JsonCommandType.TurnRelayOn => "0x2184",
        JsonCommandType.TurnRelayOff => "0x2183",
        _ => throw new NotSupportedException($"Command type {CommandType} is not supported")
    };
}

/// <summary>
/// Types of JSON commands
/// </summary>
public enum JsonCommandType
{
    /// <summary>
    /// Query current status
    /// </summary>
    QueryStatus,
    
    /// <summary>
    /// Turn relay on (unlock)
    /// </summary>
    TurnRelayOn,
    
    /// <summary>
    /// Turn relay off (lock)
    /// </summary>
    TurnRelayOff
}