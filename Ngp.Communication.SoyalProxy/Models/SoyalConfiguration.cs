namespace Ngp.Communication.SoyalProxy.Models;

/// <summary>
/// Configuration for Soyal proxy service
/// </summary>
public class SoyalConfiguration
{
    /// <summary>
    /// ModbusTcp server IP address
    /// </summary>
    public string ModbusTcpHost { get; set; } = "***************";
    
    /// <summary>
    /// ModbusTcp server port
    /// </summary>
    public ushort ModbusTcpPort { get; set; } = 502;
    
    /// <summary>
    /// JSON command server port
    /// </summary>
    public ushort JsonServerPort { get; set; } = 1631;
    
    /// <summary>
    /// Number of parallel polling engines
    /// </summary>
    public int EngineCount { get; set; } = 10;
    
    /// <summary>
    /// Polling interval in milliseconds
    /// </summary>
    public int PollingIntervalMs { get; set; } = 1000;
    
    /// <summary>
    /// JSON command timeout in milliseconds
    /// </summary>
    public int JsonCommandTimeoutMs { get; set; } = 5000;
    
    /// <summary>
    /// Areas to monitor
    /// </summary>
    public List<AreaConfiguration> Areas { get; set; } = new();
}

/// <summary>
/// Configuration for an area
/// </summary>
public class AreaConfiguration
{
    /// <summary>
    /// Area identifier (0-255)
    /// </summary>
    public byte Id { get; set; }
    
    /// <summary>
    /// Starting node ID
    /// </summary>
    public byte StartNode { get; set; } = 1;
    
    /// <summary>
    /// Ending node ID
    /// </summary>
    public byte EndNode { get; set; } = 255;
    
    /// <summary>
    /// Door card reader type
    /// </summary>
    public DoorCardReaderType ReaderType { get; set; } = DoorCardReaderType.Enterprise;
}

/// <summary>
/// Types of door card readers
/// </summary>
public enum DoorCardReaderType
{
    /// <summary>
    /// Home type reader
    /// </summary>
    Home,
    
    /// <summary>
    /// Enterprise type reader
    /// </summary>
    Enterprise
}