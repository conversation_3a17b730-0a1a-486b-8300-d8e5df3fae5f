namespace Ngp.Communication.SoyalProxy.Models;

/// <summary>
/// Represents a door node in the access control system
/// </summary>
public class DoorNode
{
    /// <summary>
    /// Area identifier (0-255)
    /// </summary>
    public byte Area { get; set; }
    
    /// <summary>
    /// Node identifier (1-254)
    /// </summary>
    public byte NodeId { get; set; }
    
    /// <summary>
    /// Door card reader type
    /// </summary>
    public DoorCardReaderType ReaderType { get; set; } = DoorCardReaderType.Enterprise;
    
    /// <summary>
    /// Current door position status (true = open, false = closed)
    /// </summary>
    public bool IsDoorOpen { get; set; }
    
    /// <summary>
    /// Current lock status (true = locked, false = unlocked)
    /// </summary>
    public bool IsLocked { get; set; }
    
    /// <summary>
    /// Last time status was updated
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// Calculated Modbus start address for this door node
    /// Enterprise type: Area * 4096 + 256
    /// Home type: Area * 4096
    /// </summary>
    public ushort ModbusStartAddress => ReaderType == DoorCardReaderType.Enterprise 
        ? (ushort)(Area * 4096 + 256) 
        : (ushort)(Area * 4096);
    
    /// <summary>
    /// Unique identifier for this door node
    /// </summary>
    public string UniqueId => $"{Area:D3}-{NodeId:D3}";
    
    /// <summary>
    /// Display name for this door node
    /// </summary>
    public string DisplayName => $"Area {Area}, Node {NodeId} ({ReaderType})";
}